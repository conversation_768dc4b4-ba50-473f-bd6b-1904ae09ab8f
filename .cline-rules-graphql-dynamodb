# GraphQL and DynamoDB Rules for TAEX Project

## GraphQL Schema Design

### Type Definitions
- Use clear, descriptive names for types, fields, and operations
- Use camelCase for field names
- Use PascalCase for type names
- Use input types for mutation arguments
- Use enums for fixed sets of values

### Query Design
- Use Query type for read operations
- Use descriptive names for query fields
- Use arguments for filtering, sorting, and pagination
- Use connections for paginated lists
- Use nullable fields when appropriate

### Mutation Design
- Use Mutation type for write operations
- Use descriptive names for mutation fields
- Use input types for complex arguments
- Return the modified object from mutations
- Use consistent naming: add*, update*, delete*

### Field Resolvers
- Use field resolvers for related data
- Use batching for efficient data loading
- Use caching for frequently accessed data
- Use pagination for lists
- Use filtering and sorting for lists

## DynamoDB Data Modeling

### Single-Table Design
- Use a single DynamoDB table for all entity types
- Use composite keys (partition key + sort key) for flexible access patterns
- Use GSIs (Global Secondary Indexes) for alternative access patterns
- Use consistent naming for partition and sort keys
- Use prefixes for entity types in keys (e.g., `PROJECT#`, `ACTIVITY#`)

### Key Structure
- Partition Key (PK): Use entity type prefix + ID
  - Example: `PROJECT#123`, `ACTIVITY#456`
- Sort Key (SK): Use entity type for base items, relationship info for related items
  - Example: `PROJECT`, `BELONGS_TO#PROJECT#123`
- Use GSIs for alternative access patterns
  - Example: GSI1PK: `CONTRACTOR#789`, GSI1SK: `ACTIVITY#456`

### Entity Patterns

#### Project Entity
- PK: `PROJECT#[projectId]`
- SK: `PROJECT`
- Attributes:
  - name
  - displayName
  - description
  - status
  - startDate
  - finishDate
  - allowStatusReview
  - activityCount
  - managerGroups (references to Group entities)
  - operatorGroups (references to Group entities)
  - workerGroups (references to Group entities)

#### Activity Entity
- PK: `ACTIVITY#[activityId]`
- SK: `ACTIVITY`
- GSI1PK: `PROJECT#[projectId]`
- GSI1SK: `ACTIVITY#[activityId]`
- GSI2PK: `WORKPACKAGE#[workPackageId]` (if applicable)
- GSI2SK: `ACTIVITY#[activityId]`
- Attributes:
  - name
  - description
  - status
  - startDate
  - finishDate
  - percentComplete
  - workPackageId (reference to WorkPackage entity)
  - projectId (reference to Project entity)
  - unableToWork
  - discipline
  - contractor (reference to Contractor entity)

#### WorkPackage Entity
- PK: `WORKPACKAGE#[workPackageId]`
- SK: `WORKPACKAGE`
- GSI1PK: `PROJECT#[projectId]`
- GSI1SK: `WORKPACKAGE#[workPackageId]`
- Attributes:
  - name
  - description
  - stopPointNo
  - stopPointDescription
  - sequenceNo
  - status
  - projectId (reference to Project entity)

#### Group Entity
- PK: `GROUP#[groupId]`
- SK: `GROUP`
- Attributes:
  - name
  - groupType (MANAGER, OPERATOR, WORKER)
  - description

#### User Entity
- PK: `USER#[username]`
- SK: `USER`
- Attributes:
  - username
  - email
  - disciplines
  - equipments

#### Contractor Entity
- PK: `CONTRACTOR#[contractorId]`
- SK: `CONTRACTOR`
- Attributes:
  - contractorNumber
  - name
  - description

### Relationship Patterns

#### Project-WorkPackage Relationship
- PK: `PROJECT#[projectId]`
- SK: `WORKPACKAGE#[workPackageId]`
- Attributes:
  - projectId
  - workPackageId

#### WorkPackage-Activity Relationship
- PK: `WORKPACKAGE#[workPackageId]`
- SK: `ACTIVITY#[activityId]`
- Attributes:
  - workPackageId
  - activityId

#### User-Group Relationship
- PK: `USER#[username]`
- SK: `GROUP#[groupId]`
- Attributes:
  - username
  - groupId
  - groupType

#### User-Contractor Relationship
- PK: `USER#[username]`
- SK: `CONTRACTOR#[contractorId]`
- Attributes:
  - username
  - contractorId

#### Activity-Predecessor Relationship
- PK: `ACTIVITY#[activityId]`
- SK: `PREDECESSOR#[predecessorActivityId]`
- Attributes:
  - activityId
  - predecessorActivityId

#### Activity-Successor Relationship
- PK: `ACTIVITY#[activityId]`
- SK: `SUCCESSOR#[successorActivityId]`
- Attributes:
  - activityId
  - successorActivityId

### Query Patterns

#### Get Item by ID
- Use GetItem operation with PK and SK
- Example: Get Project by ID
  - PK: `PROJECT#123`
  - SK: `PROJECT`

#### Query Items by Parent
- Use Query operation with PK and SK begins_with
- Example: Get all WorkPackages for a Project
  - PK: `PROJECT#123`
  - SK begins_with: `WORKPACKAGE#`

#### Query Items by GSI
- Use Query operation on GSI with GSI1PK and GSI1SK
- Example: Get all Activities for a Project
  - GSI1PK: `PROJECT#123`
  - GSI1SK begins_with: `ACTIVITY#`

#### Query Items by Type
- Use Query operation on type-index with type attribute
- Example: Get all Projects
  - type: `PROJECT`

### Batch Operations

#### BatchGetItem
- Use BatchGetItem for retrieving multiple items by key
- Limit to 100 items per batch
- Handle unprocessed items

#### BatchWriteItem
- Use BatchWriteItem for writing multiple items
- Limit to 25 items per batch
- Handle unprocessed items

#### TransactWriteItems
- Use TransactWriteItems for atomic operations
- Limit to 25 items per transaction
- Use condition expressions for optimistic locking

## Lambda Resolver Implementation

### Resolver Structure
- Use a single Lambda function for all resolvers
- Use a resolver map to route requests to handler functions
- Use a consistent pattern for handler functions
- Use a consistent error handling pattern

### Handler Functions
- Use async/await for asynchronous operations
- Use try/catch blocks for error handling
- Use consistent parameter validation
- Use consistent response formatting

### Data Access
- Use the DynamoDB Document Client for data access
- Use consistent error handling
- Use pagination for large result sets
- Use projection expressions to minimize data transfer

### Authorization
- Use Cognito user pools for authentication
- Use JWT tokens for authorization
- Use fine-grained access control
- Use field-level authorization when needed

## Best Practices

### Performance
- Use efficient access patterns
- Use batch operations when possible
- Use caching for frequently accessed data
- Use pagination for large result sets

### Security
- Use least privilege IAM roles
- Use encryption for sensitive data
- Use input validation
- Use output sanitization

### Error Handling
- Use custom error types
- Use descriptive error messages
- Use error logging
- Use consistent error response format

### Testing
- Use unit tests for resolver functions
- Use integration tests for API endpoints
- Use test data generators
- Use test cleanup to remove test data
