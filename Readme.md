# TAEx - Turnaround Execution Management

A comprehensive application for managing turnaround execution processes.

## Project Structure

The project follows a standardized structure:
- `app/` - Angular-based frontend application
- `backend/` - AWS CDK TypeScript application for infrastructure and API
- `schema/` - GraphQL schema definitions

## Technologies

### AWS CDK (Cloud Development Kit)
The infrastructure is defined using AWS CDK in TypeScript, enabling:
- Infrastructure as Code (IaC) with type safety
- Automated resource provisioning and management
- Reusable infrastructure components
- Built-in security best practices
- Integration with AWS CloudFormation

### AWS Serverless Resources
The application leverages various AWS serverless services:
- **AWS AppSync**: Managed GraphQL API service for real-time data synchronization
- **Amazon DynamoDB**: NoSQL database with single-table design
- **AWS Cognito**: User authentication and authorization
- **AWS Lambda**: Serverless compute for business logic
- **Amazon CloudFront**: Content delivery and edge caching
- **Amazon S3**: Object storage for static assets
- **AWS IAM**: Fine-grained access control and security

### Angular Frontend
The frontend is built with Angular, featuring:
- Component-based architecture
- TypeScript for type safety and better developer experience
- Angular Material for consistent UI components
- AWS Amplify integration for AWS service connectivity
- Responsive design principles
- State management for complex data flows

## Architecture Overview

<img src="docs/images/backend.png" alt="Architecture Overview Diagram" width="600"/>

The TAEx application uses a serverless architecture built on AWS services:
- Frontend hosted on CloudFront
- Backend powered by AWS CDK
- GraphQL API through AppSync
- Authentication via Cognito integrated with Azure AD

## Frontend Application

<img src="docs/images/frontend.png" alt="Frontend Sample" width="600"/>

### Available Environments
| Environment | URL | Status |
|------------|-----|--------|
| QA | [https://taex-qa.covestro.net](https://taex-qa.covestro.net) | Active |
| Production | TBD | Coming Soon |

### Access Management
User management is handled through Azure Active Directory (Azure AD):

- **QA Environment**: [Azure AD User Management Portal](https://portal.azure.com/#view/Microsoft_AAD_IAM/ManagedAppMenuBlade/~/Overview/objectId/a61745e1-7525-42de-935a-9541a8550e00/appId/fa46bb65-b0b4-4fd6-99d8-e4c423e27c9d)
- **Production Environment**: Coming Soon

### Documentation
End-user documentation with detailed instructions on system access and usage is currently being developed.

## Integration to P6
<img src="docs/images/p6Integration.svg" alt="Integration" width="600"/>

## Development Guide

### Local Development Setup
For local development, you'll need to set up a local Cognito user since the CloudFront -> Cognito -> Azure flow is not available:

```bash
# Create a local user in Cognito
aws cognito-idp admin-create-user \
    --user-pool-id <user-pool-id> \
    --username <username> \
    --temporary-password <password>
```

### Deployment Process

1. **Prerequisites**
   - Configure access to Covestro service catalogue products
   
2. **Backend Deployment**
   - Run CDK bootstrap
   - Deploy backend infrastructure
   
3. **Frontend Deployment**
   - Build the SPA application
   - Deploy to CloudFront
   - Invalidate CloudFront distribution

## CI/CD Pipeline

The repository uses GitLab CI/CD for automated deployments, configured in `.gitlab-ci.yml`.

### Pipeline Stages
1. **Frontend Deployment**
   - Builds and deploys the Angular application
   - Located in `application/app` directory
   
2. **Backend Deployment**
   - Deploys AWS CDK infrastructure
   - Configures API and services

For more details on the CI/CD setup, see [GitLab Runners Documentation](https://developer.covestro.net/toolchain/cicd/gitlab_runner.html).
