include:
  - project: "development-platform/assets/core-shared-ci-functions"
    ref: v1.0.0 # Git tag
    file: "functions/utils.yml"

# -----------------------------------------------------------#
# VARIABLES                                                  #
# -----------------------------------------------------------#
variables:
  AWS_DEFAULT_REGION: eu-central-1
  GITLAB_RUNNER: cov-shared
  **********************: 381491970997.dkr.ecr.eu-central-1.amazonaws.com/covestro/base:latest
  ROLE_ARN: arn:aws:iam::340752811583:role/CicdStack-TAExCicdIntegrationRunnerRole69E247A0-9Zu1TN1zB7ct # replace with your role arn
  KUBERNETES_CPU_REQUEST: 1 
  KUBERNETES_MEMORY_REQUEST: 2Gi 
  ANGULAR_BUILD_PATH: app/dist/app 
  S3_BUCKET: frontendstack-intranetstaticwebsitebucketc0729b19-qenc12zjylaq # replace with your S3 bucket name
  CLOUDFRONT_DISTRIBUTION_ID: E1IRV9FWFBS2R5 # replace with your CloudFront distribution ID
  CDK_BACKEND_FOLDER: backend # Define the backend folder
  CDK_DEPLOY_STACKS: 'TAExGraphQlApiStack MonitoringStack CicdStack WorkflowStack P6IntegrationStack' # Replace with specific stack names if necessary

# -----------------------------------------------------------#
# PIPELINE CONFIGURATION                                     #
# -----------------------------------------------------------#
default:
  image: $**********************
  tags:
    - $GITLAB_RUNNER

stages:
  - build
  - deploy
  - deploy-backend

# Build Angular application
build:
  stage: build
  rules:
    - if: '$CI_COMMIT_REF_NAME == "master"'
    - when: never # Prevent execution if no rules match
  script:
    - cd app
    - npm ci
    - npm install @angular/cli --save-dev
    - npx ng build --configuration=qa
  artifacts:
    paths:
      - $ANGULAR_BUILD_PATH

# Deploy to S3 and invalidate CloudFront cache
deploy:
  extends: .configure_oidc
  stage: deploy
  rules:
    - if: '$CI_COMMIT_REF_NAME == "master"'
    - when: never # Prevent execution if no rules match
  script:
    - aws s3 sync $ANGULAR_BUILD_PATH s3://$S3_BUCKET --delete
    - |
      aws cloudfront create-invalidation \
        --distribution-id $CLOUDFRONT_DISTRIBUTION_ID \
        --paths "/*"

# Deploy CDK backend
deploy-backend:
  extends: .configure_oidc
  stage: deploy-backend
  rules:
    - if: '$CI_COMMIT_REF_NAME == "master"'
    - when: never # Prevent execution if no rules match
  script:
    - cd $CDK_BACKEND_FOLDER
    - aws codeartifact login --tool npm --repository cov-cdk --domain covestro --domain-owner 618253301100 --region eu-central-1 --namespace @cov-cdk
    - npm ci
    # Install Lambda Layer
    - cd lambda/p6-integration-layer/nodejs
    - npm install --omit=dev
    - cd - || exit
    
    - npx cdk deploy -c stage=qa $CDK_DEPLOY_STACKS --require-approval never
