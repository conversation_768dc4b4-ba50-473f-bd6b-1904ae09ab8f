# P6 Integration Rules for TAEX Project

## Overview
The TAEX application integrates with Primavera P6 for project management data. This file contains specific rules and patterns for working with the P6 integration.

## Integration Architecture

### Lambda Functions
- Each P6 integration operation has its own Lambda function
- Functions are organized in the `/backend/lambda/p6-integration` directory
- Each function has a local runner script for development and testing
- Functions share common utilities from the `utils.js` file

### Authentication
- P6 credentials are stored in AWS Secrets Manager
- Credentials are retrieved at runtime by Lambda functions
- Local development uses AWS profiles for authentication

## Data Flow

### Project Synchronization
1. Projects are fetched from P6 using the `fetchProjects` Lambda
2. Project data is transformed to match the TAEX data model
3. Projects are stored in DynamoDB with a `PROJECT#` prefix
4. Project sync status is tracked in the project record

### Activity Synchronization
1. Activities are fetched from P6 using the `fetchActivities` Lambda
2. Activity data is transformed to match the TAEX data model
3. Activities are stored in DynamoDB with an `ACTIVITY#` prefix
4. Activities are linked to projects and work packages

### Activity Code Synchronization
1. Activity codes are fetched from P6 using the `fetchActivityCodes` Lambda
2. Activity code data is transformed to match the TAEX data model
3. Activity codes are stored in DynamoDB with an `ACTIVITYCODE#` prefix
4. Activity codes are linked to activities

### Relationship Synchronization
1. Relationships are fetched from P6 using the `fetchRelationships` Lambda
2. Relationship data is transformed to match the TAEX data model
3. Relationships are stored in DynamoDB as attributes of activities
4. Predecessor and successor relationships are maintained

## Error Handling

### Retry Logic
- Failed API calls should be retried with exponential backoff
- Maximum retry attempts should be configurable
- Permanent failures should be logged and reported

### Error Reporting
- Errors should be logged with context information
- Critical errors should trigger alerts
- Error details should be stored for debugging

## Local Development

### Environment Setup
- Use local AWS profiles for authentication
- Use environment variables for configuration
- Use local scripts for testing

### Testing
- Use mock data for testing
- Use local DynamoDB for testing
- Use Jest for unit testing
- Use integration tests for end-to-end testing

## Deployment

### CI/CD
- Use GitLab CI/CD for automated deployments
- Use CDK for infrastructure deployment
- Use environment-specific configurations

### Monitoring
- Use CloudWatch for logging and monitoring
- Use X-Ray for tracing
- Use alarms for critical errors
- Use dashboards for operational visibility

## Best Practices

### Code Organization
- Use modular code structure
- Use shared utilities for common operations
- Use consistent error handling
- Use typed interfaces for P6 data

### Performance
- Use batch operations for large datasets
- Use pagination for API calls
- Use efficient data transformations
- Use appropriate Lambda memory and timeout settings

### Security
- Use least privilege IAM roles
- Use encryption for sensitive data
- Use secure API authentication
- Use audit logging for sensitive operations
