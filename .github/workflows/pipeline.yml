name: Build and Deploy to AWS

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]
  workflow_dispatch:

permissions:
  id-token: write
  contents: read

env:
  AWS_REGION: eu-central-1
  AWS_ROLE_ARN: arn:aws:iam::340752811583:role/github-cicd-role
  S3_BUCKET: frontendstack-intranetstaticwebsitebucketc0729b19-qenc12zjylaq
  CLOUDFRONT_DISTRIBUTION_ID: E1IRV9FWFBS2R5
  CDK_BACKEND_FOLDER: backend
  CDK_DEPLOY_STACKS: TAExGraphQlApiStack MonitoringStack CicdStack WorkflowStack P6IntegrationStack
  ANGULAR_BUILD_PATH: app/dist/app

jobs:
  build_frontend:
    name: Build Angular Application
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: app/package-lock.json
      - name: Install and Build
        run: |
          cd app
          npm ci
          npm install @angular/cli --save-dev
          npx ng build --configuration=qa
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: angular-build
          path: ${{ env.ANGULAR_BUILD_PATH }}

  deploy_frontend:
    name: Deploy to S3 and CloudFront
    runs-on: ubuntu-latest
    needs: build_frontend
    if: github.event_name == 'push' && github.ref == 'refs/heads/master'
    steps:
      - uses: actions/download-artifact@v4
        with:
          name: angular-build
          path: ${{ env.ANGULAR_BUILD_PATH }}
      - uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.AWS_ROLE_ARN }}
          aws-region: ${{ env.AWS_REGION }}
      - name: Sync to S3
        run: aws s3 sync ${{ env.ANGULAR_BUILD_PATH }} s3://${{ env.S3_BUCKET }} --delete
      - name: Invalidate CloudFront
        run: |
          aws cloudfront create-invalidation \
            --distribution-id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} \
            --paths "/*"

  deploy_backend:
    name: Deploy CDK Backend
    runs-on: ubuntu-latest
    needs: build_frontend
    if: github.event_name == 'push' && github.ref == 'refs/heads/master'
    steps:
      - uses: actions/checkout@v4
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json
      - uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.AWS_ROLE_ARN }}
          aws-region: ${{ env.AWS_REGION }}
      - name: Login to CodeArtifact
        run: |
          aws codeartifact login --tool npm --repository cov-cdk \
            --domain covestro --domain-owner 618253301100 \
            --region ${{ env.AWS_REGION }} \
            --namespace @cov-cdk
      - name: Install dependencies
        run: |
          cd ${{ env.CDK_BACKEND_FOLDER }}
          npm ci
      - name: Install Lambda Layer
        run: |
          cd ${{ env.CDK_BACKEND_FOLDER }}/lambda/p6-integration-layer/nodejs
          npm install --omit=dev
      - name: Deploy with CDK
        run: |
          cd ${{ env.CDK_BACKEND_FOLDER }}
          npx cdk deploy -c stage=qa ${{ env.CDK_DEPLOY_STACKS }} --require-approval never
