# TAEX Project Rules for Cline

## Project Structure

- The project follows a monorepo structure with frontend and backend in separate directories
- `/app` - Angular-based frontend application
- `/backend` - AWS CDK TypeScript application for infrastructure and API
- `/schema` - GraphQL schema definitions
- `/docs` - Project documentation

## Naming Conventions

### General
- Use camelCase for variables, functions, and methods
- Use PascalCase for classes, interfaces, types, and components
- Use kebab-case for file names and directories
- Use UPPER_SNAKE_CASE for constants

### Angular
- Component files should follow the pattern: `[name].component.[type]`
  - Example: `activity-search.component.ts`, `activity-search.component.html`
- Service files should follow the pattern: `[name].service.ts`
  - Example: `activity.service.ts`
- Module files should follow the pattern: `[name].module.ts`
  - Example: `app.module.ts`, `material.module.ts`

### AWS CDK
- Stack files should follow the pattern: `[Name]Stack.ts`
  - Example: `GraphQlApiStack.ts`, `P6IntegrationStack.ts`
- Construct files should follow the pattern: `[Name].ts`
  - Example: `AttachmentHandling.ts`

### GraphQL
- GraphQL files should follow the pattern: `[name].graphql`
  - Example: `activities.graphql`, `projects.graphql`
- Generated GraphQL types should be in `generated.ts`

## Code Style

### TypeScript
- Use strict type checking
- Prefer interfaces over types for object definitions
- Use async/await for asynchronous operations
- Use optional chaining (`?.`) and nullish coalescing (`??`) operators
- Use type guards for type narrowing

### Angular
- Use OnPush change detection strategy for performance
- Use reactive forms over template-driven forms
- Use Angular Material components for UI
- Use Angular services for data access and business logic
- Use Angular modules for feature organization

### AWS CDK
- Use L2 constructs when available
- Use environment variables for configuration
- Use CDK context for environment-specific values
- Follow the principle of least privilege for IAM roles
- Use resource naming conventions that include the app name

## GraphQL Schema and API

### Schema Design
- Use clear, descriptive names for types, fields, and operations
- Use input types for mutation arguments
- Use connections for paginated lists
- Use enums for fixed sets of values
- Include descriptions for types and fields

### API Patterns
- Use Query for read operations
- Use Mutation for write operations
- Use field resolvers for related data
- Use pagination for lists
- Use filtering and sorting for lists

## DynamoDB Patterns

### Table Design
- Use a single-table design with composite keys
- Use GSIs for alternative access patterns
- Use consistent naming for partition and sort keys
- Use prefixes for entity types in keys (e.g., `PROJECT#`, `ACTIVITY#`)
- Use sparse indexes for optional attributes

### Data Access
- Use the DynamoDB Document Client for data access
- Use batch operations for multiple items
- Use transactions for atomic operations
- Use projections to minimize data transfer
- Use consistent read when strong consistency is required

## Angular Component Patterns

### Component Organization
- Use smart/container components for data management
- Use presentational components for UI rendering
- Use component inputs and outputs for data flow
- Use services for shared state and API access
- Use Angular Material for UI components

### State Management
- Use services for shared state
- Use BehaviorSubject for observable state
- Use async pipe for subscribing to observables in templates
- Use OnPush change detection for performance
- Use local component state for UI-specific state

## P6 Integration Patterns

### Integration Architecture
- Use Lambda functions for P6 API integration
- Use SOAP client for P6 API calls
- Use SQS for asynchronous processing
- Use DynamoDB for data storage
- Use event-driven architecture for updates

### Data Synchronization
- Use incremental updates when possible
- Use batch processing for large datasets
- Use error handling and retry logic
- Use logging for debugging
- Use monitoring for performance and errors

## Testing Conventions

### Unit Testing
- Use Jest for unit testing
- Use test doubles (mocks, stubs, spies) for dependencies
- Use descriptive test names
- Use test coverage reports
- Use snapshot testing for UI components

### Integration Testing
- Use AWS SDK for testing AWS resources
- Use GraphQL client for testing GraphQL API
- Use end-to-end testing for critical paths
- Use test data generators
- Use test cleanup to remove test data

## Authorization and Authentication

### User Management
- Use Cognito for user authentication
- Use Azure AD integration for enterprise users
- Use user groups for role-based access control
- Use JWT tokens for API authorization
- Use fine-grained access control for resources

### Permission Model
- Use group-based permissions
- Use three main user roles: Manager, Operator, Worker
- Use project-specific permissions
- Use attribute-based access control
- Use least privilege principle

## Error Handling

### Frontend
- Use error boundaries for React components
- Use error interceptors for HTTP requests
- Use user-friendly error messages
- Use logging for debugging
- Use retry logic for transient errors

### Backend
- Use try/catch blocks for error handling
- Use custom error types
- Use error logging
- Use error monitoring
- Use graceful degradation

## Performance Considerations

### Frontend
- Use lazy loading for routes
- Use virtual scrolling for large lists
- Use memoization for expensive calculations
- Use optimized builds for production
- Use CDN for static assets

### Backend
- Use pagination for large datasets
- Use caching for frequently accessed data
- Use efficient database queries
- Use appropriate Lambda memory and timeout settings
- Use monitoring for performance bottlenecks

## Deployment

### CI/CD
- Use GitLab CI/CD for automated deployments
- Use environment-specific configurations
- Use automated testing in CI pipeline
- Use infrastructure as code for deployments
- Use blue/green deployments for zero downtime

### Environment Management
- Use separate environments for development, QA, and production
- Use environment-specific configurations
- Use feature flags for controlled rollouts
- Use monitoring and alerting
- Use logging for debugging
