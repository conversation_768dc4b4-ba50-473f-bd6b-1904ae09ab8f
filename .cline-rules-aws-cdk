# AWS CDK Infrastructure Rules for TAEX Project

## Project Structure

### Directory Organization
- CDK application is in the `/backend` directory
- Stack definitions are in the `/backend/lib` directory
- Custom constructs are in the `/backend/lib/constructs` directory
- Lambda functions are in the `/backend/lambda` directory
- Tests are in the `/backend/test` directory

### File Organization
- Stack files follow the pattern: `[Name]Stack.ts`
  - Example: `GraphQlApiStack.ts`, `P6IntegrationStack.ts`
- Construct files follow the pattern: `[Name].ts`
  - Example: `AttachmentHandling.ts`
- Lambda function directories are organized by feature
  - Example: `/backend/lambda/graphql-api`, `/backend/lambda/p6-integration`

## Stack Patterns

### Stack Organization
- Each stack represents a logical group of related resources
- Stacks are defined as classes extending `cdk.Stack`
- Stacks receive props via constructor
- Stacks expose important resources as public properties
- Stacks output important resource identifiers

### Stack Dependencies
- Stacks can depend on other stacks
- Dependencies are managed through props
- Resources from one stack can be passed to another
- Cross-stack references are used for resource sharing
- Outputs are used for cross-stack references

## Resource Patterns

### Naming Conventions
- Resources are named with a consistent pattern
- Names include the application name as a prefix
- Names include the resource type
- Names include a unique identifier when needed
- Names use PascalCase for CDK constructs

### AppSync API
- GraphQL API is defined in `GraphQlApiStack.ts`
- Schema is loaded from `/schema/schema.graphql`
- Resolvers are implemented as Lambda functions
- Authorization is configured with Cognito user pools
- API endpoints are exposed as outputs

### DynamoDB
- Tables are defined with partition and sort keys
- GSIs are defined for alternative access patterns
- Billing mode is set to pay-per-request
- Stream is enabled for change data capture
- Table name is exposed as an output

### Lambda Functions
- Functions are organized by feature
- Functions share common layers when appropriate
- Functions use Node.js runtime
- Functions have appropriate IAM permissions
- Functions have appropriate timeout and memory settings

### Cognito
- User pools are defined for authentication
- User pool clients are defined for application access
- Identity pools are defined for AWS service access
- OAuth flows are configured for authentication
- Azure AD integration is configured for enterprise users

### S3
- Buckets are defined for file storage
- Bucket policies are defined for access control
- CORS is configured for browser access
- Lifecycle rules are defined for object management
- Encryption is configured for security

### CloudFront
- Distributions are defined for content delivery
- Origins are configured for S3 and API Gateway
- Behaviors are configured for path-based routing
- Cache policies are configured for performance
- Security policies are configured for HTTPS

## IAM Patterns

### Role Definitions
- Roles are defined with least privilege
- Roles are scoped to specific resources
- Roles use managed policies when appropriate
- Roles use inline policies for custom permissions
- Roles have appropriate trust relationships

### Permission Boundaries
- Permission boundaries are used to limit maximum permissions
- Permission boundaries are defined at the stack level
- Permission boundaries are applied to all roles
- Permission boundaries are configured with least privilege
- Permission boundaries are documented

## Environment Configuration

### Context Values
- Context values are used for environment-specific configuration
- Context values are defined in `cdk.context.json`
- Context values are accessed via `this.node.tryGetContext()`
- Context values are documented
- Context values are used for cross-environment consistency

### Environment Variables
- Environment variables are used for runtime configuration
- Environment variables are defined at the stack level
- Environment variables are passed to Lambda functions
- Environment variables are documented
- Environment variables are used for cross-environment consistency

## Deployment Patterns

### Pipeline Definition
- CI/CD pipeline is defined in `.gitlab-ci.yml`
- Pipeline stages are defined for build, test, and deploy
- Pipeline environments are defined for dev, qa, and prod
- Pipeline artifacts are defined for build outputs
- Pipeline caching is defined for dependencies

### Deployment Stages
- Deployment stages are defined for dev, qa, and prod
- Deployment stages use different AWS accounts
- Deployment stages use different context values
- Deployment stages use different environment variables
- Deployment stages use different parameter values

## Testing Patterns

### Unit Testing
- Unit tests are defined for constructs and stacks
- Unit tests use Jest
- Unit tests mock AWS SDK calls
- Unit tests verify resource properties
- Unit tests verify resource relationships

### Integration Testing
- Integration tests are defined for deployed resources
- Integration tests use AWS SDK
- Integration tests verify resource behavior
- Integration tests clean up after themselves
- Integration tests are run in a dedicated environment

## Best Practices

### Security
- Use least privilege IAM roles
- Use encryption for sensitive data
- Use secure API authentication
- Use secure network configuration
- Use secure deployment practices

### Performance
- Use appropriate resource sizing
- Use caching for frequently accessed data
- Use efficient data access patterns
- Use appropriate Lambda memory and timeout settings
- Use monitoring for performance bottlenecks

### Cost Optimization
- Use pay-per-request billing for DynamoDB
- Use appropriate Lambda memory and timeout settings
- Use appropriate storage classes for S3
- Use appropriate instance types for EC2
- Use monitoring for cost optimization

### Operational Excellence
- Use logging for debugging
- Use monitoring for operational visibility
- Use alarms for critical errors
- Use dashboards for operational insights
- Use automated deployment for consistency

### Reliability
- Use multi-AZ deployments for high availability
- Use auto-scaling for variable load
- Use retry logic for transient errors
- Use circuit breakers for dependency failures
- Use monitoring for reliability insights
