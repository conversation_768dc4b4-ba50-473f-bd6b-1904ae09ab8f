type DailyActivityCount {
  date: String!
  count: Int!
}

type StatusDisciplineCount {
  status: Status!
  discipline: String!
  count: Int!
}

type Query {
  getProject(id: ID!): Project
  listProjects(limit: Int, nextToken: String): ProjectConnection!
  getActivity(id: ID!): Activity
  listActivities: [Activity!]!

  getCountByStatus(projectId: ID!): [StatusCount!]!
  getCompletedActivitiesByDay(projectId: ID!): [DailyActivityCount!]!
  getPlannedActivitiesByDay(projectId: ID!): [DailyActivityCount!]!
  getCountOfUnableToWorkActivities(projectId: ID!): Int!
  getActivityCountGroupedByStatusAndDiscipline(projectId: ID!): [StatusDisciplineCount!]!
  
  # Group and User queries
  getGroup(id: ID!): Group
  listGroups(limit: Int, nextToken: String): GroupConnection!
  getUser(username: String!): User
  listUsers(limit: Int, nextToken: String): UserConnection!
  
  # Contractor queries
  getContractor(id: ID!): Contractor
  listContractors(limit: Int, nextToken: String): ContractorConnection!

   # List all sync jobs with optional filtering
  listSyncJobs(
    limit: Int, 
    nextToken: String,
    syncType: String,
    projectId: ID,
    status: SyncJobStatus
  ): SyncJobConnection!

}

type Mutation {

  addProject(input: ProjectInput!): Project!
  updateProject(id: ID!, input: ProjectInput!): Project!
  deleteProject(id: ID!): String!

  addActivity(input: ActivityInput!): Activity!
  updateActivity(id: ID!, input: ActivityInput!): Activity!
  updateActivities(input: [UpdateActivityInput!]!): [Activity!]!
  deleteActivity(id: ID!): String!

  getUploadUrl(fileName: String!, activityId: ID!): S3Payload
  getDownloadUrl(key: String!, activityId: ID!): S3Payload
  getDeleteUrl(key: String!, activityId: ID!): S3Payload
  
  # Group CRUD operations
  createGroup(input: GroupInput!): Group!
  updateGroup(id: ID!, input: GroupInput!): Group!
  deleteGroup(id: ID!): String!
  
  # User-Group association operations
  addUserToGroup(username: String!, groupId: ID!): User!
  removeUserFromGroup(username: String!, groupId: ID!): User!
  
  # User CRUD operations
  createUser(input: UserInput!): User!
  deleteUser(username: String!): String!
  updateUser(username: String!, input: UserInput!): User!
  
  # Contractor CRUD operations
  createContractor(input: ContractorInput!): Contractor!
  updateContractor(id: ID!, input: ContractorInput!): Contractor!
  deleteContractor(id: ID!): String!
  
  # User-Contractor association operations
  addUserToContractor(username: String!, contractorId: ID!): User!
  removeUserFromContractor(username: String!, contractorId: ID!): User!
}

type StatusCount {
  status: Status!
  count: Int!
}

type Project {
  id: ID!
  projectId: ID!
  name: String!
  displayName: String!
  description: String
  status: ProjectStatus
  autoClose: Boolean # replace with allowStatusReview

  # P6 fields
  startDate: AWSDateTime
  finishDate: AWSDateTime
  allowStatusReview: Boolean
  activityCount: Int # the total number of activities in the project

  obsName: String # the obs category this project belongs to
  obsObjectId: Int
  parentEPSObjectId: Int
  parentEPSId: String
  parentEPSName: String
  wbsObjectId: Int
  isVisible: Boolean # indicates if the project is visible to workers, managers, or operators

  activities(
    limit: Int, 
    nextToken: String, 
    status: Status, 
    unableToWork: Boolean,
    plannedStartDateFrom: AWSDateTime,
    plannedStartDateTo: AWSDateTime,
    plannedFinishDateFrom: AWSDateTime,
    plannedFinishDateTo: AWSDateTime
  ): ActivityConnection!

  # the groups that are flagged as turnaround managers
  managerGroups: [Group!]
  # the groups that are flagged as operators
  operatorGroups: [Group!]
  # the groups that are flagged as executors/workers
  workerGroups: [Group!]
  # indicates if activities for the project are synced with interface
  isSyncing: Boolean


  createdAt: AWSDateTime
  updatedAt: AWSDateTime
}

type ProjectConnection {
  items: [Project!]!
  nextToken: String
  totalCount: Int
}

type ActivityConnection {
  items: [Activity!]!
  nextToken: String
  totalCount: Int!
}


type ActivityCode {
  id: ID!
  codeTypeDescription: String
  codeTypeName: String
  codeTypeValue: String
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
}

type ActivitySync {
  targetSystem: String
  lastSyncedAt: AWSDateTime
  syncStatus: String
}

type Activity {
  id: ID!
  activityId: ID
  name: String!

  status: Status
  statusCode: StatusCode
  reviewStatus: ReviewStatus

  startDate: AWSDateTime
  finishDate: AWSDateTime
  actualStartDate: AWSDateTime
  actualFinishDate: AWSDateTime
  baselineStartDate: AWSDateTime
  baselineFinishDate: AWSDateTime
  plannedFinishDate: AWSDateTime
  plannedStartDate: AWSDateTime

  projectId: String
  projectName: String
  projectObjectId: Int

  description: String
  assignedTo: String
  executorEmail: String

  percentComplete: Float
  plannedDuration: Float
  actualDuration: Float

  scopeNr: String #SCNR
  scopeId: String #SCOP Code-Value
  scopeDescription: String #SCOP Description

  equipment: String #EQPT
  equipmentDescription: String #EQPT Description

  floc: String #FLOC
  workorderNo: String #WorkorderNo

  jobOrderNumber: String #JobOrderNo
  notificationNumber: String #NotificationNR
  discipline: String #ADIS
  phase: String #Phase

  contractor: Contractor #Contractor the activity is assigned to

  progress: Int
  lastPercentComplete: Float
  syncStatus: ActivitySync

  # Activity codes
  activityCodes: [ActivityCode!]

  # Fields from activityCodes
  sequenceNo: Int
  resources: [Resource!]
  disciplineCode: String
  companyCode: String
  plannedEffort: Int
  actualEffort: Int
  revisedPlannedTimeline: Timeline

  # Resource
  resourceId: String
  resourceName: String
  resourceObjectId: Int

  # Relations
  predecessorActivities: [Activity]
  successorActivities: [Activity]
  
  # Taex fields
  logEvents: [LogEvent]
  unableToWork: Boolean
  evidences: [Attachment!]
  comments: [Comment!]
  attachments: [Attachment!]
  allowedGroups: [String!]
  viewerCanEdit: Boolean # indicates if the user can edit the activity
}

type LogEvent {
  timestamp: AWSDateTime
  event: String
  changedBy: String
}

type Comment {
  author: String
  text: String
  createdAt: AWSDateTime
}

type Resource {
  resourceCode: String
  resourceName: String
  discipline: String
}

type Role {
  id: ID!
  name: String!
}

type Timeline {
  start: AWSDateTime
  end: AWSDateTime
}

type Attachment {
  name: String!
  description: String
  contentType: String
  size: Int
  key: String
  uploadDate: AWSDateTime
  uploadedBy: String
}

type S3Payload {
  url: String!
  key: String!
}

enum ProjectStatus {
  ACTIVE
  INACTIVE
  WHATIF
  REQUESTED
  TEMPLATE
}

# Enums
enum Status {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
}

enum StatusCode {
  PLANNED
  ACTIVE
  INACTIVE
  WHATIF
  REQUESTED
  TEMPLATE
}

enum ReviewStatus {
  OK
  FOR_REVIEW
  REJECTED
}

enum UserRoleGroup {
  Admin
  Executor
  Manager
  Operator
}

enum GroupType {
  MANAGER
  OPERATOR
  WORKER
}

# Input Types for Mutations

input ProjectInput {
  isSyncing: Boolean
  managerGroups: [ID!]
  operatorGroups: [ID!]
  workerGroups: [ID!]
  isVisible: Boolean
}

input GroupInput {
  name: String!
  groupType: GroupType!
  description: String
}

input UserInput {
  username: String!
  email: String
  disciplines: [String!]
  equipments: [String!]
}

input LogEventInput {
  timestamp: AWSDateTime
  event: String
  changedBy: String
}

input CommentInput {
  author: String
  text: String
  createdAt: AWSDateTime
}

input AttachmentInput {
  name: String!
  description: String
  contentType: String
  size: Int
  key: String
  uploadDate: AWSDateTime
  uploadedBy: String
}

input ActivityCodeInput {
  id: ID
  codeTypeDescription: String
  codeTypeName: String
  codeTypeValue: String
}

input ActivityInput {
  name: String
  activityId: ID
  description: String
  assignedTo: String
  executorEmail: String
  status: String
  percentComplete: Float
  progress: Int
  dependencies: [ActivityInput!]
  revisedPlannedTimeline: TimelineInput
  predecessorActivityStatus: Status
  predecessorActivityId: String
  successorActivityStatus: Status
  successorActivityId: String
  plannedEffort: Int
  actualTimeline: TimelineInput
  resources: [ResourceInput!]
  comments: [CommentInput!]
  sequenceNo: Int
  attachments: [AttachmentInput!]
  allowedGroups: [String!]
  disciplineCode: String
  companyCode: String
  unableToWork: Boolean
  evidences: [AttachmentInput!]
  contractorId: ID
  activityCodes: [ActivityCodeInput!]
}

input UpdateActivityInput {
  id: ID!
  status: Status
  percentComplete: Float
  unableToWork: Boolean
  contractorId: ID
}

input ResourceInput {
  resourceCode: String
  resourceName: String
  discipline: String
}

input TimelineInput {
  start: AWSDateTime
  end: AWSDateTime
}

# Group and User types for permissions
type Group {
  id: ID!
  name: String!
  groupType: GroupType!
  description: String
  users: [User!]
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
}

type User {
  username: String!
  email: String
  managerGroups: [Group!]
  operatorGroups: [Group!]
  workerGroups: [Group!]
  contractors: [Contractor]
  disciplines: [String]
  equipments: [String]
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
}

type Contractor {
  id: ID!
  contractorNumber: String!
  name: String!
  description: String
  activities: [Activity]
  users: [User]
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type ContractorConnection {
  items: [Contractor!]!
  nextToken: String
  totalCount: Int
}

input ContractorInput {
  contractorNumber: String!
  name: String!
  description: String
}

type GroupConnection {
  items: [Group!]!
  nextToken: String
  totalCount: Int
}

type UserConnection {
  items: [User!]!
  nextToken: String
  totalCount: Int
}


##########################

# New types for syncjob data
type SyncJob {
  id: ID!                           # Generated from PK and SK
  syncType: String!                 # The type of sync job (PROJECTS, ACTIVITIES, etc.)
  projectId: ID                     # Project ID if applicable
  timestamp: AWSDateTime!           # When the sync job was initiated
  status: SyncJobStatus!            # Status of the sync job
  executionTime: Float              # Total execution time in seconds
  fetchTime: Float                  # Time spent fetching data
  writeTime: Float                  # Time spent writing data
  projectsProcessed: Int            # Number of projects processed
  activitiesProcessed: Int          # Number of activities processed
  activityCodesProcessed: Int       # Number of activity codes processed
  relationshipsProcessed: Int       # Number of relationships processed
}

# Connection type for pagination
type SyncJobConnection {
  items: [SyncJob!]!
  nextToken: String
  totalCount: Int
}

# Enum for sync job status
enum SyncJobStatus {
  SUCCEEDED
  FAILED
  IN_PROGRESS
}
