# Backend Documentation

This directory contains documentation for the backend components of the application.

## Table of Contents

- [User Verification Flow](./user-verification-flow.md) - Documentation for the automated flow that loads user data from Cognito to DynamoDB when a user verifies their account.

## Architecture Overview

The backend of the application is built using AWS CDK and consists of several stacks:

- **FrontendStack** - Manages the Cognito User Pool and authentication-related resources
- **GraphQlApiStack** - Provides the GraphQL API and DynamoDB table
- **WorkflowStack** - Handles workflow-related functionality
- **P6IntegrationStack** - Integrates with Primavera P6
- **CicdStack** - Provides CI/CD resources for GitLab runners (QA and Prod only)
- **MonitoringStack** - Provides usage monitoring (QA and Prod only)

## Key Components

- **AWS Cognito** - User authentication and management
- **AWS AppSync** - GraphQL API
- **AWS DynamoDB** - NoSQL database for storing application data
- **AWS Lambda** - Serverless functions for business logic
- **AWS SQS** - Message queues for asynchronous processing
- **AWS S3** - Storage for file attachments

## Event-Driven Architecture

The application uses an event-driven architecture for several key flows:

1. **User Verification Flow** - When a user verifies their account in Cognito, their data is automatically loaded into DynamoDB
2. **Activity Updates** - Changes to activities trigger events that can be processed asynchronously
3. **P6 Integration** - Integration with Primavera P6 is handled through asynchronous events

## Data Model

The application uses a single-table design in DynamoDB with the following key patterns:

- User data: `PK=USER#{username}`, `SK=METADATA#{username}`
- Group data: `PK=GROUP#{groupId}`, `SK=METADATA#{groupId}`
- Project data: `PK=PROJECT#{projectId}`, `SK=METADATA#{projectId}`
- Activity data: `PK=ACTIVITY#{activityId}`, `SK=METADATA#{activityId}`

Global Secondary Indexes (GSIs) are used for various access patterns.
