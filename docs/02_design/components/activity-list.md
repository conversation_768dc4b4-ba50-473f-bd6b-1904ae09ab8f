# Activity List Component Requirements

## 1. Component Overview

### Purpose
The Activity List component serves as a central interface for users to view, filter, sort, and interact with turnaround execution activities assigned to them or within their projects. It provides a responsive, paginated list of activities with search capabilities and filtering options to help users quickly find and access relevant activities.

### Relationship to System Requirements
This component directly supports the following functional requirements from the main requirements document:
- **FR-1**: Import projects and activities from Oracle Primavera P6
- **FR-3**: Define and display key activity attributes
- **FR-5**: Workers log actual start/finish times, update progress, and set statuses

### Visual Representation in Application Hierarchy

```mermaid
graph TD
    A[App Root] --> B[Main Layout]
    B --> C[Dashboard View]
    B --> D[Activity Management View]
    D --> E[Activity List Component]
    E --> F[Activity Item Component]
    E --> G[Project Selector Component]
    F --> H[Activity Detail View]
```

## 2. Core Functionality

### 2.1 Activity Display
- Present activities in a card-based list format
- Display key activity information including:
  - Activity name/title
  - Planned start/finish dates
  - Progress indicator
  - Contractor information
  - Status indicators

### 2.2 Filtering Capabilities
- Filter activities by predefined timeframes:
  - All activities
  - Today's activities only
  - Today's activities plus unfinished past activities
- Support custom filters based on user role and permissions

### 2.3 Sorting Functionality
- Sort activities by date:
  - Oldest first (ascending)
  - Newest first (descending)
- Maintain sort state across filter changes

### 2.4 Search Functionality
- Provide text-based search across multiple activity fields:
  - Activity name
  - Dates
  - Progress percentage
  - Contractor information
- Toggle search bar visibility to optimize screen space

### 2.5 Pagination
- Limit displayed activities to configurable page sizes
- Provide navigation controls for moving between pages
- Display current page information and total activity count

### 2.6 Project Selection Integration
- Allow users to select projects to view associated activities
- Update activity list based on project selection
- Support single project selection at a time

## 3. Data Concepts

### 3.1 Required Data
- Activity data including:
  - Unique identifier
  - Name/title
  - Planned start/finish dates
  - Actual start/finish dates (if available)
  - Status information
  - Progress percentage
  - Contractor information
- Project data including:
  - Unique identifier
  - Name/title

### 3.2 Data Flow

```mermaid
sequenceDiagram
    participant User
    participant ActivityList as Activity List Component
    participant ProjectSelector as Project Selector Component
    participant DataSource as Data Source
    participant ActivityItem as Activity Item Component
    participant Navigation as Navigation System

    User->>ProjectSelector: Select Project
    ProjectSelector->>ActivityList: Project Selected Event
    ActivityList->>DataSource: Request Project Activities
    DataSource-->>ActivityList: Return Activities
    ActivityList->>ActivityList: Filter/Sort Activities
    ActivityList->>ActivityItem: Display Activities
    User->>ActivityItem: Click Activity
    ActivityItem->>ActivityList: Activity Click Event
    ActivityList->>Navigation: Navigate to Activity Detail
```

## 4. User Interactions

### 4.1 Activity Selection
- Users can click on any activity item to navigate to its detailed view
- Selection navigates to the activity detail view

### 4.2 Filter Operations
- Users can access filter options via the filter menu button
- Available filters include:
  - All activities
  - Today's activities only
  - Today and unfinished activities
- Filter selection immediately updates the displayed activities

### 4.3 Sort Operations
- Users can access sort options via the sort menu button
- Available sort options:
  - Oldest first (by planned start date)
  - Newest first (by planned start date)
- Sort selection immediately reorders the displayed activities

### 4.4 Search Operations
- Users can toggle search bar visibility via the filter menu
- Search input filters activities in real-time as the user types
- Clear button allows quick removal of search text

### 4.5 Pagination Controls
- Users can navigate between pages using first, previous, next, and last buttons
- Users can select page size from predefined options
- Page navigation updates the displayed activities

### 4.6 User Interaction State Diagram

```mermaid
stateDiagram-v2
    [*] --> ViewingList: Initial Load
    ViewingList --> Filtering: Click Filter Menu
    ViewingList --> Sorting: Click Sort Menu
    ViewingList --> Searching: Toggle Search
    ViewingList --> Paginating: Use Pagination Controls
    ViewingList --> SelectingProject: Use Project Selector
    ViewingList --> ViewingDetail: Click Activity Item
    
    Filtering --> ViewingList: Select Filter Option
    Sorting --> ViewingList: Select Sort Option
    Searching --> ViewingList: Enter Search Text
    Paginating --> ViewingList: Change Page
    SelectingProject --> ViewingList: Project Selected
    
    ViewingDetail --> [*]: Navigate to Detail View
```

## 5. Subcomponents

### 5.1 Activity Item Component
- **Purpose**: Display individual activity information in a card format
- **Inputs**: Activity data
- **Outputs**: Activity selection events
- **Responsibilities**:
  - Display activity details in a consistent format
  - Show progress indicators
  - Present contractor information
  - Indicate activity status
  - Handle selection events

### 5.2 Project Selector Component
- **Purpose**: Allow users to select projects to view
- **Inputs**: Available projects data
- **Outputs**: Project selection events
- **Responsibilities**:
  - Display available projects
  - Allow selection of a project
  - Communicate selection to parent components

### 5.3 Component Tree

```mermaid
graph TD
    A[Activity List Component] --> B[Project Selector Component]
    A --> C[Search Field]
    A --> D[Filter Menu]
    A --> E[Sort Menu]
    A --> F[Activity Items Container]
    F --> G[Activity Item Component]
    G --> H[Progress Indicator]
    A --> I[Pagination Component]
```

## 6. Responsive Design Concepts

### 6.1 Desktop View
- Full horizontal layout
- Side-by-side arrangement of filters and controls

### 6.2 Tablet View
- Filters may wrap to multiple rows
- Project selector takes full width
- Menu buttons remain on the same row

### 6.3 Mobile View
- Stacked layout for all controls
- Full-width search field
- Simplified pagination controls
- Larger touch targets for better usability

### 6.4 Responsive Layout Visualization

```mermaid
graph TD
    subgraph "Desktop Layout"
    A1[Header + Project Selector] --- B1[Filter Controls]
    B1 --- C1[Search Bar]
    C1 --- D1[Activity List]
    D1 --- E1[Pagination]
    end
    
    subgraph "Tablet Layout"
    A2[Header] --- F2[Project Selector]
    F2 --- B2[Filter Controls]
    B2 --- C2[Search Bar]
    C2 --- D2[Activity List]
    D2 --- E2[Pagination]
    end
    
    subgraph "Mobile Layout"
    A3[Header] --- F3[Project Selector]
    F3 --- B3[Filter Controls]
    B3 --- C3[Search Bar]
    C3 --- D3[Activity List]
    D3 --- E3[Pagination]
    end
```