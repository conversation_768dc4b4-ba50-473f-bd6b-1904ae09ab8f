# Activity Component Requirements

## 1. Component Overview

### Purpose
The Activity component serves as a detailed view interface for users to view and interact with a single turnaround execution activity. It provides a comprehensive, tab-based interface that displays activity details, allows progress updates, shows relationships with other activities, manages evidence attachments, and displays activity logs.

### Relationship to System Requirements
This component directly supports the following functional requirements from the main requirements document:
- **FR-3**: Define and display key activity attributes
- **FR-5**: Workers log actual start/finish times, update progress, and set statuses
- **FR-6**: System maintains an audit log of every change
- **FR-7**: Attach and manage evidence files per activity

### Visual Representation in Application Hierarchy

```mermaid
graph TD
    A[App Root] --> B[Main Layout]
    B --> C[Dashboard View]
    B --> D[Activity Management View]
    D --> E[Activity List Component]
    E --> F[Activity Component]
    F --> G[Progress Component]
    F --> H[Activity Details Component]
    F --> I[Activity Relationships Component]
    F --> J[Photo List and Capture Component]
    F --> K[Logs Component]
```

## 2. Core Functionality

### 2.1 Activity Information Display
- Present comprehensive activity information including:
  - Activity name/title
  - Activity ID
  - Status indicators
  - "Unable to work" flag when applicable

### 2.2 Tab-Based Interface
- Organize activity information into logical sections via tabs:
  - Progress tab for status updates
  - Details tab for comprehensive activity information
  - Relationships tab for dependencies and related activities
  - Evidences tab for photos and document attachments
  - Logs tab for activity history and audit trail

### 2.3 Error Handling
- Display appropriate error messages when activity cannot be loaded
- Handle access denied scenarios with limited view access
- Provide visual feedback during loading states

### 2.4 Activity Update Capabilities
- Allow users to update activity information
- Propagate changes to parent components
- Provide feedback on update success or failure

## 3. Data Concepts

### 3.1 Required Data
- Activity data including:
  - Unique identifier
  - Name/title
  - Status information
  - "Unable to work" flag
  - Progress information
  - Detailed attributes (shown in details tab)
  - Relationship information (predecessors, successors)
  - Evidence attachments
  - Activity logs

### 3.2 Data Flow

```mermaid
sequenceDiagram
    participant User
    participant ActivityComponent as Activity Component
    participant DataSource as Data Source
    participant SubComponents as Tab Subcomponents
    
    User->>ActivityComponent: View Activity (ID)
    ActivityComponent->>DataSource: Request Activity Data
    DataSource-->>ActivityComponent: Return Activity Data
    ActivityComponent->>SubComponents: Distribute Activity Data
    
    User->>SubComponents: Interact with Tab (e.g., Update Progress)
    SubComponents->>ActivityComponent: Activity Change Event
    ActivityComponent->>DataSource: Update Activity
    DataSource-->>ActivityComponent: Return Updated Activity
    ActivityComponent->>SubComponents: Refresh with Updated Data
```

## 4. User Interactions

### 4.1 Tab Navigation
- Users can switch between tabs to access different aspects of the activity
- Tab selection preserves state within each tab
- Default tab is Progress tab

### 4.2 Activity Updates
- Users can update activity information through tab interfaces
- Updates trigger validation and submission to backend
- Feedback is provided on update success or failure

### 4.3 Error State Handling
- Access denied errors show limited information
- General errors display appropriate messages
- Users can retry failed operations

### 4.4 User Interaction State Diagram

```mermaid
stateDiagram-v2
    [*] --> Loading: Initial Load
    Loading --> ViewingActivity: Load Success
    Loading --> ErrorState: Load Failure
    
    ViewingActivity --> ViewingProgress: Select Progress Tab
    ViewingActivity --> ViewingDetails: Select Details Tab
    ViewingActivity --> ViewingRelationships: Select Relationships Tab
    ViewingActivity --> ViewingEvidences: Select Evidences Tab
    ViewingActivity --> ViewingLogs: Select Logs Tab
    
    ViewingProgress --> UpdatingProgress: Edit Progress
    UpdatingProgress --> ViewingProgress: Update Success
    UpdatingProgress --> ErrorState: Update Failure
    
    ViewingEvidences --> CapturingPhoto: Capture Photo
    CapturingPhoto --> ViewingEvidences: Photo Captured
    
    ErrorState --> Loading: Retry
    
    ViewingActivity --> [*]: Navigate Away
```

## 5. Subcomponents

### 5.1 Progress Component
- **Purpose**: Allow users to view and update activity progress
- **Inputs**: Activity data
- **Outputs**: Activity update events
- **Responsibilities**:
  - Display current progress percentage
  - Allow progress updates
  - Handle status changes
  - Manage "unable to work" flag

### 5.2 Activity Details Component
- **Purpose**: Display comprehensive activity information
- **Inputs**: Activity data
- **Outputs**: None (display only)
- **Responsibilities**:
  - Show all activity attributes
  - Format dates and times appropriately
  - Display related information (contractor, equipment, etc.)
  - Handle access denied scenarios

### 5.3 Activity Relationships Component
- **Purpose**: Display activity dependencies and relationships
- **Inputs**: Activity data with relationship information
- **Outputs**: None (display only)
- **Responsibilities**:
  - Show predecessor activities
  - Show successor activities
  - Display relationship types
  - Visualize relationship network

### 5.4 Photo List and Capture Component
- **Purpose**: Manage evidence attachments for the activity
- **Inputs**: Activity data
- **Outputs**: Activity update events (with new attachments)
- **Responsibilities**:
  - Display existing photo/document attachments
  - Allow capture of new photos
  - Support document uploads
  - Manage attachment metadata

### 5.5 Logs Component
- **Purpose**: Display activity history and audit trail
- **Inputs**: Activity data with logs
- **Outputs**: None (display only)
- **Responsibilities**:
  - Show chronological activity changes
  - Display user information for each change
  - Format timestamps appropriately
  - Filter logs by type if needed

### 5.6 Component Tree

```mermaid
graph TD
    A[Activity Component] --> B[Activity Header]
    A --> C[Tab Container]
    C --> D[Progress Component]
    C --> E[Activity Details Component]
    C --> F[Activity Relationships Component]
    C --> G[Photo List and Capture Component]
    C --> H[Logs Component]
    B --> I[Activity Title]
    B --> J[Activity ID]
    B --> K[Status Indicators]
```

## 6. Responsive Design Concepts

### 6.1 Desktop View
- Full horizontal layout
- Tab navigation displayed as horizontal tabs
- Side-by-side arrangement of related information

### 6.2 Tablet View
- Preserved tab navigation
- Some information sections may stack vertically
- Adjusted spacing and padding

### 6.3 Mobile View
- Fully stacked layout
- Full-width tabs and content sections
- Optimized touch targets for better usability
- Simplified information display where appropriate

### 6.4 Responsive Layout Visualization

```mermaid
graph TD
    subgraph "Desktop Layout"
    A1[Activity Header] --- B1[Tab Navigation]
    B1 --- C1[Tab Content]
    end
    
    subgraph "Tablet Layout"
    A2[Activity Header] --- B2[Tab Navigation]
    B2 --- C2[Tab Content<br>Adjusted Layout]
    end
    
    subgraph "Mobile Layout"
    A3[Activity Header<br>Stacked Elements] --- B3[Tab Navigation<br>Full Width]
    B3 --- C3[Tab Content<br>Simplified Layout]
    end
```




