| Field                     | Type                      | Description                                                                 |
|---------------------------|---------------------------|-----------------------------------------------------------------------------|
| `id`                      | ID                        | TAEX internal primary key                                                   |
| `activityId`              | ID                        | External activity identifier                                                |
| `name`                    | String                    | Short description of the work                                               |
| `status`                  | Enum                      | Activity status: {NOT_STARTED, IN_PROGRESS, COMPLETED}                      |
| `statusCode`              | Enum                      | Status code: {PLANNED, ACTIVE, INACTIVE, WHATIF, REQUESTED, TEMPLATE}       |
| `reviewStatus`            | Enum                      | Review status: {OK, FOR_REVIEW, REJECTED}                                   |
| `startDate`               | AWSDateTime               | Start date                                                                  |
| `finishDate`              | AWSDateTime               | Finish date                                                                 |
| `actualStartDate`         | AWSDateTime               | Logged when work is started                                                 |
| `actualFinishDate`        | AWSDateTime               | Logged when work is complete                                                |
| `baselineStartDate`       | AWSDateTime               | Baseline start date                                                         |
| `baselineFinishDate`      | AWSDateTime               | Baseline finish date                                                        |
| `plannedStartDate`        | AWSDateTime               | Planned start date                                                          |
| `plannedFinishDate`       | AWSDateTime               | Planned finish date                                                         |
| `projectId`               | String                    | References the TAEX Project                                                 |
| `projectName`             | String                    | Name of the project                                                         |
| `projectObjectId`         | Int                       | Project object identifier                                                   |
| `description`             | String                    | Detailed description of the activity                                        |
| `assignedTo`              | String                    | Person assigned to the activity                                             |
| `executorEmail`           | String                    | Email of the executor                                                       |
| `percentComplete`         | Float                     | Current % complete                                                          |
| `plannedDuration`         | Float                     | Planned duration                                                            |
| `actualDuration`          | Float                     | Actual duration                                                             |
| `scopeNr`                 | String                    | Scope number (SCNR)                                                         |
| `scopeId`                 | String                    | Scope ID (SCOP Code-Value)                                                  |
| `scopeDescription`        | String                    | Scope description (SCOP Description)                                        |
| `equipment`               | String                    | Equipment type identifier (EQPT)                                            |
| `equipmentDescription`    | String                    | Human-readable description of the equipment (EQPT Description)              |
| `floc`                    | String                    | Functional location (FLOC)                                                  |
| `workorderNo`             | String                    | Work order number (WorkorderNo)                                             |
| `jobOrderNumber`          | String                    | Job order number (JobOrderNo)                                               |
| `notificationNumber`      | String                    | Notification number (NotificationNR)                                        |
| `discipline`              | String                    | Discipline (ADIS)                                                           |
| `phase`                   | String                    | Phase of the work (Phase)                                                   |
| `contractor`              | Contractor                | Contractor the activity is assigned to                                      |
| `progress`                | Int                       | Progress indicator                                                          |
| `lastPercentComplete`     | Float                     | Previous % complete before last update                                      |
| `syncStatus`              | ActivitySync              | Synchronization status with external systems                                |
| `activityCodes`           | [ActivityCode]            | Activity codes                                                              |
| `sequenceNo`              | Int                       | Sequence number                                                             |
| `resources`               | [Resource]                | Resources assigned to the activity                                          |
| `disciplineCode`          | String                    | Discipline code                                                             |
| `companyCode`             | String                    | Company code                                                                |
| `plannedEffort`           | Int                       | Planned effort                                                              |
| `actualEffort`            | Int                       | Actual effort                                                               |
| `revisedPlannedTimeline`  | Timeline                  | Revised planned timeline                                                    |
| `resourceId`              | String                    | Resource identifier                                                         |
| `resourceName`            | String                    | Resource name                                                               |
| `resourceObjectId`        | Int                       | Resource object identifier                                                  |
| `predecessorActivities`   | [Activity]                | Predecessor activities                                                      |
| `successorActivities`     | [Activity]                | Successor activities                                                        |
| `logEvents`               | [LogEvent]                | Log events                                                                  |
| `unableToWork`            | Boolean                   | Indicates if unable to work                                                 |
| `evidences`               | [Attachment]              | Evidence attachments                                                        |
| `comments`                | [Comment]                 | Comments on the activity                                                    |
| `attachments`             | [Attachment]              | Attached files                                                              |
| `allowedGroups`           | [String]                  | Groups allowed to access this activity                                      |
| `viewerCanEdit`           | Boolean                   | Indicates if the viewer can edit the activity                               |
