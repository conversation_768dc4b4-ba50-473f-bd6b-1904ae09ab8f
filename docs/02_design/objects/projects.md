## Project

| Field               | Type                | Description                                                    |
|---------------------|---------------------|----------------------------------------------------------------|
| `id`                | ID                  | TAEX internal primary key                                      |
| `projectId`         | ID                  | External project identifier                                    |
| `name`              | String              | Project name                                                   |
| `displayName`       | String              | Human-readable display name                                    |
| `description`       | String              | Project description                                            |
| `status`            | ProjectStatus       | Status: {ACTIVE, INACTIVE, WHATIF, REQUESTED, TEMPLATE}        |
| `autoClose`         | Boolean             | Legacy field (replaced by allowStatusReview)                   |
| `startDate`         | AWSDateTime         | Project start date (from P6)                                   |
| `finishDate`        | AWSDateTime         | Project finish date (from P6)                                  |
| `allowStatusReview` | Boolean             | Whether status review is allowed                               |
| `activityCount`     | Int                 | Total number of activities in the project                      |
| `obsName`           | String              | OBS category this project belongs to                           |
| `obsObjectId`       | Int                 | OBS object identifier                                          |
| `parentEPSObjectId` | Int                 | Parent EPS object identifier                                   |
| `parentEPSId`       | String              | Parent EPS identifier                                          |
| `parentEPSName`     | String              | Parent EPS name                                                |
| `wbsObjectId`       | Int                 | WBS object identifier                                          |
| `isVisible`         | Boolean             | Indicates if project is visible to workers, managers, operators|
| `activities`        | ActivityConnection  | Connection to project activities (with filtering options)      |
| `managerGroups`     | [Group]             | Groups flagged as turnaround managers                          |
| `operatorGroups`    | [Group]             | Groups flagged as operators                                    |
| `workerGroups`      | [Group]             | Groups flagged as executors/workers                            |
| `isSyncing`         | Boolean             | Indicates if activities are synced with interface              |
| `createdAt`         | AWSDateTime         | Record creation timestamp                                      |
| `updatedAt`         | AWSDateTime         | Last-updated timestamp                                         |
