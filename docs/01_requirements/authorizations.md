# Roles

* Worker (or executor) is asked to perform a certain task in a Turnaround Project and give feedback on the current completion state (e.g. status). A worker is assigned to a company (e.g. Siemens) and is assigned to a discipline (e.g. electrician), those two assignments determin a worker is allowed to see and edit an activitiy.
* Turnaround Manager is managing the a Project.
* <PERSON><PERSON> is responsible for the entire TAEX platform and is allowed to change everything on all projects
* Opera<PERSON> is the operator of a plant on which the turnaround is performed. He is allowed to see the status of the Turnaround project and its activities but is not allowed to chang anything.

## Worker Access Requirements

For a worker to see and edit an activity, **ALL** of the following conditions must be met:

1. **Project Assignment**: The worker must be assigned to the project (through worker groups)
2. **Discipline Match**: The worker must have the right discipline that matches the activity's discipline
3. **Equipment Association**: The worker must be associated with the equipment specified in the activity
4. **Contractor Association**: The worker must be associated with the contractor assigned to the activity

Only when all four conditions are satisfied will a worker have visibility and edit permissions for an activity.

```mermaid
graph TD
    subgraph "Worker Access Requirements"
    A["Project Assignment"] 
    B["Discipline Match"]
    C["Equipment Association"]
    D["Contractor Association"]
    
    A --> E["Access Intersection"]
    B --> E
    C --> E
    D --> E
    
    E --> F["Worker can see and edit activity"]
    end
    
    style E fill:#f9f,stroke:#333,stroke-width:2px
    style F fill:#bfb,stroke:#333,stroke-width:2px
```

The diagram below visualizes how these four requirements intersect to grant access:

```mermaid
venn-diagram
    title Worker Access Requirements
    A: "Project Assignment"
    B: "Discipline Match"
    C: "Equipment Association" 
    D: "Contractor Association"
    A∩B∩C∩D: "Access Granted"
```


# User Authorizations for Business Object 'Projects'

| Role     | View Own Activities | Edit Own Activities | View All Activities | Edit All Activities | Edit Permissions in own Project | Edit Permissions in ALL Projects |
|-----------|---------------------|--------------------|--------------------|-------------------|----------------|----------------|
| Worker    | ✓                   | ✓                  | -                  | -                 | -              | -              |
| Admin     | ✓                   | ✓                  | ✓                  | ✓                 | ✓              | ✓              |
| Operator  | ✓                   | -                  | ✓                  | -                 | ✓              | -              |
| Turnaround Manager | ✓                   | ✓                  | ✓                  | ✓                 | ✓              | -              |

# User Authorizations for Business Object 'Activities'

| Role     | Set Progress | Bulk Update Progress | Write Comment | Upload Evidence | Delete Evidence |
|-----------|--------------|---------------------|---------------|----------------|----------------|
| Worker    | ✓            | -                   | ✓             | ✓              | ✓              |
| Admin     | ✓            | ✓                   | ✓             | ✓              | ✓              |
| Operator  | -            | -                   | ✓             | -              | -              |
| Turnaround Manager     | ✓            | -                   | ✓             | ✓              | ✓              |
