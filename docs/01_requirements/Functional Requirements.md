# TAEX Functional Requirements Document

## 1. System Purpose & Domain

**Purpose:**

- Track, report, and analyze progress of Plant Turnaround execution activities, focusing on alignment between planned and actual timelines.
- Provide real-time visibility into execution status to support timely decision-making and continuous improvement.

**Domain:**

- Chemical industry; specifically the execution phase of plant turnarounds.
- Integrates with Oracle Primavera P6 for planning data; TAEX does not handle planning.

## 2. User Roles & Permissions

| Role                   | Permissions & Responsibilities                                                                                                                                                                                                                                                                   |
| ---------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **Worker**             | - Execute assigned activities when belonging to correct discipline group, having equipment-type permission, and matching contractor assignment.<br>- Update activity status (e.g. In Progress, Complete, Delayed).<br>- Report progress (% complete), add comments, attach evidence (photos, documents). |
| **Turnaround Manager** | - Full CRUD on all execution-phase activities.<br>- Import projects & activities from P6.<br>- Override permissions and reassign tasks.<br>- View all dashboards & reports.                                                                                                                                  |
| **Operator**           | - Read-only access to all activities and dashboards.<br>- No edit or approval rights.                                                                                                                                                                                                                |
| **Admin**              | - Manage access permissions across all projects.<br>- Add and manage contractors, discipline groups, and equipment types.<br>- Configure system-wide settings and user accounts.                                                                                                                        |

*(Additional roles such as Safety Inspector, Quality Auditor, or External Vendor can be added as needed.)*

## 3. Core Functionalities

### 3.1 Activity Management

- **FR-1:** Import projects and activities from Oracle Primavera P6 on demand or via scheduled sync.
- **FR-2:** Create, edit, and delete execution-phase activities manually when needed.
- **FR-3:** Define and display key activity attributes: planned start/finish, discipline, equipment type, contractor.
- **FR-4:** Assign and reassign activities to Workers.

### 3.2 Status & Progress Tracking

- **FR-5:** Workers log actual start/finish times, update progress (% complete), and set statuses.
- **FR-6:** System maintains an audit log of every change: timestamp, user, field, old/new values.
- **FR-7:** Attach and manage evidence files (photos, PDFs) per activity.

### 3.3 Permission & Discipline Enforcement

- **FR-8:** Enforce that only properly scoped Workers (discipline, equipment permission, contractor) can update an activity.
- **FR-9:** Track and log permission grants/revocations with audit details.

### 3.4 Approval Workflows

- **FR-10:** Require supervisor (Turnaround Manager) sign-off before an activity moves to **Complete**.
- **FR-11:** Configure escalation rules to notify Managers if approvals are pending beyond defined thresholds.

## 4. Reporting & Analytics

### 4.1 Key Performance Indicators (KPIs)

- **FR-12:** Calculate and display:
  - **Schedule Variance:** Difference between planned vs. actual start/finish times.
  - **On-Time Completion Rate:** Percentage of activities completed on or before planned finish date.
  - **Open Deviation Count:** Count of activities with status “Delayed” or “At-Risk.”

### 4.2 Predefined Reports & Dashboards

- **FR-13:** Provide a set of predefined templates, including:
  - Execution Status Dashboard (by discipline, equipment type, contractor).
  - KPI Summary Report.
  - Deviations & Exceptions Report.
- **FR-14:** Export scheduled reports:
  - Daily PDF for Managers.
  - Weekly Excel for Planners.