
# Turnaround Management: Relationships Between Projects and Activities

This document explains the hierarchical relationships and dependencies between **Projects** and **Activities** in turnaround management software. It also includes details about roles, groups, contractors, and process steps like scheduling and progress reporting.

---

## 1. Project
- **Definition**: A project represents the overarching event, such as a shutdown, turnaround, or major maintenance activity.
- **Characteristics**:
  - Defines the timeline with start and finish dates.
  - Has a ProjectStatus (ACTIVE, INACTIVE, WHATIF, REQUESTED, TEMPLATE).
  - Contains a name, displayName, and description.
  - Has an activityCount tracking the total number of activities.
  - Can have allowStatusReview flag to control status review capabilities.
  - Contains OBS (Organizational Breakdown Structure) information including obsName, obsObjectId.
  - Contains WBS (Work Breakdown Structure) information including wbsObjectId.
  - Has visibility controls through isVisible flag.
  - Can be configured for syncing with external systems (isSyncing).
- **Relation to Other Entities**:
  - Acts as the top-level container for all related activities.
  - Associated with specific groups for different roles:
    - managerGroups: Groups with management access to the project.
    - operatorGroups: Groups with operator access to the project.
    - workerGroups: Groups with worker/executor access to the project.
  - Tracks progress across all related activities.

---

## 2. Activity
- **Definition**: An activity is a unit of work within a project, representing a specific task or action.
- **Characteristics**:
  - Has a Status (NOT_STARTED, IN_PROGRESS, COMPLETED).
  - Has a StatusCode (PLANNED, ACTIVE, INACTIVE, WHATIF, REQUESTED, TEMPLATE).
  - Has a ReviewStatus (OK, FOR_REVIEW, REJECTED).
  - Includes multiple timeline properties:
    - startDate/finishDate: General timeline.
    - actualStartDate/actualFinishDate: When the activity was actually performed.
    - baselineStartDate/baselineFinishDate: Original planned timeline.
    - plannedStartDate/plannedFinishDate: Current planned timeline.
    - revisedPlannedTimeline: Updated planning information.
  - Tracks progress through percentComplete, progress, and actualDuration fields.
  - Can be marked as unableToWork when execution is blocked.
  - Contains detailed information like description, discipline, phase, equipment, etc.
  - Has scopeNr, scopeId, and scopeDescription for scope management.
  - Contains work order information through workorderNo, jobOrderNumber, and notificationNumber.
  - Includes resource information and planned/actual effort tracking.
  - Has attachments, evidences, and comments for documentation.
  - Maintains a log of events for activity history.
- **Relation to Other Entities**:
  - Belongs to a **project**.
  - Can be assigned to a **contractor**.
  - Has predecessor and successor relationships with other activities.
  - Can have multiple resources assigned.
  - Has activity codes for classification.
  - Can have permissions controlled through allowedGroups.

---

## 3. Groups and Users
- **Definition**: Groups and users represent the organizational structure and individuals who interact with the system.
- **Group Characteristics**:
  - Has a name, description, and groupType.
  - GroupType can be MANAGER, OPERATOR, or WORKER.
  - Groups can contain multiple users.
- **User Characteristics**:
  - Identified by username and email.
  - Can belong to multiple groups of different types.
  - Can be associated with contractors.
  - Has disciplines and equipments specializations.
- **Relation to Other Entities**:
  - Groups can be assigned to projects as manager, operator, or worker groups.
  - Users can be assigned to activities through the assignedTo or executorEmail fields.

---

## 4. Contractors
- **Definition**: Contractors represent external companies or teams that perform activities.
- **Characteristics**:
  - Has a contractorNumber, name, and description.
  - Can have multiple users associated with it.
  - Can be assigned multiple activities.
- **Relation to Other Entities**:
  - Activities can be assigned to contractors.
  - Users can be associated with contractors.

## 5. Main Process Steps
Turnaround management involves several core processes:
1. **Adjusting the Schedule of Activities**:
   - Activities are scheduled based on dependencies, resource availability, and project timelines.
   - Changes to schedules are updated in real time to reflect any disruptions or changes in priorities.

2. **Reporting Back on Progress**:
   - Progress updates are logged at the activity level through status changes and percentComplete updates.
   - These updates roll up to provide project-level status reports.
   - The system tracks both planned and actual timelines for comparison.

3. **Syncing with External Systems**:
   - SyncJobs track the synchronization of data with external systems.
   - Different types of syncs can be performed (PROJECTS, ACTIVITIES, etc.).
   - Sync status and performance metrics are tracked.

---

## 6. Roles and Permissions
- **Definition**: Roles define the level of access and permissions users have within the system.
- **Types of Roles (GroupTypes)**:
  0. **ADMIN**:
    - Can edit all activities and all projects.
    - Can edit sync status and permissions of projects
  1. **MANAGER**:
     - Can edit all activities of projects he has access to.
     - Can add attachments and make changes.
  2. **OPERATOR**:
     - Can see all activities but cannot report back on them.
     - Has read-only access to the system.
  3. **WORKER**:
     - Can report back on assigned activities.
     - Has execution responsibilities.
- **Group Structure**:
  - Groups can be organized by disciplines and equipment types.
  - Example: Groups can be specialized like "mechanik_pumpen_siemens" for specific disciplines and equipment types.
- **Relation to Other Entities**:
  - Groups are assigned to projects with specific roles.
  - Users belong to groups which determines their access and permissions.

---

## 7. Data Synchronization
- **Definition**: The system synchronizes data with external systems through SyncJobs.
- **Characteristics**:
  - SyncJobs track the type of sync, status, and performance metrics.
  - Syncs can be for projects, activities, activity codes, or relationships.
  - Status can be SUCCEEDED, FAILED, or IN_PROGRESS.
- **Relation to Other Entities**:
  - SyncJobs can be associated with specific projects.
  - Activities can track their sync status with external systems.

## Hierarchical Relationship

```plaintext
Project
 ├── Activity A
 │    ├── Resources
 │    ├── Attachments
 │    ├── Comments
 │    └── Activity Codes
 ├── Activity B
 │    ├── Resources
 │    ├── Attachments
 │    ├── Comments
 │    └── Activity Codes
 └── ...

Groups
 ├── Manager Groups
 │    └── Users
 ├── Operator Groups
 │    └── Users
 └── Worker Groups
      └── Users

Contractors
 └── Users
```
