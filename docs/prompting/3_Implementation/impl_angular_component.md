# Prompt for Generating an Angular Component

## Context
I am developing an Angular application and need to implement a component. The component should follow best practices for performance, modularity, and maintainability. The application structure follows Angular’s standard architecture with modules, services, and components. It may also involve state management using RxJS, API integration via Angular services, and Angular Material for UI elements.

## Component Requirements
- **Name**: `IdeaDetailComponent`
- **Purpose**: Displays an improvement idea with fields like `id`, `title`, `author`, `problem`, `solution`, `location`.
- **Data Inputs**: The component receives an `ID` from the URL OR via the `@Input()` parameter.
- **Services Used**: The component should fetch the data via the `IdeaService`.
- **UI Elements**: [Describe which UI elements should be used, e.g., Angular Material `mat-card`, `mat-table`, or custom components.]
- **Routing**: The component is available via `/idea/{id}`.

## Implementation Plan

### 1. Component Creation
- Use the Angular CLI: `ng generate component [ComponentName]`
- Define `@Input()` and `@Output()` properties.
- Implement lifecycle hooks (`ngOnInit`, `ngOnChanges`, etc.).

### 2. Service Integration
- Create or inject a service to fetch and manage data.
- Use `HttpClient` to retrieve API data asynchronously.
- Implement caching if necessary.

### 3. UI and Styling
- Use Angular Material components where applicable.
- Follow a modular CSS approach.
- Ensure responsiveness with Flexbox or CSS Grid.

### 4. Event Handling
- Implement event emitters for user interactions.
- Use RxJS for reactive programming where applicable.

### 5. Performance Optimization
- Use `OnPush` change detection strategy.
- Optimize for lazy loading and async data binding.
- Avoid unnecessary re-renders.

### 6. Testing
- Write unit tests using Jasmine/Karma.
- Implement end-to-end tests using Cypress or Playwright.
