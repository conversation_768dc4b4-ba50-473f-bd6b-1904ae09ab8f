// Import AWS SDK v3 modules and the UUID generator.
const { DynamoDBClient } = require("@aws-sdk/client-dynamodb");
const { DynamoDBDocumentClient, GetCommand, QueryCommand, PutCommand, UpdateCommand, DeleteCommand } = require("@aws-sdk/lib-dynamodb");
const { v4: uuidv4 } = require("uuid");

// Create a DynamoDB client and wrap it with the DocumentClient for easier JSON handling.
const ddbClient = DynamoDBDocumentClient.from(new DynamoDBClient({}));

// Table name is assumed to be set as an environment variable.
const TABLE_NAME = process.env.TABLE_NAME;

// Helper: Check that the user belongs to the admin group.
function checkAdmin(userGroups) {
  if (!userGroups || !userGroups.includes("admin")) {
    throw new Error("Unauthorized: Admin privileges required.");
  }
}

/**
 * PLANT OPERATIONS
 */

// Get a single plant by its ID.
async function getPlant(id) {
  const params = {
    TableName: TABLE_NAME,
    Key: {
      PK: `PLANT#${id}`,
      SK: `METADATA#${id}`,
    },
  };
  const command = new GetCommand(params);
  const result = await ddbClient.send(command);
  return result.Item;
}

// List plants using a GSI that indexes items by `entityType`.
// Optional filters (e.g., species, locationBuilding) are applied as filters.
async function listPlants({ limit, nextToken, species, locationBuilding }) {
  let params = {
    TableName: TABLE_NAME,
    IndexName: "GSI1",
    KeyConditionExpression: "entityType = :plantType",
    ExpressionAttributeValues: {
      ":plantType": "PLANT",
    },
    Limit: limit || 10,
  };

  // Build a FilterExpression if additional filters are provided.
  let filterExpressions = [];
  if (species) {
    filterExpressions.push("species = :species");
    params.ExpressionAttributeValues[":species"] = species;
  }
  if (locationBuilding) {
    filterExpressions.push("location.building = :building");
    params.ExpressionAttributeValues[":building"] = locationBuilding;
  }
  if (filterExpressions.length > 0) {
    params.FilterExpression = filterExpressions.join(" AND ");
  }

  // Decode nextToken if provided.
  if (nextToken) {
    params.ExclusiveStartKey = JSON.parse(Buffer.from(nextToken, 'base64').toString('ascii'));
  }

  const command = new QueryCommand(params);
  const result = await ddbClient.send(command);
  const newNextToken = result.LastEvaluatedKey
    ? Buffer.from(JSON.stringify(result.LastEvaluatedKey)).toString('base64')
    : null;
  return {
    items: result.Items,
    nextToken: newNextToken,
  };
}

// Create a new plant record (admin-only).
async function createPlant(args, userGroups) {
  checkAdmin(userGroups);
  const id = uuidv4();
  const now = new Date().toISOString();

  // Construct the plant item. Note the use of a PK/SK pattern and an entityType attribute.
  const item = {
    PK: `PLANT#${id}`,
    SK: `METADATA#${id}`,
    entityType: "PLANT",
    id,
    species: args.species,
    commonName: args.commonName,
    location: args.location, // expects an object matching LocationInput
    status: args.status,
    details: args.details,
    createdAt: now,
    updatedAt: now,
    maintenanceActions: [], // initially an empty array
  };

  const command = new PutCommand({
    TableName: TABLE_NAME,
    Item: item,
  });
  await ddbClient.send(command);
  return item;
}

// Update an existing plant record (admin-only). Only supplied fields are updated.
async function updatePlant(args, userGroups) {
  checkAdmin(userGroups);
  const { id, ...fields } = args;
  if (Object.keys(fields).length === 0) {
    throw new Error("No fields provided for update.");
  }
  // Build the UpdateExpression dynamically.
  let updateExpression = "set ";
  const ExpressionAttributeValues = {};
  const ExpressionAttributeNames = {};
  let prefix = "";

  for (const key in fields) {
    if (fields[key] !== undefined) {
      updateExpression += `${prefix}#${key} = :${key}`;
      ExpressionAttributeValues[`:${key}`] = fields[key];
      ExpressionAttributeNames[`#${key}`] = key;
      prefix = ", ";
    }
  }
  // Always update the updatedAt timestamp.
  updateExpression += ", #updatedAt = :updatedAt";
  ExpressionAttributeValues[":updatedAt"] = new Date().toISOString();
  ExpressionAttributeNames["#updatedAt"] = "updatedAt";

  const params = {
    TableName: TABLE_NAME,
    Key: {
      PK: `PLANT#${id}`,
      SK: `METADATA#${id}`,
    },
    UpdateExpression: updateExpression,
    ExpressionAttributeValues,
    ExpressionAttributeNames,
    ReturnValues: "ALL_NEW",
  };

  const command = new UpdateCommand(params);
  const result = await ddbClient.send(command);
  return result.Attributes;
}

// Delete a plant record (admin-only).
async function deletePlant({ id }, userGroups) {
  checkAdmin(userGroups);
  const params = {
    TableName: TABLE_NAME,
    Key: {
      PK: `PLANT#${id}`,
      SK: `METADATA#${id}`,
    },
    ReturnValues: "ALL_OLD",
  };
  const command = new DeleteCommand(params);
  const result = await ddbClient.send(command);
  return result.Attributes;
}

/**
 * MAINTENANCE ACTION OPERATIONS
 */

// Get a single maintenance action by its ID.
async function getMaintenanceAction(id) {
  const params = {
    TableName: TABLE_NAME,
    Key: {
      PK: `MAINTENANCE#${id}`,
      SK: `METADATA#${id}`,
    },
  };
  const command = new GetCommand(params);
  const result = await ddbClient.send(command);
  return result.Item;
}

// List maintenance actions. If a plantId is provided, filter for actions related to that plant.
async function listMaintenanceActions({ plantId, limit, nextToken }) {
  let params = {
    TableName: TABLE_NAME,
    IndexName: "GSI1",
    KeyConditionExpression: "entityType = :maintenanceType",
    ExpressionAttributeValues: {
      ":maintenanceType": "MAINTENANCE_ACTION",
    },
    Limit: limit || 10,
  };

  if (plantId) {
    // Apply a filter to return only maintenance actions for a specific plant.
    params.FilterExpression = "plantId = :plantId";
    params.ExpressionAttributeValues[":plantId"] = plantId;
  }
  if (nextToken) {
    params.ExclusiveStartKey = JSON.parse(Buffer.from(nextToken, 'base64').toString('ascii'));
  }
  const command = new QueryCommand(params);
  const result = await ddbClient.send(command);
  const newNextToken = result.LastEvaluatedKey
    ? Buffer.from(JSON.stringify(result.LastEvaluatedKey)).toString('base64')
    : null;
  return {
    items: result.Items,
    nextToken: newNextToken,
  };
}

// Create a new maintenance action (admin-only).
async function createMaintenanceAction(args, userGroups) {
  checkAdmin(userGroups);
  const id = uuidv4();
  const now = new Date().toISOString();

  // Construct the maintenance action item.
  const item = {
    PK: `MAINTENANCE#${id}`,
    SK: `METADATA#${id}`,
    entityType: "MAINTENANCE_ACTION",
    id,
    plantId: args.plantId,
    actionType: args.actionType,
    scheduledAt: args.scheduledAt,
    completedAt: null,
    status: "Scheduled",
    notes: args.notes,
    createdAt: now,
    updatedAt: now,
  };

  const command = new PutCommand({
    TableName: TABLE_NAME,
    Item: item,
  });
  await ddbClient.send(command);
  return item;
}

// Update an existing maintenance action (admin-only).
async function updateMaintenanceAction(args, userGroups) {
  checkAdmin(userGroups);
  const { id, ...fields } = args;
  if (Object.keys(fields).length === 0) {
    throw new Error("No fields provided for update.");
  }

  let updateExpression = "set ";
  const ExpressionAttributeValues = {};
  const ExpressionAttributeNames = {};
  let prefix = "";

  for (const key in fields) {
    if (fields[key] !== undefined) {
      updateExpression += `${prefix}#${key} = :${key}`;
      ExpressionAttributeValues[`:${key}`] = fields[key];
      ExpressionAttributeNames[`#${key}`] = key;
      prefix = ", ";
    }
  }
  updateExpression += ", #updatedAt = :updatedAt";
  ExpressionAttributeValues[":updatedAt"] = new Date().toISOString();
  ExpressionAttributeNames["#updatedAt"] = "updatedAt";

  const params = {
    TableName: TABLE_NAME,
    Key: {
      PK: `MAINTENANCE#${id}`,
      SK: `METADATA#${id}`,
    },
    UpdateExpression: updateExpression,
    ExpressionAttributeValues,
    ExpressionAttributeNames,
    ReturnValues: "ALL_NEW",
  };

  const command = new UpdateCommand(params);
  const result = await ddbClient.send(command);
  return result.Attributes;
}

// Delete a maintenance action (admin-only).
async function deleteMaintenanceAction({ id }, userGroups) {
  checkAdmin(userGroups);
  const params = {
    TableName: TABLE_NAME,
    Key: {
      PK: `MAINTENANCE#${id}`,
      SK: `METADATA#${id}`,
    },
    ReturnValues: "ALL_OLD",
  };
  const command = new DeleteCommand(params);
  const result = await ddbClient.send(command);
  return result.Attributes;
}

/**
 * LAMBDA HANDLER
 *
 * This handler acts as a unified resolver for AppSync.
 * It checks the fieldName provided by AppSync in the event and routes the request
 * to the corresponding function. Read operations are available to all users,
 * while create, update, and delete operations require admin privileges.
 */
exports.handler = async (event) => {
  try {
    // Extract user groups from the identity context (assumed to be provided by AppSync).
    const userGroups = event.identity?.groups || [];
    const fieldName = event.info.fieldName;
    const args = event.arguments;

    switch (fieldName) {
      // Plant operations
      case "getPlant":
        return await getPlant(args.id);
      case "listPlants":
        return await listPlants(args);
      case "createPlant":
        return await createPlant(args, userGroups);
      case "updatePlant":
        return await updatePlant(args, userGroups);
      case "deletePlant":
        return await deletePlant(args, userGroups);

      // MaintenanceAction operations
      case "getMaintenanceAction":
        return await getMaintenanceAction(args.id);
      case "listMaintenanceActions":
        return await listMaintenanceActions(args);
      case "createMaintenanceAction":
        return await createMaintenanceAction(args, userGroups);
      case "updateMaintenanceAction":
        return await updateMaintenanceAction(args, userGroups);
      case "deleteMaintenanceAction":
        return await deleteMaintenanceAction(args, userGroups);

      default:
        throw new Error(`Unknown field: ${fieldName}`);
    }
  } catch (error) {
    console.error("Error processing request:", error);
    throw error;
  }
};
