# Prompt for Integrating a SOAP Interface

## Context  
The implementation should interact with an external SOAP-based web service, process the response, and store the relevant data in AWS DynamoDB. The system should be built using Node.js and be deployable as an AWS Lambda function. It must be optimized for serverless execution, ensuring efficient handling of network requests and database operations.

### Key Requirements:
- Authenticate with the SOAP service using WS-Security headers.
- Build and send a SOAP request to fetch structured data (e.g., project activities).
- Parse and process the XML response into a structured JSON format.
- Handle missing or null values within the XML response.
- Map the processed data to a DynamoDB-compatible schema.
- Store the retrieved data efficiently in AWS DynamoDB.

---

## Component Requirements  

### 1. External API Integration (SOAP Web Service)  
- The system must authenticate with the SOAP service using WS-Security headers (username/password, timestamp, nonce, etc.).  
- The SOAP request should include necessary fields and filters.  
- The request must be dynamically generated based on input parameters (e.g., `projectObjectId`).  
- The response from the SOAP service should be parsed from XML to JSON.  

### 2. Data Processing  
- The XML response should be converted into a structured JSON format.  
- The implementation must handle potential null values in XML and replace them appropriately.  
- The response fields should be mapped to a defined schema.  

### 3. AWS DynamoDB Storage  
- The processed data must be stored in a DynamoDB table.  
- The implementation should use the AWS SDK v3 and `@aws-sdk/lib-dynamodb` to interact with DynamoDB.  
- Each data record should be assigned a unique key structure (`PK`, `SK`) for efficient retrieval.  
- The data schema should include required fields, type mappings, and status transformations.  

### 4. Error Handling and Logging  
- The implementation should include robust error handling for network failures, SOAP faults, and AWS SDK errors.  
- Logging should be provided at key steps (e.g., request initiation, response parsing, database writes).  

---

## Implementation Plan  

### 1. Setup AWS SDK and SOAP Client  
- Import necessary AWS SDK modules (`DynamoDBClient`, `DynamoDBDocumentClient`).  
- Import `axios` for making HTTP requests.  
- Use `xml2js` to parse XML responses.  

### 2. Build SOAP Request  
- Create a function to generate the SOAP request body dynamically.  
- Ensure the request includes WS-Security authentication headers.  

### 3. Send SOAP Request and Parse Response  
- Make an HTTP `POST` request using `axios`.  
- Parse the XML response into JSON.  
- Extract relevant fields from the response.  

### 4. Data Transformation  
- Replace `xsi:nil` values with `null`.  
- Convert date fields to ISO format.  
- Map response fields to a structured format.  

### 5. Store Data in DynamoDB  
- Create a function to map the structured data to a DynamoDB-compatible schema.  
- Store the data using the `PutCommand` from `@aws-sdk/lib-dynamodb`.  

### 6. Error Handling and Logging  
- Implement `try/catch` blocks around API requests and database writes.  
- Log errors and successes for traceability.  

### 7. Deploy and Test  
- Deploy the implementation in an AWS Lambda function.  
- Test with real SOAP service responses and DynamoDB storage.  

---

## Custom Specifications (User-Defined Details)  
Use this section to define the specific requirements of your implementation.  
