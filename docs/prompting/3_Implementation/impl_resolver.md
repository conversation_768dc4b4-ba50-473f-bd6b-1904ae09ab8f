# AWS Lambda Function for AppSync Resolver with DynamoDB

## Context
You need to generate an AWS Lambda function that will serve as an AppSync resolver for interacting with a DynamoDB table. The function will support CRUD operations for a specific entity and implement access control based on user roles.

The schema and queries are defined in `schema.graphql` and `queries.graphql`. The Lambda function should adhere to best practices, including:
- Proper authorization checks
- Structured database access patterns
- Efficient handling of AWS SDK commands

## Component Requirements

### 1. Entity Management
- Implement CRUD operations (Create, Read, Update, Delete) for an entity.
- Store and retrieve data using DynamoDB.
- Ensure primary keys follow the PK/SK pattern (e.g., `PK: ENTITY#<id>`, `SK: METADATA#<id>`).

### 2. Authorization & Access Control
- Use `userGroups` to determine user permissions.
- Define `admin`, `manager`, and `operator` roles for access control.
- Restrict certain operations (e.g., create, update, delete) to admin users only.

### 3. AWS Services Integration
- Use AWS SDK for JavaScript (v3) (`@aws-sdk/lib-dynamodb`).
- Utilize `GetCommand`, `QueryCommand`, `PutCommand`, `UpdateCommand`, and `DeleteCommand`.

### 4. Performance & Security
- Implement efficient queries using DynamoDB indexes where applicable.
- Ensure minimal permissions are granted per operation.
- Validate inputs before executing updates.

## Implementation Plan

### 1. Read Operations
- Implement a `getEntity` function to fetch a single entity by ID.
- Implement a `listEntities` function to query all entities of a specific type.

### 2. Create Operation
Implement an `addEntity` function that:
- Generates a new UUID.
- Constructs and inserts a new entity item.
- Enforces access control (e.g., only admins can create).

### 3. Update Operation
Implement an `updateEntity` function that:
- Dynamically builds an `UpdateExpression` for modified fields.
- Ensures only authorized users can update.

### 4. Delete Operation
Implement a `deleteEntity` function that:
- Removes the item from DynamoDB.
- Restricts access to admin users.

### 5. Modularization & Export
- Export functions in a structured module.
- Use helper utilities for role checking.

## Example Request
Given the `schema.graphql` and `queries.graphql`, derive an AWS Lambda function that:
- Supports CRUD operations.
- Ensures role-based access control.
- Uses DynamoDB’s PK/SK model.
- Leverages AWS SDK for JavaScript v3.

Ensure the function is **modular, scalable**, and adheres to **best practices** for AWS AppSync Lambda resolvers.

## Output Handling
Write resolver file to backend/labmda/appsync-resolver

## Custom Specifications (User-Defined Details)
Use this section to define the specific requirements of your implementation.
