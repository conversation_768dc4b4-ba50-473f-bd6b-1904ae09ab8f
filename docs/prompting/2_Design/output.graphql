# Custom AWS scalar types
scalar AWSDateTime
scalar AWSEmail
scalar AWSPhone
scalar AWSURL
scalar AWSJSON

# Enums for predefined sets
enum MaintenanceType {
  WATERING
  FERTILIZING
  PRUNING
  OTHER
}

enum PlantStatus {
  HEALTHY
  NEEDS_WATER
  NEEDS_FERTILIZER
  CRITICAL
}

# A type to represent physical location details
type Location {
  building: String!
  floor: String
  room: String
  mapUrl: AWSURL
}

# Primary entity for plant details
type Plant {
  id: ID!
  species: String!
  commonName: String
  location: Location!
  status: PlantStatus!
  details: AWSJSON         # Additional information as JSON
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
  maintenanceActions: [MaintenanceAction!]!  # One-to-many relationship with <PERSON>A<PERSON>
}

# Entity to track maintenance actions for a plant
type MaintenanceAction {
  id: ID!
  plantId: ID!
  plant: Plant              # Reference back to the associated plant
  actionType: MaintenanceType!
  scheduledAt: AWSDateTime!
  completedAt: AWSDateTime
  status: String            # E.g., "Scheduled", "Completed"
  notes: String
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

# Query operations for fetching data
type Query {
  getPlant(id: ID!): Plant
  listPlants(
    limit: Int,
    nextToken: String,
    species: String,
    locationBuilding: String
  ): PlantConnection
  getMaintenanceAction(id: ID!): MaintenanceAction
  listMaintenanceActions(
    plantId: ID,
    limit: Int,
    nextToken: String
  ): MaintenanceActionConnection
}

# Mutation operations for creating, updating, and deleting records
type Mutation {
  createPlant(
    species: String!,
    commonName: String,
    location: LocationInput!,
    status: PlantStatus!,
    details: AWSJSON
  ): Plant
  updatePlant(
    id: ID!,
    species: String,
    commonName: String,
    location: LocationInput,
    status: PlantStatus,
    details: AWSJSON
  ): Plant
  deletePlant(id: ID!): Plant

  createMaintenanceAction(
    plantId: ID!,
    actionType: MaintenanceType!,
    scheduledAt: AWSDateTime!,
    notes: String
  ): MaintenanceAction
  updateMaintenanceAction(
    id: ID!,
    actionType: MaintenanceType,
    scheduledAt: AWSDateTime,
    completedAt: AWSDateTime,
    status: String,
    notes: String
  ): MaintenanceAction
  deleteMaintenanceAction(id: ID!): MaintenanceAction
}

# Subscription operations for real-time updates
type Subscription {
  onPlantCreated: Plant
  onPlantUpdated: Plant
  onMaintenanceActionCreated(plantId: ID): MaintenanceAction
  onMaintenanceActionUpdated(plantId: ID): MaintenanceAction
}

# Connection types to support pagination in list queries
type PlantConnection {
  items: [Plant]
  nextToken: String
}

type MaintenanceActionConnection {
  items: [MaintenanceAction]
  nextToken: String
}

# Input type for location data used in mutations
input LocationInput {
  building: String!
  floor: String
  room: String
  mapUrl: AWSURL
}

# Root schema definition
schema {
  query: Query
  mutation: Mutation
  subscription: Subscription
}
