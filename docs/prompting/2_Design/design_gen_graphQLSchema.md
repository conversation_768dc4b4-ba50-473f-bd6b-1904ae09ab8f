# Prompt for Generating a GraphQL Schema

## Context
We are designing a GraphQL schema for an AWS AppSync API that interacts with Amazon DynamoDB as the primary data source. The schema should follow AWS best practices for GraphQL, using AWS-specific scalar types (e.g., `AWSDateTime`, `AWSEmail`, `AWSPhone`, `AWSURL`, `AWSJSON`).

The API will support a set of functional requirements, which define the entities, relationships, and operations (queries, mutations, subscriptions) needed.

## Component Requirements

1. **Entities & Fields**  
   - Define the primary data models based on the functional requirements.  
   - Each entity should use AWS data types where applicable.

2. **Relationships**  
   - Implement one-to-one, one-to-many, and many-to-many relationships as necessary.

3. **Operations**  
   - **Queries**: Fetch single and multiple entities based on various filter criteria.  
   - **Mutations**: Allow CRUD operations on entities.  
   - **Subscriptions**: Define relevant real-time updates where needed.

4. **Authorization & Identity Management**  
   - Define authentication and authorization rules using AWS Cognito User Pools, IAM, or API Keys.  
   - Implement fine-grained access control with AWS AppSync authorization directives.

## Implementation Plan

1. **Define the GraphQL Schema**  
   - Use the provided functional requirements to create types and relationships.  
   - Use GraphQL directives such as `@model`, `@auth`, and `@connection` to integrate with DynamoDB.

2. **Set Up Queries, Mutations & Subscriptions**  
   - Create efficient query patterns, leveraging DynamoDB indexes for optimized retrieval.  
   - Implement input validation using AWS scalar types.

3. **Authorization Strategy**  
   - Do not apply `@auth` rules for role-based access control (RBAC).  
   - Ensure data access security for multi-tenant or user-specific data.

4. **Scalability Considerations**  
   - Optimize schema design for high-read / high-write patterns in DynamoDB.  
   - Implement pagination where necessary using `limit` & `nextToken`.

## Handle output file
Write the resulting output to <outputFile>.graphql

## Custom Specifications (User-Defined Details)
Use this section to define the specific requirements of your implementation.