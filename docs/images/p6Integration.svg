<svg xmlns="http://www.w3.org/2000/svg" style="background: transparent; background-color: transparent; color-scheme: light dark;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="771px" height="271px" viewBox="-0.5 -0.5 771 271" content="&lt;mxfile&gt;&lt;diagram name=&quot;backend&quot; id=&quot;6JOup65e_AeVQfz4VQyZ&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;9qKIk-H6OMXjcmAIqt9d&quot; name=&quot;stepfunctions&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;RMC2B6ZuDkgfB3q3Y9I0&quot; name=&quot;Auth&quot;&gt;7Vldc5s4FP01nuk+1APCGOfRX8mm2+12ms5089SRQQa1gKgQscmv771Gso1F42S22bhpEsfRPfrmXJ0riZ43zdYXkhbJ3yJiaY840brnzXqEuO5oBP8QqRtkODhrgFjySBfaAVf8lmnQ0WjFI1a2CiohUsWLNhiKPGehamFUSrFqF1uKtN1rQWNmAVchTW30E49U0qAj39nhfzIeJ6Zn19E5GTWFNVAmNBKrPcib95xxD9PHPt5UCqEeUKFdOVtPWYq0mCfejOj85zW4fUqS5eqR+ri9XuX+F/nxCw8i5/Kvf+j169Vr3fENTStN2FTEOVeiR4YpjGSykJCKMbXiQAhxLqSowHcaSlRteF4lXLGrgoZor8CXoUaishQsF5K6GyYVW/9wyu7DWL1r7hdMZEzJGmzdn2dWhF5IA22udl7pGldL9jxyqDGqF0K8bfk/0QN1NUOPS/DAIjhm6rMSX1n+6g+LRRbB4tVmLnLWJhGIkvW/YDjGuEaj7xtztt7PnNXaarrBtu+mHoYiKhmyO6ZDtIRRCdM45tc/3ZUkS6niN+1pnL5jkPMyYze35PyDF3iOEPLsTd6x8qsiooqNQ5ghVzV6h6PX/Ed0lwc6yxOQPnwh/YgaEIt0i1UIswUmaaiERNk/dWE3Qq51nZgNxJ6wmyLPSNd9i0m9SJ0EZoS7wSZMw44NIzjNkLd8URYbzg5jO1uzsEK+CUwA9oJZQfO6MSJehrxIOazvB8b8UkkY0lSk6EdGIZ7eXw72ASSw/eUX3wh06r1nOYxkpUiBiacT9qEt7J1jJy/CfkQOhha746K4qvMQwPH7S1vlvzIVJnp/Vgieq828/Ql8oONp8+fPUAMA6RO/A+zCAht07WLwz+3q4RDswgIbdO1iaJlRt8EuLPDtER/Wdjtquwe14eNNRKVQLqfbEzU+46XIlZHCHvHg9xw8AnCepnv4PHD9YGKJJ+QsNz+QE9EyYZFuFoWUw2n7LV2w9L0oueIih7yFUEpkewXGKY8xQwlUaaqtEJYsk+31jSPV9wguMbb2HOySlkUzrSVf4zgmZseQrWO8w+jTVTnoo7jg6r8McTwTMJtUuxQtihKd9BTigmdEXwcG94xYgSEY2XHBYKevHJ3aaitH+xzwv0SHH6v+PaKD/xIdjnB8n23/S0A4zYAwC/BK9HcJCCnNFhE9iXjg/57xwL4wfCtiDiEau7xcoiC3DppSgCMQZ749QTrjd7NNn8NvFV66T7bHSQPgw1H4tQ0xDvtW0RSb2xbenWCb9vAcqo+hTdZ+rWd3SB0MvL7fPqeeOZb7Ec99dudU+2LDorRTTx9bBTtEzetHdU4z8Tla2MJN5oE3GQEOxSPOdmJ/Mj4WHNyEdAocsR1s9GtvhgL7qFxtbrkXtAQHIo5AcdvcpvUh8U7Aly7B86WQGd04E1o9fC+LHhAt+s9EeIg/OBCeQcebslMXnk2te7zz1a3vvXv35t8B&lt;/diagram&gt;&lt;diagram id=&quot;1KslJCno1vFf9DffpOd1&quot; name=&quot;p6integration&quot;&gt;7Vlbc+I2FP41zLQPZSwbY3gMt23atGWancn2iRG2MEpki5HFrb9+j2wZsCUIm4036TTAJNZnSUc65zsXyy1vmOw+Cbxa/sEjwlquE+1a3qjlusjzevBPIfsC8btOAcSCRrrTEbin/xINlt3WNCJZpaPknEm6qoIhT1MSygqGheDbarcFZ1WpKxwTA7gPMTPRBxrJZYH2fOeI/0povCwlI0ffSXDZWQPZEkd8ewJ545Y3FJzL4irZDQlTyiv1UoybnLl7WJggqbxmwD+/+58XXwI+fRx8ih5Hvw16t3/94hezbDBb6w1/vhl/AeTm4V79DUO+humLDch9qZUVp6nMNesP4AeCh07LhztD1Wq7fg2ot4MqgMyWmqMK1NtBFUD16VFNPqov8AQwWpXpnZp852SB8PMGfC0ZTcnwwEEHwFjgiIJthpxxAVjKU9DeYCkTBi0El9slleR+hUOl1S34D2ALnkrtBcgt21rxalbgucQgS+g5cksQMd6QwiBFH8bwKqPzwyhBwrXI6Ib8TbJicoUCI1fqOtnFynnbeJt12rHg61W+/FuQZb07w5oWMIUU/ImUW2y53nDkut2OWjhlrLb1DRGSgmvdMBqrmSVXgrBuMbLIZwR90DS+y1sjz9E6sImIcLYkkd6MJjKIILuzHoIOfgcBi/CESLGHLnqA52lX3ZfRS7e3R8/vlPFreeL1bqBBrKNNfJj76JBwoX3yG/zTM/zT8MXsichwqbXwA5n4YvZsVqGNOb2gj/rdRplzEPHtzDkfPc/Sya2yyTXJ5FrJ5PgNkalrkGmcRnkAeQGpaqqF70Qt5RzZTo2qLDHsTCYTgwa6c8U8pe3v8JywKc+opDmz5lxKnjxLjpCo+Fil+3PUxtmq2OiC7tQ6jCiLzvGfaIVmDfKqHqUsxLLxqttUjELI4NVCsWcq+COoMfvpZ4NeJIIiSzfNaFR0V31eoDaQw9ciJBf66RgqsYjJpfm6djMIwrCEXFqtNV9dqx1Dq1sBUfo/r1XkvaVaXUOr0+4t+Db4cB5XLgZCa9lrLX1t5a+1BDbL4Eq3vDC1SKiDNiwwQWR2K2tZE7RhtsK9PhpZRqPa6PNl87m8Uksg41GgHrUsdcQi/7zHLGLNGIIUnlXUS1CpZ7bKieFkHuEGkwrqv7esYpa+o32KEz6aG05qJVLT5rdY02tH+RJn0dxkrDsOvEHvfIXUWLng1yzr9Nu+YdvANW3bayy39d5Z7kL+lcnLfdOSwDytyStSgAaCKuW8X8cgaqEzSRMym+MsD4c1/wj8/vDSE0RT/uE6nTyDnbiI5bnfQ6XTnHpIx23I1D2zTBE0wbB7/HE493E49z88nHOfP5uzH6c0djZXvqJoPIuV2fK5LBZcmcT6b5nEAiOy/Xk3uPzY9XH+dPX5U0rklounGeM4gkTLcBrmwl+ePHvf/djg902vLLFXp1f/UuLsMhXE5gKuYnX1QOYZERsKXmXcu5neGqyE+JpGB2LYMoTh7a+v8nogtGj8gP2YJzXzncR3x0F0ZcjrXhnygmZCHjSPL3Dzeyevwb3xVw==&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><g><path d="M 310 0 L 770 0 L 770 270 L 310 270 Z" fill="none" stroke="#cd2264" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(205, 34, 100), rgb(255, 137, 194));"/><path d="M 310 0 L 310 25 L 335 25 L 335 0 L 310 0 Z M 314.09 3.69 L 330.91 3.69 C 331.01 3.69 331.12 3.73 331.19 3.81 C 331.27 3.88 331.31 3.99 331.31 4.09 L 331.31 20.91 C 331.31 21.01 331.27 21.12 331.19 21.19 C 331.12 21.27 331.01 21.31 330.91 21.31 L 314.09 21.31 C 313.99 21.31 313.88 21.27 313.81 21.19 C 313.73 21.12 313.69 21.01 313.69 20.91 L 313.69 4.09 C 313.69 3.99 313.73 3.88 313.81 3.81 C 313.88 3.73 313.99 3.69 314.09 3.69 Z M 314.49 4.49 L 314.49 20.51 L 330.51 20.51 L 330.51 4.49 L 314.49 4.49 Z M 326.5 5.31 C 326.65 5.31 326.79 5.38 326.86 5.51 L 329.46 10.72 C 329.53 10.84 329.52 10.99 329.45 11.11 C 329.37 11.23 329.25 11.3 329.11 11.3 L 323.9 11.3 L 323.9 11.3 C 323.76 11.3 323.63 11.23 323.56 11.11 C 323.49 10.99 323.48 10.84 323.54 10.72 L 326.14 5.51 C 326.21 5.38 326.36 5.31 326.5 5.31 Z M 326.5 6.59 L 324.55 10.5 L 328.46 10.5 L 326.5 6.59 Z M 316.09 9.3 L 321.3 9.3 C 321.41 9.3 321.51 9.34 321.58 9.41 C 321.66 9.49 321.7 9.59 321.7 9.7 L 321.7 14.9 C 321.7 15.01 321.66 15.11 321.58 15.19 C 321.51 15.26 321.41 15.3 321.3 15.3 L 316.09 15.3 C 315.99 15.3 315.89 15.26 315.81 15.19 C 315.74 15.11 315.69 15.01 315.69 14.9 L 315.69 9.7 C 315.69 9.59 315.74 9.49 315.81 9.41 C 315.89 9.34 315.99 9.3 316.09 9.3 Z M 316.49 10.1 L 316.49 14.5 L 320.9 14.5 L 320.9 10.1 L 316.49 10.1 Z M 325.7 13.3 C 327.47 13.3 328.9 14.74 328.91 16.5 C 328.9 18.27 327.47 19.7 325.7 19.71 C 323.94 19.7 322.5 18.27 322.5 16.5 C 322.5 14.74 323.94 13.3 325.7 13.3 Z M 325.7 14.1 C 324.38 14.1 323.3 15.18 323.3 16.5 C 323.3 17.83 324.38 18.9 325.7 18.91 C 327.03 18.9 328.1 17.83 328.1 16.5 C 328.1 15.18 327.03 14.1 325.7 14.1 Z" fill="#cd2264" stroke="none" pointer-events="all" style="fill: light-dark(rgb(205, 34, 100), rgb(255, 137, 194));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 428px; height: 1px; padding-top: 7px; margin-left: 342px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #CD2264; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#CD2264, #ff89c2); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">TAEX AWS Account</div></div></div></foreignObject><text x="342" y="19" fill="#CD2264" font-family="&quot;Helvetica&quot;" font-size="12px">TAEX AWS Account</text></switch></g></g><g><path d="M 330 20 L 590 20 L 590 225 L 330 225 Z" fill="none" stroke="#879196" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(135, 145, 150), rgb(106, 115, 119));"/><path d="M 340.59 26.65 C 340.53 26.65 340.48 26.65 340.42 26.65 L 340.42 26.65 C 339.11 26.68 338.03 27.24 337.14 28.25 C 337.13 28.25 337.13 28.25 337.13 28.25 C 336.2 29.36 335.87 30.52 335.96 31.73 C 334.81 32.06 334.12 32.92 333.76 33.74 C 333.75 33.75 333.75 33.76 333.74 33.78 C 333.33 35.05 333.68 36.36 334.24 37.16 C 334.25 37.17 334.25 37.17 334.26 37.18 C 334.94 38.05 335.97 38.53 337.02 38.53 L 348.17 38.53 C 349.19 38.53 350.07 38.16 350.8 37.37 C 351.25 36.94 351.49 36.29 351.58 35.59 C 351.67 34.9 351.61 34.16 351.32 33.55 C 351.31 33.54 351.31 33.53 351.31 33.52 C 350.8 32.62 349.95 31.81 348.76 31.64 C 348.74 30.79 348.28 29.99 347.68 29.56 C 347.67 29.55 347.66 29.55 347.65 29.54 C 347.01 29.18 346.4 29.14 345.91 29.3 C 345.6 29.4 345.36 29.56 345.14 29.74 C 344.51 28.36 343.43 27.18 341.81 26.79 C 341.81 26.79 341.81 26.79 341.81 26.79 C 341.38 26.7 340.97 26.65 340.59 26.65 Z M 340.43 27.38 C 340.8 27.38 341.2 27.43 341.64 27.53 C 343.16 27.89 344.15 29.07 344.66 30.48 C 344.71 30.6 344.81 30.69 344.94 30.72 C 345.07 30.74 345.2 30.7 345.29 30.61 C 345.54 30.34 345.83 30.11 346.14 30.01 C 346.44 29.91 346.78 29.92 347.26 30.18 C 347.67 30.49 348.11 31.31 348.03 31.9 C 348.01 32.01 348.05 32.12 348.12 32.2 C 348.19 32.28 348.29 32.33 348.39 32.33 C 349.46 32.34 350.16 33.02 350.64 33.88 C 350.85 34.3 350.91 34.92 350.84 35.5 C 350.76 36.07 350.53 36.59 350.28 36.83 C 350.27 36.84 350.27 36.85 350.26 36.85 C 349.65 37.53 349.03 37.78 348.17 37.78 L 337.02 37.78 C 336.2 37.78 335.39 37.41 334.85 36.73 C 334.44 36.13 334.14 35.02 334.46 34.02 C 334.79 33.27 335.36 32.55 336.41 32.36 C 336.6 32.32 336.74 32.14 336.71 31.94 C 336.56 30.79 336.8 29.81 337.7 28.74 C 338.49 27.85 339.33 27.39 340.43 27.38 Z M 342.2 30.7 C 341.77 30.7 341.4 30.93 341.13 31.21 C 340.85 31.5 340.64 31.85 340.64 32.25 L 340.64 32.71 L 340.14 32.71 C 340.04 32.71 339.94 32.75 339.87 32.82 C 339.8 32.89 339.76 32.98 339.76 33.08 L 339.76 35.7 C 339.76 35.8 339.8 35.89 339.87 35.96 C 339.94 36.03 340.04 36.07 340.14 36.07 L 344.16 36.07 C 344.26 36.07 344.35 36.03 344.42 35.96 C 344.49 35.89 344.53 35.8 344.53 35.7 L 344.53 33.08 C 344.53 32.98 344.49 32.89 344.42 32.82 C 344.35 32.75 344.26 32.71 344.16 32.71 L 343.68 32.71 L 343.68 32.25 C 343.68 31.84 343.47 31.47 343.21 31.2 C 342.94 30.92 342.61 30.7 342.2 30.7 Z M 342.2 31.45 C 342.29 31.45 342.5 31.54 342.67 31.72 C 342.83 31.89 342.93 32.11 342.93 32.25 L 342.93 32.71 L 341.39 32.71 L 341.39 32.25 C 341.39 32.15 341.49 31.91 341.66 31.74 C 341.83 31.56 342.06 31.45 342.2 31.45 Z M 340.51 33.46 L 343.78 33.46 L 343.78 35.32 L 340.51 35.32 Z M 330 45 L 330 20 L 355 20 L 355 45 Z" fill="#879196" stroke="none" pointer-events="all" style="fill: light-dark(rgb(135, 145, 150), rgb(106, 115, 119));"/></g><g><rect x="340" y="120" width="60" height="60" fill="none" stroke="none" pointer-events="all"/><path d="M 365.45 155 L 367.39 156.93 L 373.35 150.97 C 373.89 150.43 373.89 149.57 373.35 149.03 L 367.39 143.07 L 365.45 145 L 369.09 148.63 L 346.14 148.63 L 346.14 151.37 L 369.09 151.37 Z M 397.27 150 C 397.27 134.97 385.03 122.73 370 122.73 C 354.97 122.73 342.73 134.97 342.73 150 C 342.73 165.03 354.97 177.27 370 177.27 C 385.03 177.27 397.27 165.03 397.27 150 Z M 400 150 C 400 166.54 386.54 180 370 180 C 353.46 180 340 166.54 340 150 C 340 133.46 353.46 120 370 120 C 386.54 120 400 133.46 400 150 Z M 359.09 153.58 L 359.09 154.2 C 359.09 154.28 359.1 154.36 359.11 154.44 C 359.2 154.92 361.32 166.15 370 166.15 C 378.68 166.15 380.8 154.92 380.89 154.44 C 380.9 154.36 380.91 154.28 380.91 154.2 L 380.91 139.78 C 380.91 139.22 380.57 138.72 380.05 138.51 L 370.51 134.67 C 370.18 134.53 369.82 134.53 369.49 134.67 L 359.95 138.51 C 359.43 138.72 359.09 139.22 359.09 139.78 L 359.09 146.42 L 361.82 146.42 L 361.82 140.7 L 370 137.41 L 378.18 140.7 L 378.18 154.06 C 377.96 155.12 376.02 163.41 370 163.41 C 363.98 163.41 362.04 155.12 361.82 154.06 L 361.82 153.58 Z M 385.68 135.48 L 385.68 153.73 C 385.68 159.93 382.4 171.65 370 171.65 C 363.54 171.65 358.78 168.31 356.21 161.99 C 354.84 158.64 354.46 155.34 354.36 153.66 L 357.08 153.49 C 357.16 154.71 357.47 157.86 358.74 160.96 C 360.88 166.24 364.67 168.91 370 168.91 C 382.76 168.91 382.95 154.35 382.95 153.73 L 382.95 136.4 L 370 131.19 L 357.05 136.4 L 357.05 146.42 L 354.32 146.42 L 354.32 135.48 C 354.32 134.92 354.66 134.42 355.17 134.21 L 369.49 128.45 C 369.82 128.32 370.18 128.32 370.51 128.45 L 384.83 134.21 C 385.34 134.42 385.68 134.92 385.68 135.48 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 187px; margin-left: 370px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">Endpoint</div></div></div></foreignObject><text x="370" y="199" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Endpoint</text></switch></g></g><g><path d="M 500 150 L 406.37 150" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 401.12 150 L 408.12 146.5 L 406.37 150 L 408.12 153.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 150px; margin-left: 450px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">fetchProjects()</div></div></div></foreignObject><text x="450" y="153" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">fetchProjects()</text></switch></g></g><g><path d="M 560 150 L 653.63 150" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 658.88 150 L 651.88 153.5 L 653.63 150 L 651.88 146.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 150px; margin-left: 610px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">writeProjects()</div></div></div></foreignObject><text x="610" y="153" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">writeProjects()</text></switch></g></g><g><path d="M 500 120 L 560 120 L 560 180 L 500 180 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/><path d="M 519.74 172.29 L 509.1 172.29 L 520.87 147.68 L 526.2 158.66 Z M 521.64 145.33 C 521.49 145.04 521.19 144.85 520.87 144.85 L 520.86 144.85 C 520.53 144.85 520.23 145.04 520.09 145.34 L 506.96 172.77 C 506.84 173.04 506.85 173.35 507.01 173.6 C 507.17 173.85 507.44 174 507.74 174 L 520.29 174 C 520.62 174 520.92 173.81 521.06 173.51 L 527.93 159.02 C 528.04 158.79 528.04 158.52 527.93 158.28 Z M 551.45 172.29 L 540.87 172.29 L 523.91 136.77 C 523.77 136.48 523.46 136.29 523.13 136.29 L 516.21 136.29 L 516.22 127.71 L 529.78 127.71 L 546.67 163.22 C 546.81 163.52 547.11 163.71 547.44 163.71 L 551.45 163.71 Z M 552.31 162 L 547.99 162 L 531.1 126.49 C 530.96 126.19 530.66 126 530.32 126 L 515.36 126 C 514.89 126 514.5 126.38 514.5 126.86 L 514.49 137.14 C 514.49 137.37 514.58 137.59 514.75 137.75 C 514.91 137.91 515.12 138 515.35 138 L 522.59 138 L 539.56 173.51 C 539.7 173.81 540 174 540.33 174 L 552.31 174 C 552.78 174 553.16 173.62 553.16 173.14 L 553.16 162.86 C 553.16 162.38 552.78 162 552.31 162 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 187px; margin-left: 530px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">P6Integration</div></div></div></foreignObject><text x="530" y="199" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">P6Integrat...</text></switch></g></g><g><path d="M 684.22 190.5 L 667.91 182.51 L 660 172.61 L 660 164.06 L 662.26 163.83 L 662.26 161.69 L 660 160.34 L 660 151.79 L 662.26 151.79 L 662.26 148.19 L 660 148.19 L 660 139.66 L 662.26 138.29 L 662.26 136.17 L 660 135.93 L 660 127.38 L 667.91 117.48 L 684.22 109.5 L 706.8 109.5 L 722.96 117.48 L 732 127.38 L 732 135.93 L 728.96 136.15 L 728.96 138.17 L 732 139.75 L 732 148.19 L 728.96 148.19 L 728.96 151.79 L 732 151.79 L 732 160.46 L 728.96 161.92 L 728.96 163.83 L 732 164.06 L 732 172.61 L 722.96 182.51 L 706.8 190.5 Z" fill="#2e73b8" stroke="none" pointer-events="all" style="fill: light-dark(rgb(46, 115, 184), rgb(95, 155, 214));"/><path d="M 684.22 124.45 L 667.91 129.87 L 667.91 117.48 L 684.22 109.5 Z M 684.22 146.95 L 667.91 147.4 L 667.91 135.07 L 684.22 131.2 Z M 684.22 168.33 L 667.91 164.72 L 667.91 152.59 L 684.22 153.71 Z M 684.22 190.5 L 667.91 182.51 L 667.91 170.12 L 684.22 175.08 Z" fill-opacity="0.3" fill="#000000" stroke="none" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));"/><path d="M 732 164.06 L 722.96 170.13 L 706.8 175.08 L 684.22 175.08 L 667.91 170.12 L 667.91 182.51 L 660 172.61 L 660 164.06 L 662.26 163.83 L 662.26 161.69 L 660 160.34 L 660 151.79 L 662.26 151.79 L 662.26 148.19 L 660 148.19 L 660 139.66 L 662.26 138.29 L 662.26 136.17 L 660 135.93 L 660 127.38 L 667.91 117.48 L 667.91 129.87 L 684.22 124.45 L 706.8 124.45 L 722.96 129.64 L 732 135.93 L 728.96 136.15 L 728.96 138.17 L 722.96 135.03 L 706.8 131.2 L 684.22 131.2 L 667.91 135.07 L 667.91 147.4 L 684.22 146.95 L 706.8 146.95 L 722.96 147.52 L 732 148.19 L 728.96 148.19 L 728.96 151.79 L 732 151.79 L 722.96 152.59 L 706.8 153.71 L 684.22 153.71 L 667.91 152.59 L 667.91 164.72 L 684.22 168.33 L 706.8 168.33 L 722.96 164.84 L 728.96 161.92 L 728.96 163.83 Z" fill-opacity="0.5" fill="#000000" stroke="none" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));"/><path d="M 706.8 124.45 L 706.8 109.5 L 722.96 117.48 L 722.96 129.64 Z" fill-opacity="0.3" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/><path d="M 706.8 131.2 L 722.96 135.03 L 722.96 147.52 L 706.8 146.95 Z" fill-opacity="0.3" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/><path d="M 706.8 153.71 L 722.96 152.59 L 722.96 164.84 L 706.8 168.33 Z" fill-opacity="0.3" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/><path d="M 706.8 175.08 L 722.96 170.13 L 722.96 182.51 L 706.8 190.5 Z" fill-opacity="0.3" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 198px; margin-left: 696px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">DynamoDb</div></div></div></foreignObject><text x="696" y="210" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">DynamoDb</text></switch></g></g><g><path d="M 530 72 L 530 113.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 530 118.88 L 526.5 111.88 L 530 113.63 L 533.5 111.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><path d="M 545.75 38.26 L 545.74 39.89 C 545.74 44.05 542.4 48.2 537.53 48.2 C 532.17 48.2 529.33 43.5 529.33 40.15 L 529.33 37.84 C 529.33 34.68 532.25 30 537.6 30 C 541.95 30 545.75 33.65 545.75 38.26 Z M 521.37 66.28 L 515.33 66.28 L 515.33 49.6 L 521.37 49.6 Z M 530.99 66.28 L 524.93 66.28 L 524.93 52.57 L 530.99 52.57 Z M 540.57 66.28 L 534.51 66.28 L 534.51 47.76 C 536.12 48.68 538.47 48.93 540.57 47.76 Z M 541.67 72 L 514.25 72 L 514.25 67.29 L 541.67 67.29 Z" fill="#759c3e" stroke="none" pointer-events="all" style="fill: light-dark(rgb(117, 156, 62), rgb(95, 129, 48));"/><path d="M 545.75 38.26 L 545.74 39.89 C 545.74 44.05 542.41 48.2 537.53 48.2 C 532.17 48.2 529.33 43.49 529.33 40.15 L 529.33 37.84 C 529.33 43.33 533.16 46.76 537.43 46.76 C 542.49 46.76 545.75 42.6 545.75 38.26 Z M 521.37 66.28 L 515.33 66.28 L 515.33 64.93 L 521.37 64.93 Z M 530.99 66.28 L 524.93 66.28 L 524.93 64.93 L 530.99 64.93 Z M 540.57 66.28 L 534.51 66.28 L 534.51 64.93 L 540.57 64.93 Z M 541.67 72 L 514.25 72 L 514.25 70.57 L 541.67 70.57 Z" fill-opacity="0.3" fill="#000000" stroke="none" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));"/><rect x="514.25" y="30" width="0" height="0" fill="none" stroke="#000000" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 545.75 38.26 C 545.75 42.59 542.49 46.76 537.43 46.76 C 533.16 46.76 529.33 43.33 529.33 37.84 C 529.33 34.68 532.25 30 537.6 30 C 541.95 30 545.75 33.65 545.75 38.26 Z" fill="#4f4f4f" stroke="none" pointer-events="all" style="fill: light-dark(rgb(79, 79, 79), rgb(169, 169, 169));"/><path d="M 534.14 39.83 L 538.96 39.83 L 538.96 33.15 L 537.26 33.15 L 537.26 38.1 L 534.14 38.1 Z M 537.61 31.74 C 541.03 31.74 544.04 34.64 544.04 38.43 C 544.04 42.09 541.11 45.05 537.52 45.05 C 533.97 45.05 531.02 42.19 531.02 38.3 C 531.02 35.31 533.32 31.74 537.61 31.74 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 79px; margin-left: 530px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">Event Bridge</div></div></div></foreignObject><text x="530" y="91" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Event...</text></switch></g></g><g><path d="M 0 0 L 260 0 L 260 270 L 0 270 Z" fill="none" stroke="#cd2264" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(205, 34, 100), rgb(255, 137, 194));"/><path d="M 0 0 L 0 25 L 25 25 L 25 0 L 0 0 Z M 4.09 3.69 L 20.91 3.69 C 21.01 3.69 21.12 3.73 21.19 3.81 C 21.27 3.88 21.31 3.99 21.31 4.09 L 21.31 20.91 C 21.31 21.01 21.27 21.12 21.19 21.19 C 21.12 21.27 21.01 21.31 20.91 21.31 L 4.09 21.31 C 3.99 21.31 3.88 21.27 3.81 21.19 C 3.73 21.12 3.69 21.01 3.69 20.91 L 3.69 4.09 C 3.69 3.99 3.73 3.88 3.81 3.81 C 3.88 3.73 3.99 3.69 4.09 3.69 Z M 4.49 4.49 L 4.49 20.51 L 20.51 20.51 L 20.51 4.49 L 4.49 4.49 Z M 16.5 5.31 C 16.65 5.31 16.79 5.38 16.86 5.51 L 19.46 10.72 C 19.53 10.84 19.52 10.99 19.45 11.11 C 19.37 11.23 19.25 11.3 19.11 11.3 L 13.9 11.3 L 13.9 11.3 C 13.76 11.3 13.63 11.23 13.56 11.11 C 13.49 10.99 13.48 10.84 13.54 10.72 L 16.14 5.51 C 16.21 5.38 16.36 5.31 16.5 5.31 Z M 16.5 6.59 L 14.55 10.5 L 18.46 10.5 L 16.5 6.59 Z M 6.09 9.3 L 11.3 9.3 C 11.41 9.3 11.51 9.34 11.58 9.41 C 11.66 9.49 11.7 9.59 11.7 9.7 L 11.7 14.9 C 11.7 15.01 11.66 15.11 11.58 15.19 C 11.51 15.26 11.41 15.3 11.3 15.3 L 6.09 15.3 C 5.99 15.3 5.89 15.26 5.81 15.19 C 5.74 15.11 5.69 15.01 5.69 14.9 L 5.69 9.7 C 5.69 9.59 5.74 9.49 5.81 9.41 C 5.89 9.34 5.99 9.3 6.09 9.3 Z M 6.49 10.1 L 6.49 14.5 L 10.9 14.5 L 10.9 10.1 L 6.49 10.1 Z M 15.7 13.3 C 17.47 13.3 18.9 14.74 18.91 16.5 C 18.9 18.27 17.47 19.7 15.7 19.71 C 13.94 19.7 12.5 18.27 12.5 16.5 C 12.5 14.74 13.94 13.3 15.7 13.3 Z M 15.7 14.1 C 14.38 14.1 13.3 15.18 13.3 16.5 C 13.3 17.83 14.38 18.9 15.7 18.91 C 17.03 18.9 18.1 17.83 18.1 16.5 C 18.1 15.18 17.03 14.1 15.7 14.1 Z" fill="#cd2264" stroke="none" pointer-events="all" style="fill: light-dark(rgb(205, 34, 100), rgb(255, 137, 194));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 228px; height: 1px; padding-top: 7px; margin-left: 32px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #CD2264; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#CD2264, #ff89c2); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Primavera AWS Account</div></div></div></foreignObject><text x="32" y="19" fill="#CD2264" font-family="&quot;Helvetica&quot;" font-size="12px">Primavera AWS Account</text></switch></g></g><g><path d="M 190 149.61 L 146.37 149.76" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 141.12 149.78 L 148.11 146.26 L 146.37 149.76 L 148.13 153.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><rect x="190" y="120" width="59" height="59" fill="none" stroke="none" pointer-events="all"/><path d="M 235.93 144.64 L 228.89 144.64 C 228.14 144.64 227.55 145.24 227.55 145.98 L 227.55 153.02 C 227.55 153.76 228.14 154.36 228.89 154.36 L 235.93 154.36 C 236.67 154.36 237.27 153.76 237.27 153.02 L 237.27 145.98 C 237.27 145.24 236.67 144.64 235.93 144.64 Z M 230.23 151.68 L 230.23 147.32 L 234.58 147.32 L 234.58 151.68 Z M 225.72 161.77 C 225.91 162.48 225.49 163.22 224.78 163.41 L 219.39 164.87 L 218.69 162.29 L 220.79 161.72 L 211.01 156.55 L 212.26 154.18 L 222.33 159.49 L 221.67 157.08 L 224.26 156.38 Z M 208.82 142.33 L 197.17 142.33 C 196.43 142.33 195.83 142.93 195.83 143.67 L 195.83 155.33 C 195.83 156.07 196.43 156.67 197.17 156.67 L 208.82 156.67 C 209.56 156.67 210.16 156.07 210.16 155.33 L 210.16 143.67 C 210.16 142.93 209.56 142.33 208.82 142.33 Z M 198.51 153.99 L 198.51 145.02 L 207.48 145.02 L 207.48 153.99 Z M 235.93 131.73 L 228.89 131.73 C 228.14 131.73 227.55 132.33 227.55 133.07 L 227.55 140.11 C 227.55 140.86 228.14 141.45 228.89 141.45 L 235.93 141.45 C 236.67 141.45 237.27 140.86 237.27 140.11 L 237.27 133.07 C 237.27 132.33 236.67 131.73 235.93 131.73 Z M 230.23 138.77 L 230.23 134.42 L 234.58 134.42 L 234.58 138.77 Z M 211.01 142.45 L 220.79 137.28 L 218.69 136.71 L 219.39 134.13 L 224.78 135.59 C 225.49 135.78 225.91 136.52 225.72 137.23 L 224.26 142.62 L 221.67 141.92 L 222.33 139.5 L 212.26 144.82 Z M 235.93 157.55 L 228.89 157.55 C 228.14 157.55 227.55 158.14 227.55 158.89 L 227.55 165.93 C 227.55 166.67 228.14 167.27 228.89 167.27 L 235.93 167.27 C 236.67 167.27 237.27 166.67 237.27 165.93 L 237.27 158.89 C 237.27 158.14 236.67 157.55 235.93 157.55 Z M 230.23 164.58 L 230.23 160.23 L 234.58 160.23 L 234.58 164.58 Z M 226.31 148.55 C 226.84 149.08 226.84 149.92 226.31 150.45 L 222.37 154.4 L 220.47 152.5 L 222.13 150.84 L 212.46 150.84 L 212.46 148.16 L 222.13 148.16 L 220.47 146.5 L 222.37 144.6 Z M 219.5 120 C 203.23 120 190 133.23 190 149.5 C 190 165.77 203.23 179 219.5 179 C 235.77 179 249 165.77 249 149.5 C 249 133.23 235.77 120 219.5 120 Z M 219.5 176.32 C 204.71 176.32 192.68 164.29 192.68 149.5 C 192.68 134.71 204.71 122.68 219.5 122.68 C 234.29 122.68 246.32 134.71 246.32 149.5 C 246.32 164.29 234.29 176.32 219.5 176.32 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 186px; margin-left: 220px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">NLB</div></div></div></foreignObject><text x="220" y="198" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">NLB</text></switch></g></g><g><rect x="20" y="120" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 150px; margin-left: 21px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Primavera<br />Webservice<br />API</div></div></div></foreignObject><text x="80" y="154" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Primavera...</text></switch></g></g><g><path d="M 340 149.9 L 255.37 149.62" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 250.12 149.6 L 257.13 146.12 L 255.37 149.62 L 257.11 153.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>