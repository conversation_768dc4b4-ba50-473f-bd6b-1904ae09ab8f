# DynamoDB Single Table Design Summary and Query Patterns

## Schema Summary

| **Entity**  | **Primary Key (PK)**       | **Sort Key (SK)**                 | **Attributes**                                                                                 |
|-------------|----------------------------|-----------------------------------|-----------------------------------------------------------------------------------------------|
| **Plant**   | `Plant#{PlantID}`          | `METADATA`                        | `name`, additional metadata                                                                   |
| **Project** | `Project#{ProjectID}`      | `Plant#{PlantID}`                 | `name`, `description`, `status`, `startDate`, `endDate`, `budget`                             |
| **WorkOrder**| `WorkOrder#{WorkOrderID}` | `Project#{ProjectID}`             | `name`, `description`, `status`, `plannedStartDate`, `plannedEndDate`, `actualStartDate`, `actualEndDate` |
| **Activity**| `Activity#{ActivityID}`    | `WorkOrder#{WorkOrderID}`         | `name`, `description`, `status`, `plannedStartDate`, `plannedEndDate`, `actualStartDate`, `actualEndDate`, `attachments`, `comments`, `assignedTo` |

---

## Query Patterns

### 1. Get All Projects for a Plant
- **Purpose**: Retrieve all projects associated with a specific plant.
- **Key Condition**: 
  - `PK = Plant#{PlantID}`
  - `SK` begins with `Project#`.

---

### 2. Get All Work Orders for a Project
- **Purpose**: Retrieve all work orders associated with a specific project.
- **Key Condition**: 
  - `PK = Project#{ProjectID}`
  - `SK` begins with `WorkOrder#`.

---

### 3. Get All Activities for a Work Order
- **Purpose**: Retrieve all activities under a specific work order.
- **Key Condition**: 
  - `PK = WorkOrder#{WorkOrderID}`
  - `SK` begins with `Activity#`.

---

### 4. Get All Projects by Status (via GSI1)
- **Purpose**: Retrieve all projects filtered by a specific status.
- **GSI Key Condition**: 
  - `GSI1PK = Status#OPEN`
  - `GSI1SK` for sorting by project metadata if needed.

---

### 5. Find All Activities Assigned to a Contractor
- **Purpose**: Retrieve all activities assigned to a specific contractor.
- **GSI Key Condition**:
  - `GSI2PK = Contractor#{ContractorID}`
  - `GSI2SK` begins with `Activity#`.

---

### 6. Get Hierarchical Data (Project > Work Orders > Activities)
- **Purpose**: Query related data in a hierarchy.
- **Process**:
  - Step 1: Query projects using `PK = Plant#{PlantID}` and `SK` starts with `Project#`.
  - Step 2: For each project, query work orders using `PK = Project#{ProjectID}`.
  - Step 3: For each work order, query activities using `PK = WorkOrder#{WorkOrderID}`.

---

### 7. Get All Plants
- **Purpose**: Retrieve all plants.
- **Key Condition**:
  - `PK` begins with `Plant#`.
  - `SK = METADATA`.

---

## Example Data

| **PK**                 | **SK**                 | **Type**      | **Attributes**                                |
|-------------------------|------------------------|---------------|----------------------------------------------|
| `Plant#1`              | `METADATA`            | `Plant`       | `name`: "Plant A"                            |
| `Project#101`          | `Plant#1`             | `Project`     | `name`: "Turnaround 2024", `status`: "OPEN"  |
| `WorkOrder#501`        | `Project#101`         | `WorkOrder`   | `name`: "Maintenance Work", `status`: "PENDING" |
| `Activity#1001`        | `WorkOrder#501`       | `Activity`    | `name`: "Valve Inspection", `assignedTo`: "ContractorA" |

---

This table and list of query patterns provide a clear structure for implementing a single table design in DynamoDB while ensuring optimal query performance and scalability.
