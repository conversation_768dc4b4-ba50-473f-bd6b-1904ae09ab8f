# Workflow Logic Documentation

This document outlines the logic applied during the activity and work package status updates within the workflow. The workflow reacts to changes in the progress and status of activities and updates the state of related entities accordingly.

## Workflow Scenarios

### 1. When an Activity's Progress Reaches 100%
- **Action**: 
  - Set the activity's `status` to `COMPLETED`.
  - Update the `actualTimeline.end` field to the current timestamp.
- **Subsequent Check**:
  - Evaluate the status of all activities within the same work package.
  - **If all activities are `COMPLETED`**: 
    - Set the work package's `status` to `COMPLETED`.

### 2. When an Activity's Status is Set to `COMPLETED`
- **Check for Auto-Close Condition**:
  - Determine if the associated project has `autoClose` set to `true`.
  - **If `autoClose` is true**:
    - Update the activity's `status` to `AUTO_CLOSED`.

### 3. When an Activity's Progress Changes from 0% to a Non-Zero Value
- **Action**:
  - Set the activity's `status` to `IN_PROGRESS`.
  - Update the `actualTimeline.start` field to the current timestamp.

---

## Detailed Conditions and Actions

Below are the detailed conditions and corresponding actions for clarity:

### A. Progress Reaches 100%
1. **Condition**: Activity progress is set to 100%.
2. **Actions**:
   - Update status: `status = COMPLETED`.
   - Record end time: `actualTimeline.end = current_timestamp`.
   - Check all activities in the same work package:
     - **If** all activities `status == COMPLETED`:
       - Set work package status: `workpackage.status = COMPLETED`.

### B. Activity Status Changes to `COMPLETED`
1. **Condition**: Activity status is set to `COMPLETED`.
2. **Actions**:
   - Check project setting:
     - **If** `project.autoClose == true`:
       - Update status: `status = AUTO_CLOSED`.

### C. Progress Moves from 0% to Another Value
1. **Condition**: Activity progress changes from 0% to a value greater than 0%.
2. **Actions**:
   - Update status: `status = IN_PROGRESS`.
   - Record start time: `actualTimeline.start = current_timestamp`.
