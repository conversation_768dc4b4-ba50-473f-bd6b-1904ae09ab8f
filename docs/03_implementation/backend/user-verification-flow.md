# User Verification Flow: Cognito to DynamoDB

This document describes the automated flow for loading user data from AWS Cognito to DynamoDB when a user verifies their account.

## Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Components](#components)
  - [Cognito User Pool](#cognito-user-pool)
  - [Post-Confirmation Lambda](#post-confirmation-lambda)
  - [SQS Queue](#sqs-queue)
  - [User Processor Lambda](#user-processor-lambda)
  - [DynamoDB Table](#dynamodb-table)
- [Data Flow](#data-flow)
- [Implementation Details](#implementation-details)
  - [FrontendStack.ts](#frontendstackts)
  - [GraphQlApiStack.ts](#graphqlapistackts)
  - [Post-Confirmation Lambda](#post-confirmation-lambda-1)
  - [User Processor Lambda](#user-processor-lambda-1)
- [Testing](#testing)
- [Troubleshooting](#troubleshooting)

## Overview

When a user verifies their account in Cognito (typically by confirming their email address), we want to automatically create a corresponding user record in DynamoDB. This enables the GraphQL API to access and manage user data consistently with other application data.

The solution uses an event-driven architecture with AWS Lambda and SQS to ensure reliable data transfer between Cognito and DynamoDB.

## Architecture

```mermaid
flowchart LR
    A[Cognito User Pool] -->|Post-Confirmation Trigger| B[Post-Confirmation Lambda]
    B -->|Send Message| C[SQS Queue]
    C -->|Process Message| D[User Processor Lambda]
    D -->|Write User Data| E[DynamoDB]
    
    style A fill:#FF9900,stroke:#FF9900,color:#000000
    style B fill:#009900,stroke:#009900,color:#FFFFFF
    style C fill:#FF4F8B,stroke:#FF4F8B,color:#FFFFFF
    style D fill:#009900,stroke:#009900,color:#FFFFFF
    style E fill:#3F51B5,stroke:#3F51B5,color:#FFFFFF
```

## Components

### Cognito User Pool

The Cognito User Pool manages user authentication and registration. When a user confirms their account (typically by clicking a link in a verification email), Cognito triggers a post-confirmation Lambda function.

### Post-Confirmation Lambda

This Lambda function is triggered when a user confirms their account in Cognito. It extracts the relevant user data from the Cognito event and sends it to an SQS queue for further processing.

### SQS Queue

The SQS (Simple Queue Service) queue acts as a buffer between the Cognito event and the DynamoDB write operation. This decouples the systems and provides resilience in case of temporary failures.

### User Processor Lambda

This Lambda function is triggered by messages in the SQS queue. It processes the user data and writes it to DynamoDB in the format expected by the application.

### DynamoDB Table

The DynamoDB table stores user data along with other application data. The user data is stored in a format that's compatible with the existing user management functionality.

## Data Flow

The following sequence diagram illustrates the flow of data through the system:

```mermaid
sequenceDiagram
    participant User
    participant Cognito
    participant PostConfirmationLambda
    participant SQS
    participant UserProcessorLambda
    participant DynamoDB
    
    User->>Cognito: Verify Account
    Cognito->>PostConfirmationLambda: Trigger Post-Confirmation
    PostConfirmationLambda->>SQS: Send User Data
    Note right of SQS: Message contains username, email, sub
    SQS->>UserProcessorLambda: Deliver Message
    UserProcessorLambda->>DynamoDB: Check if user exists
    DynamoDB-->>UserProcessorLambda: User does not exist
    UserProcessorLambda->>DynamoDB: Write User Record
    Note right of DynamoDB: User record with empty group arrays
    UserProcessorLambda->>SQS: Delete Message
```

## Implementation Details

### FrontendStack.ts

The FrontendStack creates and configures the Cognito User Pool, the SQS queue for user verification events, and the post-confirmation Lambda function.

Key modifications:

```typescript
// Add imports
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as sqs from 'aws-cdk-lib/aws-sqs';
import * as iam from 'aws-cdk-lib/aws-iam';

// Add property to export the queue
export class FrontendStack extends cdk.Stack {
  public readonly userPool: cognito.IUserPool;
  public readonly userVerificationQueue: sqs.Queue;
  
  // In the constructor
  // Create SQS queue for user verification events
  this.userVerificationQueue = new sqs.Queue(this, 'UserVerificationQueue', {
    visibilityTimeout: cdk.Duration.seconds(300),
    retentionPeriod: cdk.Duration.days(4),
  });

  // Create post confirmation Lambda
  const postConfirmationLambda = new lambda.Function(this, 'PostConfirmationLambda', {
    runtime: lambda.Runtime.NODEJS_18_X,
    handler: 'index.handler',
    code: lambda.Code.fromAsset('lambda/post-confirmation'),
    environment: {
      QUEUE_URL: this.userVerificationQueue.queueUrl,
    },
  });

  // Grant Lambda permission to send messages to the queue
  this.userVerificationQueue.grantSendMessages(postConfirmationLambda);

  // Add Lambda as a post confirmation trigger to the user pool
  const cfnUserPool = this.userPool.node.defaultChild as cognito.CfnUserPool;
  
  // Add the Lambda trigger
  cfnUserPool.lambdaConfig = {
    postConfirmation: postConfirmationLambda.functionArn
  };
  
  // Add permissions for Cognito to invoke the Lambda
  postConfirmationLambda.addPermission('AllowCognitoInvoke', {
    principal: new iam.ServicePrincipal('cognito-idp.amazonaws.com'),
    sourceArn: this.userPool.userPoolArn
  });
}
```

### GraphQlApiStack.ts

The GraphQlApiStack imports the SQS queue from the FrontendStack and creates a Lambda function to process the messages and write the user data to DynamoDB.

Key modifications:

```typescript
// Update imports
import { DynamoEventSource, SqsEventSource } from 'aws-cdk-lib/aws-lambda-event-sources';

// Update interface to accept the queue
interface GraphQlApiStackProps extends cdk.StackProps {
  appName: string;
  userPool: cognito.IUserPool;
  userVerificationQueue: sqs.Queue;
}

// In the constructor
// Create a Lambda function to process user verification messages
const userProcessorLambda = new lambda.Function(this, `${appName}UserProcessorFunction`, {
  runtime: lambda.Runtime.NODEJS_18_X,
  handler: 'index.handler',
  code: lambda.Code.fromAsset('lambda/user-processor'),
  environment: {
    TABLE_NAME: this.dynamoTable.tableName,
  },
  timeout: cdk.Duration.seconds(30),
  memorySize: 256,
});

// Grant Lambda permission to access DynamoDB
this.dynamoTable.grantWriteData(userProcessorLambda);

// Create event source mapping to connect SQS to Lambda
userProcessorLambda.addEventSource(
  new SqsEventSource(props.userVerificationQueue, {
    batchSize: 10,
  })
);
```

### Post-Confirmation Lambda

The post-confirmation Lambda function is triggered by Cognito when a user confirms their account. It extracts the user data and sends it to the SQS queue.

```javascript
// lambda/post-confirmation/index.js
const { SQSClient, SendMessageCommand } = require('@aws-sdk/client-sqs');

const sqs = new SQSClient({ region: process.env.AWS_REGION });

exports.handler = async (event, context) => {
  console.log('Post Confirmation event:', JSON.stringify(event, null, 2));
  
  try {
    // Only process if this is a sign-up event
    if (event.triggerSource === 'PostConfirmation_ConfirmSignUp') {
      const { userName, request } = event;
      const { userAttributes } = request;
      
      // Extract user details from the event
      const userData = {
        username: userName,
        email: userAttributes.email,
        sub: userAttributes.sub,
        emailVerified: userAttributes.email_verified === 'true',
        createdAt: new Date().toISOString()
      };
      
      // Send message to SQS queue
      const params = {
        QueueUrl: process.env.QUEUE_URL,
        MessageBody: JSON.stringify(userData),
        MessageAttributes: {
          'event_type': {
            DataType: 'String',
            StringValue: 'USER_VERIFIED'
          }
        }
      };
      
      const command = new SendMessageCommand(params);
      const response = await sqs.send(command);
    }
    
    // Return the event object to Cognito
    return event;
  } catch (error) {
    console.error('Error processing post confirmation event:', error);
    // Still return the event to Cognito to allow the user to be confirmed
    return event;
  }
};
```

### User Processor Lambda

The user processor Lambda function processes messages from the SQS queue and writes the user data to DynamoDB.

```javascript
// lambda/user-processor/index.js
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, PutCommand, GetCommand } = require('@aws-sdk/lib-dynamodb');

const client = new DynamoDBClient({ region: process.env.AWS_REGION });
const dynamodb = DynamoDBDocumentClient.from(client);

exports.handler = async (event, context) => {
  console.log('Processing SQS event:', JSON.stringify(event, null, 2));
  
  const tableName = process.env.TABLE_NAME;
  
  // Process each record in the SQS event
  const processPromises = event.Records.map(async (record) => {
    try {
      // Parse the message body
      const messageBody = JSON.parse(record.body);
      
      // Check if this is a user verification event
      const messageAttributes = record.messageAttributes || {};
      const eventType = messageAttributes.event_type?.stringValue;
      
      if (eventType === 'USER_VERIFIED') {
        await processUserVerification(messageBody, tableName);
      }
    } catch (error) {
      console.error('Error processing record:', error);
    }
  });
  
  // Wait for all records to be processed
  await Promise.all(processPromises);
};

async function processUserVerification(userData, tableName) {
  const { username, email, sub } = userData;
  
  // Check if the user already exists in DynamoDB
  const existingUser = await getUserByUsername(username, tableName);
  if (existingUser) {
    console.log(`User ${username} already exists in DynamoDB, skipping`);
    return;
  }
  
  const timestamp = new Date().toISOString();
  
  // Create the user record in the same format as the existing user data
  const user = {
    username,
    email,
    sub,
    PK: `USER#${username}`,
    SK: `METADATA#${username}`,
    managerGroupIds: [],
    operatorGroupIds: [],
    workerGroupIds: [],
    type: 'USER',
    createdAt: timestamp,
    updatedAt: timestamp
  };
  
  // Write the user record to DynamoDB
  await dynamodb.send(new PutCommand({
    TableName: tableName,
    Item: user
  }));
}

async function getUserByUsername(username, tableName) {
  try {
    const response = await dynamodb.send(new GetCommand({
      TableName: tableName,
      Key: {
        PK: `USER#${username}`,
        SK: `METADATA#${username}`
      }
    }));
    
    return response.Item;
  } catch (error) {
    console.error(`Error getting user ${username}:`, error);
    return null;
  }
}
```

## Component Relationships

The following diagram shows the relationships between the different components:

```mermaid
classDiagram
    class FrontendStack {
        +userPool: IUserPool
        +userVerificationQueue: Queue
    }
    class GraphQlApiStack {
        +dynamoTable: Table
    }
    class PostConfirmationLambda {
        +handler(event, context)
    }
    class UserProcessorLambda {
        +handler(event, context)
    }
    
    FrontendStack --> PostConfirmationLambda: creates
    FrontendStack --> SQS: creates queue
    GraphQlApiStack --> UserProcessorLambda: creates
    GraphQlApiStack --> DynamoDB: creates table
    PostConfirmationLambda --> SQS: sends messages
    SQS --> UserProcessorLambda: triggers
    UserProcessorLambda --> DynamoDB: writes data
```

## Testing

To test the user verification flow:

1. Create a new user in the Cognito User Pool
2. Confirm the user's account (either by clicking the verification link in the email or using the AWS CLI/Console)
3. Check the CloudWatch logs for the post-confirmation Lambda to verify it was triggered
4. Check the CloudWatch logs for the user processor Lambda to verify it processed the message
5. Query the DynamoDB table to verify the user record was created

### Manual Testing with PowerShell Script

For manual testing and troubleshooting, you can use the `backend/scripts/cognito/Invoke-PostConfirmationLambda.ps1` PowerShell script to simulate a Cognito post-confirmation event:

```powershell
# Execute the script with the default Lambda function name
.\Invoke-PostConfirmationLambda.ps1

# Or specify a custom Lambda function name
.\Invoke-PostConfirmationLambda.ps1 -FunctionName YourLambdaFunctionName
```

The script creates a mock Cognito post-confirmation event and invokes the Lambda function directly. This is useful for:

- Testing the Lambda function without going through the actual Cognito confirmation flow
- Debugging issues with the Lambda function or its permissions
- Verifying that the SQS message is sent correctly
- Testing the end-to-end flow from Lambda to SQS to DynamoDB

The script saves the event payload and Lambda response to temporary files for inspection.

## Troubleshooting

Common issues and their solutions:

1. **Post-confirmation Lambda not triggered**
   - Check that the Lambda trigger is correctly configured in the Cognito User Pool
   - Verify that Cognito has permission to invoke the Lambda function

2. **Message not sent to SQS**
   - Check that the post-confirmation Lambda has permission to send messages to the queue
   - Verify that the queue URL environment variable is correctly set

3. **User processor Lambda not triggered**
   - Check that the event source mapping is correctly configured
   - Verify that the Lambda has permission to receive messages from the queue

4. **User record not created in DynamoDB**
   - Check that the user processor Lambda has permission to write to the DynamoDB table
   - Verify that the table name environment variable is correctly set
   - Check if the user already exists in the table (the Lambda skips existing users)
