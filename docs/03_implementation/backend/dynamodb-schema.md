# DynamoDB Single Table Design Schema

This document describes the DynamoDB single table design used in the GraphQL API backend. The application uses a single DynamoDB table to store all entities and their relationships, leveraging DynamoDB's flexible schema and secondary indexes to support various access patterns.

## Table of Contents

- [Overview](#overview)
- [Primary Key Structure](#primary-key-structure)
- [Entity Types](#entity-types)
- [Global Secondary Indexes](#global-secondary-indexes)
- [Access Patterns](#access-patterns)
- [Relationship Modeling](#relationship-modeling)
- [Example Queries](#example-queries)

## Overview

The application uses a single DynamoDB table to store all data, following the single-table design pattern. This approach offers several benefits:

- **Performance**: All related data is stored in one table, reducing the need for multiple queries
- **Cost efficiency**: Fewer tables means lower provisioned throughput costs
- **Flexibility**: The schema can evolve over time without requiring table migrations
- **Simplified operations**: Managing a single table is easier than managing multiple tables

The table uses a composite primary key consisting of a partition key (PK) and a sort key (SK), along with several global secondary indexes (GSIs) to support various access patterns.

## Primary Key Structure

The table uses the following primary key structure:

- **Partition Key (PK)**: Identifies the entity type and ID (e.g., `USER#username`, `PROJECT#id`, `ACTIVITY#id`)
- **Sort Key (SK)**: Identifies the item type or relationship (e.g., `METADATA#id`, `SYNC`)

This structure allows for efficient queries by entity ID and supports storing multiple item types and relationships for each entity.

### Table Layout

```mermaid
graph TD
    subgraph "Single DynamoDB Table"
        PK[Partition Key (PK)] --- SK[Sort Key (SK)]
        PK --- type[type]
        PK --- projectObjectId[projectObjectId]
        PK --- groupId[groupId]
        PK --- predecessorActivityObjectId[predecessorActivityObjectId]
        PK --- successorActivityObjectId[successorActivityObjectId]
        
        subgraph "Indexes"
            type --- GSI1[type-index]
            projectObjectId --- GSI2[projectObjectId-index]
            groupId --- GSI3[groupId-index]
            predecessorActivityObjectId --- GSI4[predecessorActivityObjectId-index]
            successorActivityObjectId --- GSI5[successorActivityObjectId-index]
        end
    end
```

The table contains items with different entity types, all stored together but distinguished by their PK and SK values. The GSIs enable efficient querying by different attributes.

## Entity Types

The application manages the following entity types:

### User

**Primary Item**:
- PK: `USER#{username}`
- SK: `METADATA#{username}`

**Attributes**:
- `username`: String - The unique username
- `email`: String - User's email address
- `sub`: String - Cognito subject identifier
- `managerGroupIds`: Array - IDs of manager groups the user belongs to
- `operatorGroupIds`: Array - IDs of operator groups the user belongs to
- `workerGroupIds`: Array - IDs of worker groups the user belongs to
- `disciplines`: Array - Disciplines the user is associated with
- `equipments`: Array - Equipment types the user is qualified to work with
- `type`: String - Always "USER"
- `createdAt`: String - ISO timestamp
- `updatedAt`: String - ISO timestamp

### Group

**Primary Item**:
- PK: `GROUP#{id}`
- SK: `METADATA#{id}`

**Attributes**:
- `id`: String - Unique identifier
- `name`: String - Group name
- `description`: String - Group description
- `type`: String - Always "GROUP"
- `groupType`: String - One of "MANAGER", "OPERATOR", or "WORKER"
- `createdAt`: String - ISO timestamp
- `updatedAt`: String - ISO timestamp

### Project

**Primary Item**:
- PK: `PROJECT#{id}`
- SK: `METADATA#{id}`

**Attributes**:
- `id`: String - Unique identifier
- `name`: String - Project name
- `description`: String - Project description
- `managerGroups`: Array - Group IDs with manager access
- `operatorGroups`: Array - Group IDs with operator access
- `workerGroups`: Array - Group IDs with worker access
- `isVisible`: Boolean - Whether the project is visible to non-admin users
- `type`: String - Always "PROJECT"
- `createdAt`: String - ISO timestamp
- `updatedAt`: String - ISO timestamp

### Activity

**Primary Item**:
- PK: `ACTIVITY#{id}`
- SK: `METADATA#{id}`

**Attributes**:
- `id`: String - Unique identifier
- `name`: String - Activity name
- `description`: String - Activity description
- `projectObjectId`: String - ID of the parent project
- `status`: String - Activity status (e.g., "NOT_STARTED", "IN_PROGRESS", "COMPLETED")
- `statusCode`: String - Activity status code (e.g., "PLANNED", "ACTIVE", "INACTIVE")
- `percentComplete`: Number - Completion percentage (0.0 to 1.0)
- `plannedStartDate`: String - ISO timestamp
- `plannedFinishDate`: String - ISO timestamp
- `actualStartDate`: String - ISO timestamp
- `actualFinishDate`: String - ISO timestamp
- `discipline`: String - Associated discipline
- `equipment`: String - Associated equipment type
- `contractor`: String - Associated contractor number
- `contractorId`: String - ID of the associated contractor
- `unableToWork`: Boolean - Whether work is blocked
- `type`: String - Always "ACTIVITY"
- `createdAt`: String - ISO timestamp
- `updatedAt`: String - ISO timestamp

**Sync Status Item**:
- PK: `ACTIVITY#{id}`
- SK: `SYNC`

**Attributes**:
- `targetSystem`: String - Target system name (e.g., "P6")
- `lastSyncedAt`: String - ISO timestamp
- `syncStatus`: String - Sync status (e.g., "SUCCESS", "FAILED")

### Contractor

**Primary Item**:
- PK: `CONTRACTOR#{id}`
- SK: `METADATA#{id}`

**Attributes**:
- `id`: String - Unique identifier
- `name`: String - Contractor name
- `contractorNumber`: String - Unique contractor number
- `type`: String - Always "CONTRACTOR"
- `createdAt`: String - ISO timestamp
- `updatedAt`: String - ISO timestamp

### SyncJob

**Primary Item**:
- PK: `SYNCJOB`
- SK: `{syncType}#{projectId}#{timestamp}` or `{syncType}#{timestamp}`

**Attributes**:
- `status`: String - Job status (e.g., "PENDING", "IN_PROGRESS", "COMPLETED", "FAILED")
- `projectId`: String - ID of the associated project (if applicable)
- `executionTime`: Number - Total execution time in milliseconds
- `fetchTime`: Number - Time spent fetching data in milliseconds
- `writeTime`: Number - Time spent writing data in milliseconds
- `projectsProcessed`: Number - Count of processed projects
- `activitiesProcessed`: Number - Count of processed activities
- `activityCodesProcessed`: Number - Count of processed activity codes
- `relationshipsProcessed`: Number - Count of processed relationships

### Relationships

#### User-Group Relationship
- PK: `USER#{username}`
- SK: `GROUP#{groupId}`
- Additional attributes:
  - `username`: String - User's username
  - `groupId`: String - Group ID
  - `type`: String - "USER_GROUP_RELATIONSHIP"

#### User-Contractor Relationship
- PK: `USER#{username}`
- SK: `CONTRACTOR#{contractorId}`
- Additional attributes:
  - `username`: String - User's username
  - `contractorId`: String - Contractor ID
  - `type`: String - "USER_CONTRACTOR_RELATIONSHIP"

#### Activity Relationship
- PK: `RELATIONSHIP#{id}`
- SK: `ACTIVITY_RELATIONSHIP`
- Additional attributes:
  - `predecessorActivityObjectId`: String - ID of the predecessor activity
  - `successorActivityObjectId`: String - ID of the successor activity
  - `type`: String - "ACTIVITY_RELATIONSHIP"
  - `relationshipType`: String - Type of relationship (e.g., "Finish to Start")

## Global Secondary Indexes

The table uses the following Global Secondary Indexes (GSIs) to support various access patterns:

### type-index

- Partition Key: `type`
- Sort Key: None

This index allows querying items by their type, such as finding all users, projects, or activities.

### projectObjectId-index

- Partition Key: `projectObjectId`
- Sort Key: None

This index allows querying activities by their parent project ID.

### groupId-index

- Partition Key: `groupId`
- Sort Key: None

This index allows finding all users belonging to a specific group.

### predecessorActivityObjectId-index

- Partition Key: `predecessorActivityObjectId`
- Sort Key: None

This index allows finding all successor activities for a given activity.

### successorActivityObjectId-index

- Partition Key: `successorActivityObjectId`
- Sort Key: None

This index allows finding all predecessor activities for a given activity.

## Access Patterns

The table design supports the following access patterns:

1. **Get item by ID**: Using the primary key (PK and SK)
   - Get user by username
   - Get project by ID
   - Get activity by ID
   - Get group by ID
   - Get contractor by ID

2. **List items by type**: Using the `type-index`
   - List all users
   - List all projects
   - List all groups
   - List all contractors
   - List all sync jobs

3. **Get activities for a project**: Using the `projectObjectId-index`
   - List all activities for a specific project
   - Filter activities by status, discipline, equipment, etc.

4. **Get users for a group**: Using the `groupId-index`
   - List all users belonging to a specific group

5. **Get activity relationships**: Using the relationship indexes
   - Get predecessor activities for an activity
   - Get successor activities for an activity

6. **Get sync status for an activity**: Using the primary key with a different sort key
   - Get sync status for a specific activity

## Relationship Modeling

### Entity Relationship Diagram

```mermaid
erDiagram
    USER {
        string username PK
        string email
        array managerGroupIds
        array operatorGroupIds
        array workerGroupIds
        array disciplines
        array equipments
    }
    
    GROUP {
        string id PK
        string name
        string description
        string groupType
    }
    
    PROJECT {
        string id PK
        string name
        string description
        array managerGroups
        array operatorGroups
        array workerGroups
        boolean isVisible
    }
    
    ACTIVITY {
        string id PK
        string name
        string description
        string projectObjectId FK
        string status
        string statusCode
        number percentComplete
        string discipline
        string equipment
        string contractorId FK
        boolean unableToWork
    }
    
    CONTRACTOR {
        string id PK
        string name
        string contractorNumber
    }
    
    SYNCJOB {
        string syncType
        string projectId FK
        string timestamp
        string status
    }

    USER }|--o{ GROUP : "belongs to"
    PROJECT }|--o{ ACTIVITY : "contains"
    ACTIVITY }o--o{ ACTIVITY : "predecessor/successor"
    ACTIVITY }o--o| CONTRACTOR : "assigned to"
    USER }o--o{ CONTRACTOR : "works for"
    PROJECT }o--o{ GROUP : "access control"
    SYNCJOB }o--o| PROJECT : "syncs"
```

The application models relationships between entities in several ways:

### One-to-Many Relationships

1. **Project to Activities**: Activities store a reference to their parent project in the `projectObjectId` attribute. The `projectObjectId-index` is used to query all activities for a project.

2. **Group to Users**: The application uses a junction table pattern with items that have PK=`USER#{username}` and SK=`GROUP#{groupId}`. The `groupId-index` allows querying all users in a group.

### Many-to-Many Relationships

1. **Users to Groups**: Users can belong to multiple groups, and groups can have multiple users. This is modeled using:
   - User items with arrays of group IDs (`managerGroupIds`, `operatorGroupIds`, `workerGroupIds`)
   - Junction table items with PK=`USER#{username}` and SK=`GROUP#{groupId}`

2. **Users to Contractors**: Users can be associated with multiple contractors, and contractors can have multiple users. This is modeled using junction table items with PK=`USER#{username}` and SK=`CONTRACTOR#{contractorId}`.

3. **Activities to Activities (Predecessor/Successor)**: Activities can have multiple predecessors and successors. This is modeled using relationship items with attributes for `predecessorActivityObjectId` and `successorActivityObjectId`, indexed by the respective GSIs.

## Example Queries

### Get a User by Username

```javascript
const params = {
  TableName: tableName,
  Key: {
    PK: `USER#${username}`,
    SK: `METADATA#${username}`
  }
};
const result = await dynamodb.send(new GetCommand(params));
return result.Item;
```

### List Activities for a Project

```javascript
const params = {
  TableName: tableName,
  IndexName: "projectObjectId-index",
  KeyConditionExpression: "projectObjectId = :projectId",
  ExpressionAttributeValues: {
    ":projectId": projectId
  }
};
const result = await dynamodb.send(new QueryCommand(params));
return result.Items;
```

### Get Predecessor Activities for an Activity

```javascript
// First, query relationships where this activity is the successor
const relationshipsResult = await queryByIndex(
  dynamodb,
  tableName,
  "successorActivityObjectId-index",
  "successorActivityObjectId",
  activityId
);

// Extract predecessor activity IDs
const activityKeys = relationshipsResult.items
  .map(relationship => ({
    PK: `ACTIVITY#${relationship.predecessorActivityObjectId}`,
    SK: `METADATA#${relationship.predecessorActivityObjectId}`
  }));

// Batch get the predecessor activities
const activities = await batchGetItems(dynamodb, tableName, activityKeys);
return activities;
```

### List All Projects (with Permission Filtering)

```javascript
const params = {
  TableName: tableName,
  IndexName: "type-index",
  KeyConditionExpression: "#type = :projectType",
  ExpressionAttributeNames: {
    "#type": "type"
  },
  ExpressionAttributeValues: {
    ":projectType": "PROJECT"
  }
};

const result = await dynamodb.send(new QueryCommand(params));
const projects = result.Items || [];

// Filter projects based on user permissions
const filteredProjects = projects.filter(project => 
  isAdminWorkerOperatorOrManager.forProject(project, userGroups)
);

return filteredProjects;
```

### Update an Activity

```javascript
const params = {
  TableName: tableName,
  Key: {
    PK: `ACTIVITY#${id}`,
    SK: `METADATA#${id}`
  },
  UpdateExpression: "SET #status = :status, #percentComplete = :percentComplete, #updatedAt = :updatedAt",
  ExpressionAttributeNames: {
    "#status": "status",
    "#percentComplete": "percentComplete",
    "#updatedAt": "updatedAt"
  },
  ExpressionAttributeValues: {
    ":status": "IN_PROGRESS",
    ":percentComplete": 0.5,
    ":updatedAt": new Date().toISOString()
  },
  ReturnValues: "ALL_NEW"
};

const result = await dynamodb.send(new UpdateCommand(params));
return result.Attributes;
```

## Data Consistency and Transactions

### Eventual Consistency

The DynamoDB table uses the default eventually consistent read model for most operations. This provides better performance and lower costs but means that read operations might not immediately reflect the most recent write operations.

For operations that require strong consistency, such as checking if a user exists before creating a new one, the application uses strongly consistent reads by setting the `ConsistentRead` parameter to `true` in the request.

### Transaction Patterns

For operations that require atomicity across multiple items, the application uses DynamoDB transactions. For example, when adding a user to a group, the application needs to:

1. Create a relationship item between the user and group
2. Update the user's group arrays

This is done using a transaction to ensure that either both operations succeed or both fail:

```javascript
const transactionParams = {
  TransactItems: [
    {
      Put: {
        TableName: tableName,
        Item: {
          PK: `USER#${username}`,
          SK: `GROUP#${groupId}`,
          username,
          groupId,
          type: "USER_GROUP_RELATIONSHIP",
          createdAt: timestamp
        }
      }
    },
    {
      Update: {
        TableName: tableName,
        Key: {
          PK: `USER#${username}`,
          SK: `METADATA#${username}`
        },
        UpdateExpression: "SET #groupIds = list_append(#groupIds, :groupId), #updatedAt = :updatedAt",
        ExpressionAttributeNames: {
          "#groupIds": `${groupType}GroupIds`,
          "#updatedAt": "updatedAt"
        },
        ExpressionAttributeValues: {
          ":groupId": [groupId],
          ":updatedAt": timestamp
        }
      }
    }
  ]
};

await dynamodb.send(new TransactWriteCommand(transactionParams));
```

### Optimistic Concurrency Control

For operations that might conflict with concurrent updates, the application uses optimistic concurrency control with conditional expressions. For example, when updating an activity, the application can check that the activity hasn't been modified since it was last read:

```javascript
const params = {
  TableName: tableName,
  Key: {
    PK: `ACTIVITY#${id}`,
    SK: `METADATA#${id}`
  },
  UpdateExpression: "SET #status = :status, #updatedAt = :updatedAt",
  ConditionExpression: "#updatedAt = :currentUpdatedAt",
  ExpressionAttributeNames: {
    "#status": "status",
    "#updatedAt": "updatedAt"
  },
  ExpressionAttributeValues: {
    ":status": "COMPLETED",
    ":updatedAt": new Date().toISOString(),
    ":currentUpdatedAt": currentActivity.updatedAt
  }
};

try {
  await dynamodb.send(new UpdateCommand(params));
} catch (error) {
  if (error.name === "ConditionalCheckFailedException") {
    throw new Error("Activity was modified by another user. Please refresh and try again.");
  }
  throw error;
}
```

### Event-Driven Updates

For certain operations that require asynchronous processing, the application uses an event-driven approach. For example, when an activity is updated, a message is sent to an SQS queue for P6 integration:

```javascript
// Send a message to the SQS queue for P6 integration
const message = {
  action: 'updateActivity',
  activityId: updatedActivity.id,
  updates: {
    PercentComplete: updatedActivity.percentComplete,
    Status: statusMapping[updatedActivity.status] || updatedActivity.status
  }
};

await sqs.send(new SendMessageCommand({
  QueueUrl: process.env.ACTIVITY_UPDATE_QUEUE_URL,
  MessageBody: JSON.stringify(message)
}));
```

This allows the GraphQL API to respond quickly while the integration with external systems happens asynchronously.
