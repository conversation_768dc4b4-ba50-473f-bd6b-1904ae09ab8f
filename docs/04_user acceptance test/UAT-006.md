**Test Case ID/Title**  
UAT-006: Manager – Bulk Set Progress & Status for Selected Activities

**Story / Requirement**  
As a manager I want to bulk set the progress and status of selected activities so I can update many at once.

**Preconditions**  
– Manager is logged in and in the “All Activities” view  
– At least two activities are selected via checkboxes  

**Acceptance Criteria**  
1. “Bulk Actions” menu appears when ≥1 activity is selected  
2. “Set Progress” option opens a modal/input for a percentage value  
3. “Set Status” option opens a dropdown with statuses (“Not Started,” “In Progress,” “Completed,” “Unable to Work”)  
4. Bulk progress obeys same rules: <100% → “In Progress,” 100% → “Completed”  
5. Confirmation toast or modal confirms the operation succeeded  

**Steps & Expected Results**  
1. Select two activities → “Bulk Actions” button becomes enabled  
2. Click “Bulk Actions” → Options for “Set Progress” and “Set Status” appear  
3. Choose “Set Progress,” enter “50%,” click “Apply” → Both activities’ progress updated to 50%, status updates to “In Progress”  
4. Choose “Set Status,” select “Unable to Work,” click “Apply” → Both activities’ status set to “Unable to Work” regardless of progress  
5. Observe success notification → Activities reflect new values in the list  

**Pass/Fail**  
– **Pass**: Both progress and status update correctly for all selected activities; UI confirms success  
– **Fail**: Any selected activity does not update, or non-selected items change  

**Cleanup**  
– Reset those activities to original progress/status values  
