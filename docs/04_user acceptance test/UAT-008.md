**Test Case ID/Title**  
UAT-008: Administrator – Project Management Interface

**Story / Requirement**  
As an administrator I want to view, search, and manage all projects in the system—including their visibility, synchronization status, and access permissions—so I can effectively control which projects are available to users and how they're configured.

**Preconditions**  
– User with administrator privileges is logged in  
– "Project Admin" view is accessible  
– Multiple projects exist in the system with various statuses and settings  

**Acceptance Criteria**  
1. A table displays all projects with columns for ID, name, status, syncing status, and visibility  
2. Administrator can search/filter projects by text input  
3. Administrator can select a project to view and edit its details  
4. Administrator can update project properties (visibility, syncing status, and group permissions)  
5. Table supports pagination with configurable page size  
6. Changes to project settings are saved and reflected in the table  

**Steps & Expected Results**  
1. Navigate to "Project Admin" view →  
   – Table loads with projects displaying ID, name, status, syncing status, and visibility columns  
   – Pagination controls are visible at the bottom  
2. Enter search text in the filter field →  
   – Table updates to show only projects matching the search criteria  
   – Pagination resets to first page  
3. Select a project by clicking on its row →  
   – Row becomes highlighted with light gray background  
   – Project details component loads with the selected project's information  
4. Toggle visibility or syncing status in the project details →  
   – Changes are saved when submitted  
   – Success message "Project updated successfully" appears  
   – Table updates to reflect the changes  
5. Modify group permissions (manager, operator, worker) →  
   – Changes are saved when submitted  
   – Success message "Project updated successfully" appears  
6. Change page size from 10 to 20 items →  
   – Table updates to show 20 items per page  
7. Navigate to next page using pagination controls →  
   – Table updates to show the next set of projects  
   – If approaching the end of cached data and more data is available, additional projects are loaded automatically  

**Pass/Fail**  
- **Pass**: All projects load correctly, search functionality filters as expected, project selection works, updates to project settings are saved and reflected in the UI, and pagination functions properly  
- **Fail**: Projects fail to load, search doesn't filter correctly, project selection doesn't work, updates to project settings aren't saved or reflected in the UI, or pagination doesn't function properly  

**Cleanup**  
– Reset any changed project settings if needed  
– Clear any search filters  
– Navigate away from the Project Admin view
