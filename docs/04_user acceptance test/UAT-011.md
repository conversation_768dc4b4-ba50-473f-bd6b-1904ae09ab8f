**Test Case ID/Title**  
UAT-011: Administrator – Synchronization Jobs Monitoring

**Story / Requirement**  
As an administrator I want to monitor system synchronization jobs—viewing their status, execution time, and processing metrics—so I can track data integration processes and identify any issues that require attention.

**Preconditions**  
– Administrator is logged in  
– "Sync Jobs" view is accessible  
– Multiple synchronization jobs of different types and statuses exist in the system  

**Acceptance Criteria**  
1. Administrator can view a list of all sync jobs with their details (type, project ID, timestamp, status, execution time, items processed)  
2. Status indicators are color-coded for quick visual identification (green for succeeded, red for failed, orange for in progress)  
3. Administrator can search/filter sync jobs by text input  
4. Administrator can sort sync jobs by any column (default is timestamp descending - newest first)  
5. Administrator can paginate through sync jobs with configurable page size  
6. The system loads additional sync jobs automatically when needed during pagination  

**Steps & Expected Results**  
1. Navigate to "Sync Jobs" view →  
   – Sync jobs table displays with columns for sync type, project ID, timestamp, status, execution time, and items processed  
   – Status column shows color-coded values (green for succeeded, red for failed, orange for in progress)  
   – Jobs are sorted by timestamp in descending order (newest first)  
2. Enter search text in the filter field →  
   – Table updates to show only sync jobs matching the search criteria  
   – Search works across sync type, project ID, and status fields  
   – Filter is applied after a 300ms debounce time  
3. Sort the table by different columns →  
   – Click on a column header  
   – Table re-sorts according to the selected column  
   – Click again to toggle between ascending and descending order  
4. Change page size →  
   – Select a different page size from the paginator (e.g., 5, 10, 20, 50)  
   – Table updates to show the selected number of items per page  
5. Navigate to next page →  
   – Click on the next page button  
   – Table updates to show the next set of sync jobs  
   – If approaching the end of cached data and more data is available, additional jobs are loaded automatically  
   – Loading indicator appears during data fetch operations  

**Pass/Fail**  
- **Pass**: All sync jobs load correctly, search functionality filters as expected, sorting works for all columns, pagination functions properly, and status colors display correctly  
- **Fail**: Sync jobs fail to load, search doesn't filter correctly, sorting doesn't work, pagination doesn't function properly, or status colors don't display correctly  

**Cleanup**  
– Clear any search filters  
– Reset pagination to default  
– Navigate away from the Sync Jobs view
