**Test Case ID/Title**  
UAT-003: Update Activity Progress, Status & Comment

**Story / Requirement**  
As a worker I want to see and set the progress of an activity, add a comment or mark it ‘unable to work’, and have status auto-update based on my input.

**Preconditions**  
– User is logged in  
– On “My Activities,” a project and one of the user’s activities is selected  
– Activity detail view is open  

**Acceptance Criteria**  
1. Progress control (slider or input) shows current % and related activity details/relationships  
2. Changing progress from 0%→<100% sets status = “In Progress”  
3. Changing progress to 100% sets status = “Completed”  
4. “Unable to work” button sets status = “Unable to work” regardless of progress  
5. Comment field accepts & displays new comments  

**Steps & Expected Results**  
1. In activity detail view, verify you see: Title, Discipline, Equipment Type, Contractor, Related Tasks  
2. Set progress to 25% → Status updates to “In Progress”  
3. Set progress to 100% → Status updates to “Completed”  
4. Click “Unable to work” → Status changes to “Unable to work”  
5. Enter “Can’t reach site” in Comment → Click “Add” → New comment appears in activity’s comment feed  

**Pass/Fail**  
– **Pass**: Status and comments behave exactly as above  
– **Fail**: Any mismatch in status logic, missing details, or comments not persisting  

**Cleanup**  
– Reset progress to original value  
– Delete test comment  
