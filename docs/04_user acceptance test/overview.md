# User Acceptance Tests Overview

This document provides an overview of all User Acceptance Tests (UATs) for the TAEx application.

| UAT ID | Link | Story/Requirement | Status |
|--------|------|------------------|--------|
| UAT-001 | [View "My Activities" for Selected Turnaround Project](UAT-001.md) | As a worker I want to select a turnaround project and see only the activities associated to me—complete with discipline, equipment type and contractor—so I can focus on my own tasks. | TBD |
| UAT-002 | [View Your Discipline, Equipment Type & Contractor in Profile](UAT-002.md) | As a worker I want to see the discipline, equipment type and contractor that I am associated with in my profile. | TBD |
| UAT-003 | [Update Activity Progress, Status & Comment](UAT-003.md) | As a worker I want to see and set the progress of an activity, add a comment or mark it 'unable to work', and have status auto-update based on my input. | TBD |
| UAT-005 | [Manager – View, Search, Filter & Select All Project Activities](UAT-005.md) | As a manager I want to see, search, filter and select all activities of a project I'm associated with so I can manage them in bulk. | TBD |
| UAT-006 | [Manager – Bulk Set Progress & Status for Selected Activities](UAT-006.md) | As a manager I want to bulk set the progress and status of selected activities so I can update many at once. | TBD |
| UAT-007 | [Manager – Project Progress Overview](UAT-007.md) | As a manager I want an overview of my project's progress—seeing counts of activities by status, a timeline of progress by date, and a tally of "Unable to work" activities—so I can quickly assess where attention is needed. | TBD |
| UAT-008 | [Administrator – Project Management Interface](UAT-008.md) | As an administrator I want to view, search, and manage all projects in the system—including their visibility, synchronization status, and access permissions—so I can effectively control which projects are available to users and how they're configured. | TBD |
| UAT-009 | [Administrator – User and Group Management](UAT-009.md) | As an administrator I want to manage users and groups in the system—creating, viewing, modifying, and deleting users and groups, as well as assigning users to groups—so I can control system access and organize users effectively. | TBD |
| UAT-010 | [Administrator – Contractor Management](UAT-010.md) | As an administrator I want to manage contractors in the system—creating, viewing, editing, and deleting contractors, as well as assigning users to contractors—so I can maintain accurate contractor information and control which users are associated with each contractor. | TBD |
| UAT-011 | [Administrator – Synchronization Jobs Monitoring](UAT-011.md) | As an administrator I want to monitor system synchronization jobs—viewing their status, execution time, and processing metrics—so I can track data integration processes and identify any issues that require attention. | TBD |
