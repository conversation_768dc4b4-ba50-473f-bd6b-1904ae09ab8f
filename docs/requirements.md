
# Turnaround Management: Relationships Between Projects, Work Orders, and Activities

This document explains the hierarchical relationships and dependencies between **Projects**, **Work Orders**, and **Activities** in turnaround management software such as **Roser** or **STO Planner**. It also includes details about plant associations, roles, and process steps like scheduling and progress reporting.

---

## 1. Project
- **Definition**: A project represents the overarching event, such as a shutdown, turnaround, or major maintenance activity.
- **Characteristics**:
  - Defines the timeline, budget, and resources for the event.
  - Has a Status
  - Associated with a specific **Plant**, representing the facility where the project is executed.
  - Acts as the top-level container for all related work orders, and activities.
  - Access to a project is provided to contractors having tasks in the project. Otherwise only admins and managers have access.
- **Relation to Other Entities**:
  - Tracks progress across all related activities and work orders.

---

## 2. Work Order
- **Definition**: A work order specifies individual tasks or sets of tasks required to achieve a particular outcome within a project.
- **Characteristics**:
  - Provides detailed instructions, materials, tools, and workforce requirements for the task.
  - Includes information like priorities, deadlines, and safety protocols.
  - Has a planned and actual timeline
  - Has a Status as status that is dervied from the underlaying activities
- **Relation to Other Entities**:
  - Belongs to a **project**, providing granular details for execution.
  - Includes or references **activities** that represent steps or sub-tasks.
  - May involve dependencies on other work orders.

---

## 3. Activity
- **Definition**: An activity is the smallest unit of work within a work order, representing a single action or step.
- **Characteristics**:
  - Has a Status
  - Highly detailed, specifying what needs to be done, when, and by whom.
  - Includes resources such as labor, materials, and tools.
  - Has a planned and actual timeline
  - Has one or many attachments
  - Users can comment on activities
  - Activities are assigned to contractors
- **Relation to Other Entities**:
  - Activities are part of **work orders**.
  - May have sequencing or dependencies (e.g., Activity A must finish before Activity B starts).

---

## 4. Main Process Steps
Turnaround management involves two core processes:
1. **Adjusting the Schedule of Activities**:
   - Activities and work orders are scheduled based on dependencies, resource availability, and project timelines.
   - Changes to schedules are updated in real time to reflect any disruptions or changes in priorities.

2. **Reporting Back on Progress**:
   - Progress updates are logged at the activity or work order level.
   - These updates roll up to provide scope-level and project-level status reports.
   - Software tools like Roser or STO Planner ensure visibility across all levels.

---

## 6. Roles
- **Definition**: Roles define the level of access and permissions users have within the system.
- **Types of Roles**:
  1. **Admin**:
     - Can see and manage all projects across all plants.
     - Has the highest level of access, including plant and project management.
  2. **Plant-Specific Roles**:
     - **Read Role**: Can view projects and associated data within a specific plant.
     - **Write Role**: Can modify projects and associated data within a specific plant.
- **Relation to Other Entities**:
  - Roles are tied to **Plants** to enforce access controls and ensure secure data management.


* operator kann alle tätigkeiten und workpackages sehen (aber nichts zurückmelden)
* manager (alles sehen und kann auch attachments hinzufügen)
* contractor / rückmeldender

Gruppen: 
* Siemens ist ein großer Laden. eine Gruppe wird nicht reichen. Wir brauchen Sub-Gruppen für Disziplinen und Equipement Type z.B. PCT oder Mechanik
* z.B. mechanik_pumpen_siemens

---

## Hierarchical Relationship

```plaintext
Plant
 ├── Project 1
 │    │     ├── Work Order 1
 │    │     │     ├── Activity A
 │    │     │     └── Activity B
 │    │     ├── Work Order 2
 │    │     │     ├── Activity C
 │    │     │     └── Activity D
 │    │     └── ...
 │    │     ├── Work Order 3
 │    │     │     ├── Activity E
 │    │     │     └── Activity F
 │    │     └── ...
 │    └── ...
 └── Project 2
      ├── Work Order 2
      └── ...
```


