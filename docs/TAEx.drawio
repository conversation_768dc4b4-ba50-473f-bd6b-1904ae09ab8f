<mxfile host="65bd71144e">
    <diagram name="backend" id="6JOup65e_AeVQfz4VQyZ">
        <mxGraphModel dx="1332" dy="514" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="n3-xsg5VywRs4mN6ifzq-1" value="AWS Account" style="points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_account;strokeColor=#CD2264;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#CD2264;dashed=0;" parent="1" vertex="1">
                    <mxGeometry x="200" y="210" width="730" height="610" as="geometry"/>
                </mxCell>
                <mxCell id="n3-xsg5VywRs4mN6ifzq-4" value="IntranetStaticWebsite&lt;br&gt;Construct" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;strokeColor=#232F3E;fillColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.stack;" parent="n3-xsg5VywRs4mN6ifzq-1" vertex="1">
                    <mxGeometry x="150" y="76" width="60" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="3" style="edgeStyle=none;html=1;" parent="n3-xsg5VywRs4mN6ifzq-1" source="n3-xsg5VywRs4mN6ifzq-8" target="2" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="n3-xsg5VywRs4mN6ifzq-8" value="AppSync&lt;br&gt;API" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#E7157B;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.appsync;" parent="n3-xsg5VywRs4mN6ifzq-1" vertex="1">
                    <mxGeometry x="230" y="204" width="78" height="78" as="geometry"/>
                </mxCell>
                <mxCell id="11" style="edgeStyle=none;html=1;" parent="n3-xsg5VywRs4mN6ifzq-1" source="n3-xsg5VywRs4mN6ifzq-10" target="10" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="n3-xsg5VywRs4mN6ifzq-10" value="DynamoDb&lt;br&gt;TrainingsTable" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#C925D1;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.dynamodb;" parent="n3-xsg5VywRs4mN6ifzq-1" vertex="1">
                    <mxGeometry x="590" y="204" width="78" height="78" as="geometry"/>
                </mxCell>
                <mxCell id="4" style="edgeStyle=none;html=1;" parent="n3-xsg5VywRs4mN6ifzq-1" source="2" target="n3-xsg5VywRs4mN6ifzq-10" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="graphql-api" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.lambda;" parent="n3-xsg5VywRs4mN6ifzq-1" vertex="1">
                    <mxGeometry x="420" y="204" width="78" height="78" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="Attachment Bucket" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;" parent="n3-xsg5VywRs4mN6ifzq-1" vertex="1">
                    <mxGeometry x="308" y="440" width="78" height="78" as="geometry"/>
                </mxCell>
                <mxCell id="13" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" parent="n3-xsg5VywRs4mN6ifzq-1" source="10" target="14" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="638" y="480" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="10" value="StreamHandler" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.lambda;" parent="n3-xsg5VywRs4mN6ifzq-1" vertex="1">
                    <mxGeometry x="590" y="362" width="78" height="78" as="geometry"/>
                </mxCell>
                <mxCell id="15" style="edgeStyle=none;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" parent="n3-xsg5VywRs4mN6ifzq-1" source="14" target="n3-xsg5VywRs4mN6ifzq-10" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="800" y="537"/>
                            <mxPoint x="800" y="243"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="14" value="StepFunctions Workflow" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.step_functions;fillColor=#D9A741;gradientColor=none;" parent="n3-xsg5VywRs4mN6ifzq-1" vertex="1">
                    <mxGeometry x="590" y="490" width="76.5" height="93" as="geometry"/>
                </mxCell>
                <mxCell id="n3-xsg5VywRs4mN6ifzq-6" value="loadSinglePageApplication()" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="n3-xsg5VywRs4mN6ifzq-5" target="n3-xsg5VywRs4mN6ifzq-4" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="99" y="316"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="n3-xsg5VywRs4mN6ifzq-11" value="getProject(), ..." style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="n3-xsg5VywRs4mN6ifzq-5" target="n3-xsg5VywRs4mN6ifzq-8" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="upload/get/delete Attachments" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="n3-xsg5VywRs4mN6ifzq-5" target="7" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="90" y="690"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="n3-xsg5VywRs4mN6ifzq-5" value="User" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#232F3D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.user;" parent="1" vertex="1">
                    <mxGeometry x="60" y="410" width="78" height="78" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="preSignS3URLs" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.lambda;" parent="1" vertex="1">
                    <mxGeometry x="620" y="530" width="78" height="78" as="geometry"/>
                </mxCell>
                <mxCell id="6" style="edgeStyle=none;html=1;" parent="1" source="n3-xsg5VywRs4mN6ifzq-8" target="5" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
    <diagram id="9qKIk-H6OMXjcmAIqt9d" name="stepfunctions">
        <mxGraphModel dx="1338" dy="560" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="Xth5X9H5TaYKL8Bg5qdP-1" value="AWS Step Functions workflow" style="points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_aws_step_functions_workflow;strokeColor=#CD2264;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#CD2264;dashed=0;" parent="1" vertex="1">
                    <mxGeometry x="230" y="210" width="400" height="280" as="geometry"/>
                </mxCell>
                <mxCell id="Xth5X9H5TaYKL8Bg5qdP-2" value="" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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***************************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;" parent="Xth5X9H5TaYKL8Bg5qdP-1" vertex="1">
                    <mxGeometry x="10" y="49.99999999999994" width="380" height="201.59" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
    <diagram id="RMC2B6ZuDkgfB3q3Y9I0" name="Auth">
        <mxGraphModel dx="1188" dy="649" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="zYwn5jrTji7d0IKOaY-w-1" value="Cognito&lt;br&gt;with Groups" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="310" y="40" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="zYwn5jrTji7d0IKOaY-w-4" value="get_token()" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="zYwn5jrTji7d0IKOaY-w-2" target="zYwn5jrTji7d0IKOaY-w-1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2Fsmevz2FR3730oor9Jn-1" value="updateActivity() with Token" style="edgeStyle=none;html=1;" edge="1" parent="1" source="zYwn5jrTji7d0IKOaY-w-2" target="zYwn5jrTji7d0IKOaY-w-6">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="zYwn5jrTji7d0IKOaY-w-2" value="" style="shape=actor;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="40" y="200" width="40" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="zYwn5jrTji7d0IKOaY-w-5" value="Token has groups for&amp;nbsp;&lt;br&gt;executor, company, discipline" style="whiteSpace=wrap;html=1;strokeColor=none;" vertex="1" parent="1">
                    <mxGeometry x="10" y="270" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="2Fsmevz2FR3730oor9Jn-3" value="resolver" style="edgeStyle=none;html=1;" edge="1" parent="1" source="zYwn5jrTji7d0IKOaY-w-6" target="2Fsmevz2FR3730oor9Jn-2">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="zYwn5jrTji7d0IKOaY-w-6" value="AppSync API" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#E7157B;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.appsync;" vertex="1" parent="1">
                    <mxGeometry x="320" y="192" width="78" height="78" as="geometry"/>
                </mxCell>
                <mxCell id="2Fsmevz2FR3730oor9Jn-6" value="updateActivity" style="edgeStyle=none;html=1;" edge="1" parent="1" source="2Fsmevz2FR3730oor9Jn-2" target="2Fsmevz2FR3730oor9Jn-5">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2Fsmevz2FR3730oor9Jn-2" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#ED7100;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.lambda;" vertex="1" parent="1">
                    <mxGeometry x="520" y="192" width="78" height="78" as="geometry"/>
                </mxCell>
                <mxCell id="2Fsmevz2FR3730oor9Jn-4" value="Logic: If token has role Executor AND &quot;company&quot; at activity equals company groups AND discpline group equals discipline" style="whiteSpace=wrap;html=1;strokeColor=none;" vertex="1" parent="1">
                    <mxGeometry x="443.5" y="290" width="231" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="2Fsmevz2FR3730oor9Jn-5" value="" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.dynamo_db;fillColor=#2E73B8;gradientColor=none;" vertex="1" parent="1">
                    <mxGeometry x="710" y="192" width="72" height="81" as="geometry"/>
                </mxCell>
                <mxCell id="2Fsmevz2FR3730oor9Jn-7" value="Auth based on Token. No Auth information in dynamodb." style="whiteSpace=wrap;html=1;strokeColor=none;" vertex="1" parent="1">
                    <mxGeometry x="254.5" y="440" width="231" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
    <diagram id="1KslJCno1vFf9DffpOd1" name="p6integration">
        <mxGraphModel dx="1332" dy="514" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="YK5TfX7oPjBGdjDJB8IO-5" value="TAEX AWS Account" style="points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_account;strokeColor=#CD2264;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#CD2264;dashed=0;" parent="1" vertex="1">
                    <mxGeometry x="330" y="130" width="460" height="270" as="geometry"/>
                </mxCell>
                <mxCell id="YK5TfX7oPjBGdjDJB8IO-3" value="" style="sketch=0;outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_vpc;strokeColor=#879196;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#879196;dashed=0;" parent="YK5TfX7oPjBGdjDJB8IO-5" vertex="1">
                    <mxGeometry x="20" y="20" width="260" height="205" as="geometry"/>
                </mxCell>
                <mxCell id="YK5TfX7oPjBGdjDJB8IO-6" value="Endpoint" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#8C4FFF;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.endpoints;" parent="YK5TfX7oPjBGdjDJB8IO-5" vertex="1">
                    <mxGeometry x="30" y="120" width="60" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="YK5TfX7oPjBGdjDJB8IO-11" value="fetchProjects()" style="edgeStyle=none;html=1;" parent="YK5TfX7oPjBGdjDJB8IO-5" source="YK5TfX7oPjBGdjDJB8IO-2" target="YK5TfX7oPjBGdjDJB8IO-6" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="YK5TfX7oPjBGdjDJB8IO-14" value="writeProjects()" style="edgeStyle=none;html=1;" parent="YK5TfX7oPjBGdjDJB8IO-5" source="YK5TfX7oPjBGdjDJB8IO-2" target="YK5TfX7oPjBGdjDJB8IO-13" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="YK5TfX7oPjBGdjDJB8IO-2" value="P6Integration" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#ED7100;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.lambda;" parent="YK5TfX7oPjBGdjDJB8IO-5" vertex="1">
                    <mxGeometry x="190" y="120" width="60" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="YK5TfX7oPjBGdjDJB8IO-13" value="DynamoDb" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.dynamo_db;fillColor=#2E73B8;gradientColor=none;" parent="YK5TfX7oPjBGdjDJB8IO-5" vertex="1">
                    <mxGeometry x="350" y="109.5" width="72" height="81" as="geometry"/>
                </mxCell>
                <mxCell id="YK5TfX7oPjBGdjDJB8IO-18" style="edgeStyle=none;html=1;" parent="YK5TfX7oPjBGdjDJB8IO-5" source="YK5TfX7oPjBGdjDJB8IO-15" target="YK5TfX7oPjBGdjDJB8IO-2" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="YK5TfX7oPjBGdjDJB8IO-15" value="Event Bridge" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.event_time_based;fillColor=#759C3E;gradientColor=none;" parent="YK5TfX7oPjBGdjDJB8IO-5" vertex="1">
                    <mxGeometry x="204.25" y="30" width="31.5" height="42" as="geometry"/>
                </mxCell>
                <mxCell id="YK5TfX7oPjBGdjDJB8IO-8" value="Primavera AWS Account" style="points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_account;strokeColor=#CD2264;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#CD2264;dashed=0;" parent="1" vertex="1">
                    <mxGeometry x="20" y="130" width="260" height="270" as="geometry"/>
                </mxCell>
                <mxCell id="YK5TfX7oPjBGdjDJB8IO-10" style="edgeStyle=none;html=1;" parent="YK5TfX7oPjBGdjDJB8IO-8" source="YK5TfX7oPjBGdjDJB8IO-7" target="YK5TfX7oPjBGdjDJB8IO-9" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="YK5TfX7oPjBGdjDJB8IO-7" value="NLB" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#8C4FFF;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.network_load_balancer;" parent="YK5TfX7oPjBGdjDJB8IO-8" vertex="1">
                    <mxGeometry x="190" y="120" width="59" height="59" as="geometry"/>
                </mxCell>
                <mxCell id="YK5TfX7oPjBGdjDJB8IO-9" value="Primavera&lt;br&gt;Webservice&lt;br&gt;API" style="rounded=0;whiteSpace=wrap;html=1;" parent="YK5TfX7oPjBGdjDJB8IO-8" vertex="1">
                    <mxGeometry x="20" y="120" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="YK5TfX7oPjBGdjDJB8IO-12" style="edgeStyle=none;html=1;" parent="1" source="YK5TfX7oPjBGdjDJB8IO-6" target="YK5TfX7oPjBGdjDJB8IO-7" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
    <diagram id="I8SGmahdP5udgatgzcxY" name="integration_logivc">
        <mxGraphModel dx="1332" dy="514" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="DS5ngCHecK6zzx7O3GxV-3" value="getAllProjects()" style="edgeStyle=none;html=1;" edge="1" parent="1" source="DS5ngCHecK6zzx7O3GxV-1" target="DS5ngCHecK6zzx7O3GxV-2">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="DS5ngCHecK6zzx7O3GxV-5" value="add message fetchProject(xyz) to queue" style="edgeStyle=none;html=1;" edge="1" parent="1" source="DS5ngCHecK6zzx7O3GxV-1" target="DS5ngCHecK6zzx7O3GxV-4">
                    <mxGeometry x="0.0052" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="DS5ngCHecK6zzx7O3GxV-1" value="sync-funciton" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.lambda_function;fillColor=#F58534;gradientColor=none;" vertex="1" parent="1">
                    <mxGeometry x="420" y="124.5" width="69" height="72" as="geometry"/>
                </mxCell>
                <mxCell id="DS5ngCHecK6zzx7O3GxV-2" value="Table" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.dynamo_db;fillColor=#2E73B8;gradientColor=none;" vertex="1" parent="1">
                    <mxGeometry x="670" y="120" width="72" height="81" as="geometry"/>
                </mxCell>
                <mxCell id="DS5ngCHecK6zzx7O3GxV-4" value="FetchProjectsQueue" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.sqs;fillColor=#D9A741;gradientColor=none;" vertex="1" parent="1">
                    <mxGeometry x="413" y="290" width="76.5" height="93" as="geometry"/>
                </mxCell>
                <mxCell id="DS5ngCHecK6zzx7O3GxV-7" value="subscribe()" style="edgeStyle=none;html=1;" edge="1" parent="1" source="DS5ngCHecK6zzx7O3GxV-6" target="DS5ngCHecK6zzx7O3GxV-4">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="DS5ngCHecK6zzx7O3GxV-9" value="getActivities()" style="edgeStyle=none;html=1;entryX=0.25;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="DS5ngCHecK6zzx7O3GxV-6" target="DS5ngCHecK6zzx7O3GxV-8">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="DS5ngCHecK6zzx7O3GxV-10" value="getActivityCode()" style="edgeStyle=none;html=1;entryX=0.75;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="DS5ngCHecK6zzx7O3GxV-6" target="DS5ngCHecK6zzx7O3GxV-8">
                    <mxGeometry x="0.3237" y="-18" relative="1" as="geometry">
                        <mxPoint x="1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="DS5ngCHecK6zzx7O3GxV-11" value="writeActivities()" style="edgeStyle=none;html=1;" edge="1" parent="1" source="DS5ngCHecK6zzx7O3GxV-6" target="DS5ngCHecK6zzx7O3GxV-2">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="DS5ngCHecK6zzx7O3GxV-6" value="fetchProjects" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.lambda_function;fillColor=#F58534;gradientColor=none;" vertex="1" parent="1">
                    <mxGeometry x="660" y="301" width="69" height="72" as="geometry"/>
                </mxCell>
                <mxCell id="DS5ngCHecK6zzx7O3GxV-8" value="API" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="646" y="450" width="120" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>