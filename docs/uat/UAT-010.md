**Test Case ID/Title**  
UAT-010: Administrator – Contractor Management

**Story / Requirement**  
As an administrator I want to manage contractors in the system—creating, viewing, editing, and deleting contractors, as well as assigning users to contractors—so I can maintain accurate contractor information and control which users are associated with each contractor.

**Preconditions**  
– Administrator is logged in  
– "Contractor Management" view is accessible  
– Test contractors and users exist in the system  

**Acceptance Criteria**  
1. Administrator can view a list of all contractors with their details and assigned users  
2. Administrator can create new contractors with contractor number, name, and description  
3. Administrator can edit existing contractor information  
4. Administrator can delete existing contractors  
5. Administrator can assign users to contractors  
6. Administrator can remove users from contractors  

**Steps & Expected Results**  
1. Navigate to "Contractor Management" view →  
   – Contractor list displays with columns for contractor number, name, description, users, and actions  
   – Create/Edit contractor form is visible at the top of the page  
2. Create a new contractor →  
   – Enter contractor number (required) and name (required) in the form  
   – Add optional description  
   – Click "Create Contractor" button  
   – Contractor appears in the list after creation  
   – Success message "Contractor created successfully" is displayed  
3. Edit an existing contractor →  
   – Click the edit icon for a contractor  
   – Form is populated with contractor details  
   – Form title changes to "Edit Contractor"  
   – Modify the information  
   – Click "Update Contractor" button  
   – Contractor information is updated in the list  
   – Success message "Contractor updated successfully" is displayed  
   – Form resets to "Create Contractor" mode  
4. Cancel editing a contractor →  
   – While in edit mode, click "Cancel" button  
   – Form resets to "Create Contractor" mode  
   – No changes are made to the contractor  
5. Assign a user to a contractor →  
   – Click the "Assign User" icon (person_add) for a contractor  
   – Select a user from the dropdown menu  
   – User appears in the contractor's user list  
   – Success message "User assigned to contractor successfully" is displayed  
6. Remove a user from a contractor →  
   – Click the "X" button on a user chip in the users column  
   – User is removed from the contractor's user list  
   – Success message "User removed from contractor successfully" is displayed  
7. Delete a contractor →  
   – Click the delete icon for a contractor  
   – Confirm deletion in the dialog  
   – Contractor is removed from the list  
   – Success message "Contractor deleted successfully" is displayed  

**Pass/Fail**  
- **Pass**: All contractor management functions work as expected, with appropriate feedback and UI updates  
- **Fail**: Any function fails to perform as expected, error messages are not displayed, or UI is not updated correctly  

**Cleanup**  
– Delete any test contractors created during testing  
– Navigate away from the Contractor Management view
