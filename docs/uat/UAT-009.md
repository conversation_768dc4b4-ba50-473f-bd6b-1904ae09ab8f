**Test Case ID/Title**  
UAT-009: Administrator – User and Group Management

**Story / Requirement**  
As an administrator I want to manage users and groups in the system—creating, viewing, modifying, and deleting users and groups, as well as assigning users to groups—so I can control system access and organize users effectively.

**Preconditions**  
– Administrator is logged in  
– "User Management" view is accessible  
– Test users and groups exist in the system  

**Acceptance Criteria**  
1. Administrator can view a list of all users with their email addresses and group assignments  
2. Administrator can create new users with username and email  
3. Administrator can delete existing users  
4. Administrator can view and modify user details including disciplines and equipment  
5. Administrator can view a list of all groups with their types and descriptions  
6. Administrator can create new groups with name, type, and description  
7. Administrator can delete existing groups  
8. Administrator can assign users to groups and remove users from groups  

**Steps & Expected Results**  
1. Navigate to "User Management" view →  
   – User list displays with columns for username, email, groups, and actions  
   – Group list is accessible via tab navigation  
2. Create a new user →  
   – Enter username (required) and email in the form  
   – Click "Create User" button  
   – User appears in the list after creation  
   – Success message is displayed  
3. Select a user from the list →  
   – User row is clicked  
   – User details panel appears with basic information (username, email, creation date)  
   – Disciplines and equipment sections are available  
4. Add disciplines and equipment to the selected user →  
   – Enter discipline name and press Enter  
   – Enter equipment name and press Enter  
   – Items are added to the respective chip lists  
   – Success messages "Disciplines updated successfully" and "Equipment updated successfully" are displayed  
5. Assign the user to a group →  
   – Click the "Assign to Group" icon  
   – Select a group from the dropdown menu  
   – Group appears in the user's group list  
   – Success message "User assigned to group successfully" is displayed  
6. Remove the user from a group →  
   – Click the "X" button on a group chip  
   – Group is removed from the user's group list  
   – Success message "User removed from group successfully" is displayed  
7. Switch to the Groups tab →  
   – Click on the "Groups" tab  
   – Group list displays with columns for name, type, description, and actions  
8. Create a new group →  
   – Enter name (required), select type, and add optional description  
   – Click "Create Group" button  
   – Group appears in the list after creation  
   – Success message "Group created successfully" is displayed  
9. Delete a user and a group →  
   – Click the delete icon for a user  
   – Confirm deletion in the dialog  
   – User is removed from the list  
   – Success message "User deleted successfully" is displayed  
   – Click the delete icon for a group  
   – Confirm deletion in the dialog  
   – Group is removed from the list  
   – Success message "Group deleted successfully" is displayed  

**Pass/Fail**  
- **Pass**: All user and group management functions work as expected, with appropriate feedback and UI updates  
- **Fail**: Any function fails to perform as expected, error messages are not displayed, or UI is not updated correctly  

**Cleanup**  
– Delete any test users and groups created during testing  
– Navigate away from the User Management view
