**Test Case ID/Title**  
UAT-001: View “My Activities” for Selected Turnaround Project

**Story / Requirement**  
As a worker I want to select a turnaround project and see only the activities associated to me—complete with discipline, equipment type and contractor—so I can focus on my own tasks.

**Preconditions**  
– User is logged in  
– At least one turnaround project exists with activities assigned to this user  

**Acceptance Criteria**  
1. “My Activities” view is available  
2. Only the user’s activities for the chosen project display  
3. Each activity row shows Discipline, Equipment Type & Contractor  
4. Search, Filter and Sort controls are present and functional  

**Steps & Expected Results**  
1. Click “My Activities” → Projects dropdown appears  
2. Select a project → Activity list refreshes to show only your activities  
3. Verify each row displays your Discipline, Equipment Type & Contractor  
4. Enter a keyword in Search → List is filtered to matching activities  
5. Apply a Filter (e.g. by discipline) → List narrows accordingly  
6. Click on a column header (e.g. “Start Date”) → List sorts by that column  

**Pass/Fail**  
– **Pass**: Only the user’s activities appear; all fields and controls work as specified  
– **Fail**: Any activity not belonging to the user appears, fields are missing, or controls don’t filter/sort/search correctly  

**Cleanup**  
– Reset Search, Filters & Sort to defaults  