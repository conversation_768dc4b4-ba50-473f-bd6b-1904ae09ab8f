**Test Case ID/Title**  
UAT-005: Manager – View, Search, Filter & Select All Project Activities

**Story / Requirement**  
As a manager I want to see, search, filter and select all activities of a project I’m associated with so I can manage them in bulk.

**Preconditions**  
– Manager is logged in  
– At least one project exists with multiple activities  

**Acceptance Criteria**  
1. “All Activities” view is available and lists every activity in the chosen project  
2. Search box filters the list by keyword (e.g. activity name)  
3. Filter controls (e.g. by Discipline, Equipment Type, Status) narrow the list correctly  
4. Sort controls (column headers) reorder the list correctly  
5. Each row has a checkbox for multi-select  

**Steps & Expected Results**  
1. Click “All Activities” → Full list of that project’s activities appears  
2. Type “welding” in Search → List shows only activities with “welding” in their name/description  
3. Apply filter “Status = In Progress” → List shows only in-progress activities  
4. Click “Start Date” header → List sorts ascending by start date, click again → descending  
5. Check boxes beside three activities → Three rows become highlighted/selected  

**Pass/Fail**  
– **Pass**: List shows exactly the expected activities; search, filter, sort and multi-select work as specified  
– **Fail**: Any activity out of scope appears; controls do not filter/sort/select correctly  

**Cleanup**  
– Clear Search text, reset Filters and Sort to defaults  
– Deselect all checkboxes  
