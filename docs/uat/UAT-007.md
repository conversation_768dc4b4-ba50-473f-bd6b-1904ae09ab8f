**Test Case ID/Title**  
UAT-007: Manager – Project Progress Overview

**Story / Requirement**  
As a manager I want an overview of my project’s progress—seeing counts of activities by status, a timeline of progress by date, and a tally of “Unable to work” activities—so I can quickly assess where attention is needed.

**Preconditions**  
– Manager is logged in  
– “Project Dashboard” or “Progress Overview” view is available for the project  
– The project has activities in various statuses (Not Started, In Progress, Completed, Unable to work) with progress updates on different dates  

**Acceptance Criteria**  
1. A summary panel shows counts for each status: Not Started, In Progress, Completed, Unable to work  
2. A date-based chart/graph plots aggregate progress (e.g. average % complete or total # of activities updated) over time  
3. The “Unable to work” count is highlighted (e.g. bold or colored) and matches the summary panel  
4. All elements load within 3 seconds of opening the view  

**Steps & Expected Results**  
1. Navigate to “Progress Overview” →  
   – Summary panel displays four status counts (e.g. Not Started: 10; In Progress: 25; Completed: 50; Unable to work: 3)  
2. Verify the “Unable to work” count matches both the panel and any highlighted call-out  
3. Observe the progress-over-time chart →  
   – X-axis = Date, Y-axis = Progress metric (e.g. avg. % or # of updates)  
   – Data points appear for each day with activity updates  
4. Hover over/chart-click a data point → Tooltip shows date and exact progress value  
5. (Optional) Change time window filter (e.g. Last 7 days → Last 30 days) → Chart updates accordingly, summary counts remain correct  

**Pass/Fail**  
- **Pass**: All counts and chart data are accurate, synchronized, highlighted “Unable to work” count is correct, and UI responds as expected  
- **Fail**: Any mismatch in counts, missing chart points, incorrect highlighting, or loading errors  

**Cleanup**  
– Close the “Progress Overview” view  
– Reset any time-window filters to default  
