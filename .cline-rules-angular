# Angular Frontend Rules for TAEX Project

## Component Structure

### Directory Organization
- Components are organized by feature in the `/app/src/app/components` directory
- Core functionality is in the `/app/src/app/core` directory
- Shared components are in the `/app/src/app/shared` directory
- Services are in the `/app/src/app/services` directory
- GraphQL queries are in the `/app/src/app/graphql` directory

### File Organization
- Each component has its own directory
- Component files follow the pattern: `[name].component.[type]`
  - TypeScript logic: `[name].component.ts`
  - HTML template: `[name].component.html`
  - CSS styles: `[name].component.css` or `[name].component.scss`
  - Tests: `[name].component.spec.ts`

## Component Patterns

### Container Components
- Manager components like `manager-view.component.ts` act as container components
- Container components handle data fetching, state management, and business logic
- Container components pass data to presentational components via inputs
- Container components handle events from presentational components via outputs

### Presentational Components
- Components like `activity-search.component.ts` act as presentational components
- Presentational components focus on rendering UI and handling user interactions
- Presentational components receive data via inputs
- Presentational components emit events via outputs

### Component Communication
- Use `@Input()` decorators for passing data to child components
- Use `@Output()` decorators with `EventEmitter` for child-to-parent communication
- Use services for communication between unrelated components
- Use `BehaviorSubject` for maintaining observable state in services

## Angular Material Usage

### UI Components
- Use Angular Material components for consistent UI
- Use Material Design icons for icons
- Use Material themes for styling
- Use Material form controls for forms

### Table Components
- Use `MatTableDataSource` for table data
- Use `MatSort` for sorting
- Use `MatPaginator` for pagination
- Use `MatDialog` for dialogs

## Data Management

### Services
- Use services for data access and business logic
- Use services for state management
- Use services for API communication
- Use dependency injection for service consumption

### GraphQL Integration
- Use generated TypeScript types from GraphQL schema
- Use Apollo Client for GraphQL queries and mutations
- Use query variables for parameterized queries
- Use optimistic UI updates for mutations

### State Management
- Use services with `BehaviorSubject` for state management
- Use the async pipe for subscribing to observables in templates
- Use the `shareReplay` operator for sharing observable results
- Use the `distinctUntilChanged` operator to prevent unnecessary updates

## Forms and Validation

### Form Structure
- Use reactive forms for complex forms
- Use form groups for organizing related form controls
- Use form arrays for dynamic form elements
- Use form builders for creating forms

### Validation
- Use built-in validators for common validations
- Use custom validators for complex validations
- Use error messages for user feedback
- Use form status for form-level validation

## Routing

### Route Configuration
- Use lazy loading for feature modules
- Use route guards for protected routes
- Use route resolvers for pre-loading data
- Use route parameters for dynamic routes

### Navigation
- Use the Router service for programmatic navigation
- Use routerLink directive for declarative navigation
- Use route parameters for passing data between routes
- Use query parameters for optional route data

## Error Handling

### Error Display
- Use error properties in components for displaying errors
- Use error interceptors for handling HTTP errors
- Use error boundaries for handling component errors
- Use user-friendly error messages

### Error Recovery
- Use retry logic for transient errors
- Use fallback UI for permanent errors
- Use error logging for debugging
- Use error reporting for monitoring

## Performance Optimization

### Change Detection
- Use OnPush change detection strategy
- Use pure pipes for transforming data
- Use trackBy functions for ngFor loops
- Use memoization for expensive calculations

### Rendering Optimization
- Use virtual scrolling for large lists
- Use lazy loading for routes
- Use deferred loading for non-critical content
- Use web workers for CPU-intensive tasks

## Testing

### Unit Testing
- Use Jest for unit testing
- Use TestBed for component testing
- Use test doubles (mocks, stubs, spies) for dependencies
- Use snapshot testing for UI components

### Integration Testing
- Use Cypress for end-to-end testing
- Use test data generators
- Use test cleanup to remove test data
- Use test coverage reports

## Accessibility

### ARIA Attributes
- Use ARIA roles for semantic meaning
- Use ARIA labels for screen readers
- Use ARIA described-by for additional descriptions
- Use ARIA live regions for dynamic content

### Keyboard Navigation
- Use proper tab order
- Use keyboard shortcuts
- Use focus management
- Use visible focus indicators

## Internationalization

### Text Extraction
- Use i18n markers for text extraction
- Use translation files for different languages
- Use translation pipes for dynamic text
- Use locale-specific formatting

### Localization
- Use locale-specific date and number formatting
- Use locale-specific currency formatting
- Use locale-specific collation for sorting
- Use locale-specific text direction
