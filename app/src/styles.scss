/* You can add global styles to this file, and also import other style files */
@import "~@angular/material/prebuilt-themes/indigo-pink.css";
@import "src/assets/css/custom-theme.scss";

html,
body {
  height: 100%;
}
body {
  margin: 0;
  font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif;
}

.mb-2 {
  margin-bottom: 20px;
}

.logout {
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 100px;
}

.mat-datepicker-content-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.mat-datepicker-content {
  box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2),
    0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);
  display: block;
  border-radius: 4px;
  background-color: var(--mat-datepicker-calendar-container-background-color);
  color: var(--mat-datepicker-calendar-container-text-color);
}

/* Highlight the selected date */
.mat-calendar-body-selected {
  background-color: #3f51b5 !important; /* Background color for selected date */
  color: white !important; /* Text color for contrast */
  border-radius: 50%; /* Circular shape for the date */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* Shadow for depth */
  transition: background-color 0.3s ease, box-shadow 0.3s ease; /* Smooth transition */
}

/* Highlight the current date (today) */
.mat-calendar-body-today:not(.mat-calendar-body-selected) {
  border: 2px solid #ff4081 !important; /* Border color for current date */
  border-radius: 50%; /* Circular shape for the date */
  background-color: transparent !important; /* Transparent background if not selected */
  color: #ff4081 !important; /* Text color for today */
}

/* Hover effect for the selected date */
.mat-calendar-body-selected:hover {
  background-color: #303f9f !important; /* Darker shade on hover */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); /* Enhanced shadow on hover */
}

/* Optional: Style the focus state for accessibility */
.mat-calendar-body-selected:focus,
.mat-calendar-body-today:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.5); /* Focus ring */
}
.mat-mdc-option.select-all-option-custom {
  padding-left: 3px !important;

  .mat-mdc-option-pseudo-checkbox {
    display: none !important;
  }
}

