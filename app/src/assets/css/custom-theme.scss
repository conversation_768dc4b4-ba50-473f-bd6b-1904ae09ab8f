@use '@angular/material' as mat;

// Light Theme Text
$dark-text: #000000;
$dark-primary-text: rgba($dark-text, 0.87);
$dark-accent-text: rgba($dark-primary-text, 0.54);
$dark-disabled-text: rgba($dark-primary-text, 0.38);
$dark-dividers: rgba($dark-primary-text, 0.12);
$dark-focused: rgba($dark-primary-text, 0.12);

// Dark Theme text
$light-text: #ffffff;
$light-primary-text: $light-text;
$light-accent-text: rgba($light-primary-text, 0.7);
$light-disabled-text: rgba($light-primary-text, 0.5);
$light-dividers: rgba($light-primary-text, 0.12);
$light-focused: rgba($light-primary-text, 0.12);

// Theme Config

$mat-primary: (
  main: #009fe4,
  lighter: #b3e2f7,
  darker: #0083d9,
  200: #009fe4,
  // For slide toggle,
  contrast:
    (
      main: $light-primary-text,
      lighter: $dark-primary-text,
      darker: $light-primary-text,
    ),
);
$primary-palette: mat.m2-define-palette($mat-primary, main, lighter, darker);

$mat-accent: (
  main: #000000,
  lighter: #b3b3b3,
  darker: #000000,
  200: #000000,
  // For slide toggle,
  contrast:
    (
      main: $light-primary-text,
      lighter: $dark-primary-text,
      darker: $light-primary-text,
    ),
);
$accent-palette: mat.m2-define-palette($mat-accent, main, lighter, darker);

$mat-warn: (
  main: #e6007e,
  lighter: #f8b3d8,
  darker: #db0061,
  200: #e6007e,
  // For slide toggle,
  contrast:
    (
      main: $light-primary-text,
      lighter: $dark-primary-text,
      darker: $light-primary-text,
    ),
);
$warn-palette: mat.m2-define-palette($mat-warn, main, lighter, darker);

// Define the theme
$my-theme: mat.m2-define-light-theme((
  color: (
    primary: $primary-palette,
    accent: $accent-palette,
    warn: $warn-palette,
  ),
  density: 0,
));

// Apply the theme to the application
@include mat.all-component-themes($my-theme);