# Sync Jobs Component

## Overview

The Sync Jobs component provides a comprehensive view of synchronization operations between the system and external data sources. It allows users to monitor the status, timing, and results of various synchronization jobs, helping administrators track data integration processes and troubleshoot any issues that may arise.

## Key Features

- **Sync Job Listing**: View all synchronization jobs in a sortable, filterable table
- **Status Monitoring**: Quickly identify successful, in-progress, and failed sync operations
- **Performance Metrics**: See execution times and item counts for each synchronization job
- **Search Functionality**: Filter jobs by type, project, or status
- **Pagination**: Navigate through large sets of sync job records efficiently

## How to Use

### Viewing Sync Jobs

1. The component displays a table of synchronization jobs with the following columns:
   - **Sync Type**: The type of synchronization operation (e.g., Projects, Activities, ActivityCodes)
   - **Project ID**: The identifier of the project being synchronized (if applicable)
   - **Timestamp**: The date and time when the sync job was initiated
   - **Status**: The current status of the sync job (color-coded for quick identification)
   - **Execution Time**: How long the sync job took to complete (in seconds)
   - **Items Processed**: The total number of items processed during the sync operation

2. By default, sync jobs are sorted by timestamp in descending order (newest first)

### Filtering Sync Jobs

1. Use the search field at the top of the component to filter sync jobs
2. Enter text to search across sync type, project ID, and status fields
3. The table will automatically update to show only matching records
4. Clear the search field to show all sync jobs again

### Sorting Sync Jobs

1. Click on any column header to sort the table by that column
2. Click again to toggle between ascending and descending order
3. A small arrow indicator shows the current sort direction

### Navigating Through Pages

1. Use the pagination controls at the bottom of the table to navigate between pages
2. Change the number of items per page using the dropdown in the pagination area
3. The component automatically loads more sync jobs as needed when navigating through pages

### Understanding Status Colors

The status column is color-coded to help quickly identify the state of each sync job:
- **Green**: Succeeded - The sync job completed successfully
- **Orange**: In Progress - The sync job is currently running
- **Red**: Failed - The sync job encountered an error and did not complete successfully

## Technical Notes

- The component uses a hybrid pagination approach:
  - Sync jobs are fetched in large batches from the server
  - Client-side pagination is used for smooth navigation
  - Additional batches are loaded as needed when scrolling through pages
- Search filtering happens on the client side for immediate response
- The "Items Processed" column shows the total count of all entities processed during the sync operation

## Troubleshooting

- **No sync jobs visible**: Ensure that synchronization operations have been performed in the system
- **Search not returning expected results**: Try using more general search terms or clear the filter
- **Failed sync jobs**: Check the system logs for more detailed error information about failed synchronization jobs

---

This component is primarily used by system administrators to monitor data synchronization processes and troubleshoot integration issues.
