# User Management Component

## Overview

The User Management component provides a comprehensive interface for administrators to manage users and groups within the system. It allows for creating, viewing, and deleting users and groups, assigning users to groups, and managing user-specific attributes like disciplines and equipment.

## Key Features

- **User Management**: Create, view, and delete users
- **Group Management**: Create, view, and delete groups of different types
- **Group Assignment**: Assign users to groups and remove them from groups
- **User Attributes**: Manage user-specific attributes like disciplines and equipment
- **Tabbed Interface**: Separate tabs for user and group management

## How to Use

### Managing Users

#### Creating a New User

1. Navigate to the "Users" tab
2. In the "Create User" section, enter the required information:
   - Username (required)
   - Email (optional, but must be valid if provided)
3. Click the "Create User" button
4. A success message will appear when the user is created successfully

#### Viewing Users

1. All users are displayed in the table below the "Create User" section
2. The table shows each user's username, email, and assigned groups
3. Click on any user row to select that user and view their details

#### Deleting a User

1. In the users table, locate the user you want to delete
2. Click the delete (trash) icon in the "Actions" column
3. Confirm the deletion when prompted
4. A success message will appear when the user is deleted successfully

#### Managing User Groups

1. To assign a user to a group:
   - Find the user in the users table
   - Click the group add (person+) icon in the "Actions" column
   - Select the desired group from the dropdown menu
   - The user will be immediately assigned to the selected group
2. To remove a user from a group:
   - Find the user in the users table
   - Locate the group chip in the "Groups" column
   - Click the close (X) icon on the group chip
   - The user will be immediately removed from the group

#### Managing User Details

1. Click on any user in the table to select them and view their details
2. The user details section will appear below the table, showing:
   - Basic information (username, email, creation date)
   - Disciplines
   - Equipment

#### Managing User Disciplines

1. Select a user to view their details
2. In the "Disciplines" card:
   - To add a discipline: Type the discipline name in the input field and press Enter
   - To remove a discipline: Click the close (X) icon on the discipline chip
3. Changes are saved automatically

#### Managing User Equipment

1. Select a user to view their details
2. In the "Equipment" card:
   - To add equipment: Type the equipment name in the input field and press Enter
   - To remove equipment: Click the close (X) icon on the equipment chip
3. Changes are saved automatically

### Managing Groups

#### Creating a New Group

1. Navigate to the "Groups" tab
2. In the "Create Group" section, enter the required information:
   - Name (required)
   - Type (required, select from dropdown)
   - Description (optional)
3. Click the "Create Group" button
4. A success message will appear when the group is created successfully

#### Viewing Groups

1. All groups are displayed in the table below the "Create Group" section
2. The table shows each group's name, type, and description

#### Deleting a Group

1. In the groups table, locate the group you want to delete
2. Click the delete (trash) icon in the "Actions" column
3. Confirm the deletion when prompted
4. A success message will appear when the group is deleted successfully

## Technical Notes

- Group types include Worker, Operator, and Manager, each with different permissions
- User changes (group assignments, disciplines, equipment) are saved immediately
- The component shows a loading spinner during API operations

## Troubleshooting

- **User creation fails**: Ensure the username is unique and all required fields are filled
- **Group creation fails**: Ensure the group name is unique and all required fields are filled
- **Group assignment fails**: Ensure the user and group both exist and the user is not already in the group
- **Changes not saving**: Check your network connection and ensure you have administrative permissions

---

This component is part of the administrative section of the application and is typically only accessible to users with administrative privileges.
