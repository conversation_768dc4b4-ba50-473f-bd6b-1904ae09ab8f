# Activity List Component

## Overview

The Activity List component is a responsive, interactive interface for viewing and managing activities within projects. It provides users with the ability to browse, search, filter, and sort activities, making it easy to find and access specific tasks.

## Key Features

- **Project Selection**: Choose which project's activities to display
- **Activity Browsing**: View activities in a paginated list
- **Sorting**: Arrange activities by date (newest or oldest first)
- **Filtering**: Filter activities by timeframe (all, today only, today and unfinished)
- **Searching**: Search for activities by text across multiple fields

## How to Use

### Selecting a Project

1. Use the project selector dropdown in the header area to choose which project's activities to display
2. The list will automatically update to show activities from the selected project

### Sorting Activities

1. Click the sort button (↑) in the header area
2. Select either:
   - "Oldest First" to sort activities from earliest to latest date
   - "Newest First" to sort activities from latest to earliest date

### Filtering Activities

1. Click the filter button (funnel icon) in the header area
2. Select from the following options:
   - "All Activities" to show all activities regardless of date
   - "Show Today Only" to display only activities scheduled for today
   - "Show Today and Unfinished" to display today's activities plus any past activities that aren't completed

### Searching for Activities

1. Click the filter button, then select "Search" to toggle the search bar
2. Enter text in the search field to filter activities
3. The search will match against:
   - Activity name
   - Dates
   - Progress percentage
   - Contractor name
4. Clear the search by clicking the X button or removing all text

### Navigating Through Activities

1. Browse through activities using the pagination controls at the bottom
2. Change the number of items per page using the dropdown in the pagination area
3. Use the first/last page buttons to quickly navigate to the beginning or end of the list

### Viewing Activity Details

Click on any activity item to navigate to its detailed view page

## Technical Notes

- Activities are loaded when a project is selected
- Filtering happens client-side for immediate response
- The component tracks activities by ID for optimal rendering performance

## Troubleshooting

- **No activities shown**: Ensure a project is selected and check if any filters are active
- **Search returning no results**: Try broadening your search terms or clearing filters
- **Loading indicator persists**: If the loading state doesn't resolve, try selecting a different project or refreshing the page

---

This component is part of the larger activity management system and integrates with the activity detail views and project management features.
