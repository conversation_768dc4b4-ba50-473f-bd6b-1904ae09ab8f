# Project Admin Component

## Overview

The Project Admin component provides an administrative interface for managing projects within the system. It allows administrators to view, search, and update project settings, including visibility, synchronization status, and access permissions.

## Key Features

- **Project Listing**: View all projects in a sortable, filterable table
- **Project Search**: Quickly find projects by name, ID, or other attributes
- **Project Selection**: Select a project to view or edit its details
- **Project Updates**: Modify project settings such as visibility and synchronization status
- **Pagination**: Navigate through large sets of projects efficiently

## How to Use

### Viewing Projects

1. The component displays a table of projects with columns for ID, name, status, synchronization status, and visibility
2. Projects are initially loaded in batches for optimal performance
3. Click on any column header to sort the table by that column
4. Use the pagination controls at the bottom to navigate between pages

### Searching for Projects

1. Enter search text in the filter field above the table
2. The table will automatically filter to show only projects that match your search term
3. Filtering works across all visible columns
4. The search is case-insensitive and matches partial text

### Selecting and Editing a Project

1. Click on any row in the table to select that project
2. The selected row will be highlighted in gray
3. The project details will appear below the table in the project-details component
4. Make changes to the project settings as needed:
   - Toggle visibility status
   - Toggle synchronization status
   - Modify group permissions

### Updating Project Settings

1. After making changes in the project details component, the changes will be automatically saved
2. A success message will appear when the project is updated successfully
3. The project list will refresh to show the updated information

### Managing Pagination

1. Use the pagination controls at the bottom of the table to navigate between pages
2. Change the number of items per page using the dropdown in the pagination area
3. The component automatically loads more projects as needed when navigating through pages

## Technical Notes

- The component uses a hybrid pagination approach:
  - Projects are fetched in large batches from the server
  - Client-side pagination is used for smooth navigation
  - Additional batches are loaded as needed when scrolling through pages
- Project updates are sent to the server immediately when changes are made
- The table supports dynamic sorting on any column

## Troubleshooting

- **Projects not loading**: Check your network connection and permissions
- **Changes not saving**: Ensure you have the necessary administrative permissions
- **Search not working**: Make sure you're searching for text that appears in the visible columns

---

This component is part of the administrative section of the application and is typically only accessible to users with administrative privileges.
