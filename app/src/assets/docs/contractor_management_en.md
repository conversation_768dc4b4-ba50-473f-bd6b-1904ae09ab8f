# Contractor Management Component

## Overview

The Contractor Management component provides an interface for administrators to manage contractors within the system. It allows for creating, viewing, updating, and deleting contractors, as well as assigning users to contractors and managing these relationships.

## Key Features

- **Contractor Creation**: Add new contractors to the system
- **Contractor Editing**: Update existing contractor information
- **Contractor Deletion**: Remove contractors from the system
- **User Assignment**: Assign users to contractors and remove these assignments
- **Contractor Listing**: View all contractors in a tabular format

## How to Use

### Managing Contractors

#### Creating a New Contractor

1. In the "Create Contractor" section at the top of the page, enter the required information:
   - Contractor Number (required)
   - Name (required)
   - Description (optional)
2. Click the "Create Contractor" button
3. A success message will appear when the contractor is created successfully

#### Viewing Contractors

1. All contractors are displayed in the table below the creation form
2. The table shows each contractor's number, name, description, and assigned users

#### Editing a Contractor

1. In the contractors table, locate the contractor you want to edit
2. Click the edit (pencil) icon in the "Actions" column
3. The form at the top of the page will be populated with the contractor's current information
4. Make the necessary changes to the contractor's details
5. Click the "Update Contractor" button
6. A success message will appear when the contractor is updated successfully

#### Canceling an Edit

1. If you've started editing a contractor but want to cancel:
2. Click the "Cancel" button next to the "Update Contractor" button
3. The form will be cleared and return to "Create Contractor" mode

#### Deleting a Contractor

1. In the contractors table, locate the contractor you want to delete
2. Click the delete (trash) icon in the "Actions" column
3. Confirm the deletion when prompted
4. A success message will appear when the contractor is deleted successfully

### Managing User Assignments

#### Assigning a User to a Contractor

1. In the contractors table, locate the contractor you want to assign a user to
2. Click the person add (person+) icon in the "Actions" column
3. Select the desired user from the dropdown menu
4. The user will be immediately assigned to the selected contractor
5. The user will appear in the "Users" column for that contractor

#### Removing a User from a Contractor

1. In the contractors table, locate the contractor with the user you want to remove
2. In the "Users" column, find the user chip you want to remove
3. Click the close (X) icon on the user chip
4. The user will be immediately removed from the contractor

## Technical Notes

- User assignments to contractors are updated in real-time
- The component shows a loading spinner during API operations
- Form validation ensures that required fields are filled before submission

## Troubleshooting

- **Contractor creation fails**: Ensure the contractor number is unique and all required fields are filled
- **User assignment fails**: Ensure both the user and contractor exist in the system
- **Changes not saving**: Check your network connection and ensure you have administrative permissions
- **User already assigned**: Users can only be assigned to a contractor once; already assigned users will be disabled in the dropdown menu

---

This component is part of the administrative section of the application and is typically only accessible to users with administrative privileges.
