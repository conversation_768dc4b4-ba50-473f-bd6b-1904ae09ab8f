# Activity Component

## Overview

The Activity component provides a detailed view of a single activity, allowing users to view and manage all aspects of an activity through a tabbed interface. It displays key information like activity name, ID, and status, and provides access to detailed information, progress tracking, relationships, evidence uploads, and activity logs.

## Key Features

- **Activity Details**: View comprehensive information about a specific activity
- **Progress Tracking**: Update and monitor activity progress
- **Relationship Management**: View and manage relationships with other activities
- **Evidence Management**: Upload and view photos and other evidence
- **Activity Logs**: Track history and changes to the activity
- **Status Indicators**: Visual indicators of activity status and conditions

## How to Use

### Accessing an Activity

- Activities can be accessed by clicking on an activity in the Activity List
- The component loads the activity details based on the ID in the URL

### Understanding the Activity Header

- The header displays the activity name, ID, and status chips
- Status chips indicate the current state of the activity (e.g., In Progress, Completed)
- An "Unable to Work" chip appears when applicable

### Using the Tabbed Interface

The Activity component organizes information into several tabs:

#### Progress Tab

- View and update the activity's progress
- Mark activities as complete or update percentage completion
- Record issues that prevent work from proceeding

#### Details Tab

- View comprehensive information about the activity
- See dates, descriptions, and other metadata
- View work order information when applicable

#### Relationships Tab

- See how this activity relates to other activities
- View predecessor and successor relationships
- Understand dependencies between activities

#### Evidences Tab

- Upload photos and other evidence related to the activity
- View previously uploaded evidence
- Document the activity's progress visually

#### Logs Tab

- View a chronological history of changes to the activity
- Track who made changes and when
- Monitor the activity's progress over time

### Handling Access Denied

- If you don't have permission to view an activity, an access denied message will appear
- Limited information may still be available in the Details tab

## Technical Notes

- Activity data is loaded when the component initializes
- Updates to the activity are sent to the server immediately
- The component handles both route-based navigation and direct embedding

## Troubleshooting

- **Activity not loading**: Ensure you have the correct permissions and a valid activity ID
- **Unable to update progress**: Check your permissions and network connectivity
- **Missing tabs**: Some tabs may be hidden based on your permissions or the activity state

---

This component is part of the larger activity management system and integrates with the activity list and project management features.
