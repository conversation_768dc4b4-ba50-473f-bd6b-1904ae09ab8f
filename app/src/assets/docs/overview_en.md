# TAEX - Task Activity Execution System

## Overview

TAEX (Task Activity Execution System) is a comprehensive project and activity management platform designed to streamline workflow processes across organizations. The system enables efficient tracking, management, and execution of activities within projects, supporting various roles from workers in the field to administrators managing the system.

TAEX provides a seamless integration between planning tools and execution tracking, allowing organizations to:

- Synchronize project data from external planning systems
- Track activity progress in real-time
- Document work with photos and evidence
- Manage relationships between activities
- Assign work to contractors and team members
- Generate reports and monitor performance metrics

The system is built with a role-based access control model, providing different capabilities to users based on their responsibilities within the organization.

## Documentation by User Role

### For Workers

Workers are responsible for executing activities and updating their progress. The following documentation will help workers navigate the system:

- [Activity List](docs/activities_en) - View and filter assigned activities
- [Activity Details](docs/activity_details_en) - Update progress, add photos, and manage activity information

Workers primarily interact with these components to:
- Find their assigned activities
- Update activity progress
- Document work with photos
- Report issues that prevent work completion
- View relationships with other activities

### For Operators and Managers

Operators and managers oversee projects and monitor activity execution across teams. The following documentation will help operators and managers effectively use the system:

- [Manager View](docs/manager_view_en) - Comprehensive dashboard for monitoring and managing activities across projects

Operators and managers use these components to:
- Filter and search for specific activities
- Perform bulk operations on multiple activities
- Monitor progress across projects
- Export data for reporting
- View detailed activity information

### For Administrators

Administrators are responsible for system configuration, user management, and data synchronization. The following documentation will help administrators maintain the system:

- [Project Admin](docs/project_admin_en) - Manage projects and their settings
- [User Management](docs/user_management_en) - Create and manage users and groups
- [Contractor Management](docs/contractor_management_en) - Manage contractors and their assignments
- [Sync Jobs](docs/sync_jobs_en) - Monitor data synchronization with external systems

Administrators use these components to:
- Configure project visibility and synchronization settings
- Manage user accounts and permissions
- Assign users to groups and contractors
- Monitor system integration health
- Troubleshoot synchronization issues

## Getting Started

New users should begin by reviewing the documentation relevant to their role:

1. **Workers** should start with the [Activity List](docs/activities_en) documentation
2. **Operators and Managers** should start with the [Manager View](docs/manager_view_en) documentation
3. **Administrators** should review all administrative documentation, beginning with [User Management](docs/user_management_en)

For technical support or questions about the system, please contact your system administrator.
