import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

// Routing
import { AppRoutingModule } from './app-routing.module';

// Shared and Core
import { CoreModule } from './core/core.module';

// Forms
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// HTTP and Translating
import { HttpClient, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';

// Charts
import { BaseChartDirective, provideCharts, withDefaultRegisterables } from 'ng2-charts';
import 'chartjs-adapter-date-fns';


// Components
import { AppComponent } from './app.component';
import { ActivityListComponent } from './components/activity-list/activity-list.component';
import { ActivityItemComponent } from './components/activity-list/activity-item/activity-item.component';
import { ActivityComponent } from './components/activity/activity.component';
import { ActivityDetailsComponent } from './components/activity/activity-details/activity-details.component';
import { ActivityRelationshipsComponent } from './components/activity/activity-relationships/activity-relationships.component';
import { ProgressComponent } from './components/activity/progress/progress.component';
import { LogsComponent } from './components/activity/logs/logs.component';
import { DashboardComponent } from './components/dashboard/dashboard.component';
import { PieChartProgressComponent } from './components/dashboard/pie-chart-progress/pie-chart-progress.component';
import { BurndownChartComponent } from './components/dashboard/burndown-chart/burndown-chart.component';
import { SCurveChartComponent } from './components/dashboard/s-curve-chart/s-curve-chart.component';
import { ManagerViewComponent } from './components/manager-view/manager-view.component';
import { ActivitySearchComponent } from './components/manager-view/activity-search/activity-search.component';
import { SetProgressDialogComponent } from './components/manager-view/set-progress-dialog/set-progress-dialog.component';
import { SetStatusDialogComponent } from './components/manager-view/set-status-dialog/set-status-dialog.component';
import { AboutComponent } from './components/about/about.component';
import { UserManagementComponent } from './components/user-management/user-management.component';
import { ContractorManagementComponent } from './components/contractor-management/contractor-management.component';

// Material (via a custom module)
import { MaterialModule } from './material.module';
import { PhotoListAndCaptureComponent } from './components/activity/photo-list-and-capture/photo-list-and-capture.component';
import { UnableToWorkComponent } from './components/dashboard/unable-to-work/unable-to-work.component';
import { ActivitiesByStatusDisciplineComponent } from './components/dashboard/activities-by-status-discipline/activities-by-status-discipline.component';
import { LandingPageComponent } from './components/landing-page/landing-page.component';
import { ColumnSelectorDialogComponent } from './components/manager-view/column-selector-dialog/column-selector-dialog.component';
import { ProjectAdminComponent } from './components/project-admin/project-admin.component';
import { ProjectSelectorComponent } from './components/manager-view/project-selector/project-selector.component';
import { BulkActionsComponent } from './components/manager-view/bulk-actions/bulk-actions.component';
import { ProjectDetailsComponent } from './components/project-details/project-details.component';
import { SyncJobsComponent } from './components/sync-jobs/sync-jobs.component';
import { MarkdownModule } from 'ngx-markdown';
import { DocPageComponent } from './components/doc-page/doc-page.component';

export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

@NgModule({ declarations: [
        AppComponent,
        ActivityListComponent,
        ActivityItemComponent,
        ActivityComponent,
        ActivityDetailsComponent,
        ActivityRelationshipsComponent,
        ProgressComponent,
        DashboardComponent,
        AboutComponent,
        PieChartProgressComponent,
        BurndownChartComponent,
        SCurveChartComponent,
        ManagerViewComponent,
        ActivitySearchComponent,
        SetProgressDialogComponent,
        SetStatusDialogComponent,
        LogsComponent,
        ColumnSelectorDialogComponent,
        PhotoListAndCaptureComponent,
        UnableToWorkComponent,
        ActivitiesByStatusDisciplineComponent,
        LandingPageComponent,
        ProjectAdminComponent, 
        ProjectDetailsComponent,
        ProjectSelectorComponent,
        BulkActionsComponent,
        UserManagementComponent,
        ContractorManagementComponent,
        DocPageComponent,
        SyncJobsComponent
    ],
    bootstrap: [AppComponent],
    imports: [BrowserModule,
        CoreModule,
        AppRoutingModule,
        BrowserAnimationsModule,
        MaterialModule,
        ReactiveFormsModule,
        FormsModule,
        BaseChartDirective,
        MarkdownModule.forRoot(),
        TranslateModule.forRoot({
            loader: {
                provide: TranslateLoader,
                useFactory: HttpLoaderFactory,
                deps: [HttpClient]
            }
        })], providers: [provideHttpClient(withInterceptorsFromDi()), provideCharts(withDefaultRegisterables())] })
export class AppModule { }
