import { Component, OnInit } from '@angular/core';
import { AwsRum } from 'aws-rum-web';
import { environment } from '../environments/environment';
import { Router, NavigationEnd } from '@angular/router';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit {
  title = environment.appTitle;
  awsRum: AwsRum;

  constructor(private router: Router) {
    // Ensure AWS RUM is initialized if not running on localhost
    if (window.location.hostname !== 'localhost') {
      try {
        const config = environment.Rum.CONFIG;
        this.awsRum = new AwsRum(
          environment.Rum.APPLICATION_ID,
          environment.Rum.APPLICATION_VERSION,
          environment.Rum.APPLICATION_REGION,
          config
        );
      } catch (error) {
        console.log('RUM initialization failed:', error);
      }
    }
  }

  ngOnInit(): void {
    this.trackRouteChanges();
  }

  trackRouteChanges() {
    // Subscribe to router events
    this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        this.logVisit(event.urlAfterRedirects);
      }
    });
  }

  logVisit(url: string) {
    if (this.awsRum) {
      this.awsRum.recordEvent('site_visit', {
        page: url,
        timestamp: new Date().toISOString(),
        userAgent: window.navigator.userAgent
      });
      console.log('Visit logged to:', url);
    } else {
      console.log('AWS RUM not initialized, visit not logged.');
    }
  }

}
