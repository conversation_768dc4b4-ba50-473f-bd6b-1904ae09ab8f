import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ProfileComponent } from './components/profile/profile.component';
import { SignInComponent } from './components/sign-in/sign-in.component';
import { ROUTER_PATHS } from '../shared/constant.routing';
import { authGuard } from './guards/auth.guard';

const routes: Routes = [
  { path: ROUTER_PATHS.PROFILE, component: ProfileComponent},
  { path: ROUTER_PATHS.SIGN_IN, component: SignInComponent, canActivate:[authGuard] },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CoreRoutingModule {}
