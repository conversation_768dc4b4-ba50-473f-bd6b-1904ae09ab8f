import { Component, OnInit } from '@angular/core';
import { environment } from 'src/environments/environment';
import { AuthService } from '../../../services/auth.service';
import { Router } from '@angular/router';
import { BehaviorSubject } from 'rxjs';
import { ROUTER_PATHS } from 'src/app/shared/constant.routing';

@Component({
  selector: 'app-core-layout',
  templateUrl: './core-layout.component.html',
  styleUrls: ['./core-layout.component.css']
})
export class CoreLayoutComponent {
  title = environment.appTitle;

  constructor(
    public authService: AuthService,
    private router: Router
  ) {}

  isLoginPage() {
    return this.router.url === '/' + ROUTER_PATHS.SIGN_IN
  }

  login() {
    this.router.navigate([ROUTER_PATHS.SIGN_IN]);
  }
}
