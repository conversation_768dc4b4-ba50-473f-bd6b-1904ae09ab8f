<mat-sidenav-container class="sidenav-container">
    <app-global-loading></app-global-loading>
    <mat-sidenav #sidenav mode="side" class="sidenav">
        <app-navigation></app-navigation>
    </mat-sidenav>
    <mat-sidenav-content class="alda-sidenav-content">
  
        <mat-toolbar color="accent" class="no-print">
            <button *ngIf="authService.authenticationSubject$ | async" mat-icon-button (click)="sidenav.toggle()">
                <mat-icon>menu</mat-icon>
            </button>
            <img class="logo" [routerLink]="['/']" src="assets/cov-logo.png" alt="Logo">
            <a class="toolbar-title" [routerLink]="['/']">{{ title }}</a>
            <span class="spacer"></span>
            <ng-container *ngIf="authService.authenticationSubject$ | async; else loginButton">
                <img class="profile-picture" [routerLink]="['/profile']" src="assets/dummy-profile-picture.jpg"
                    alt="Profile Picture">
            </ng-container>
            <ng-template #loginButton>
                <button *ngIf="!isLoginPage()" mat-raised-button color="primary" (click)="login()">
                    <mat-icon>login</mat-icon>
                    Login
                </button>
            </ng-template>
        </mat-toolbar>
  
        <div class="main-content">
            <ng-content></ng-content> <!-- Placeholder for routed content -->
        </div>
        <footer class="no-print"></footer>
    </mat-sidenav-content>
  </mat-sidenav-container>
