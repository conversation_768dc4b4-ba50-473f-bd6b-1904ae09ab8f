<!-- navigation.component.html -->
<mat-toolbar>Navigation</mat-toolbar>
<mat-nav-list>
    <a mat-list-item [routerLink]="['/']">{{ 'MENU.HOME' | translate }}</a>
    <a mat-list-item [routerLink]="['/activity-list']" *ngIf="isWorker || isAdmin">{{ 'MENU.MY_ACTIVITIES' | translate }}</a>
    <mat-divider *ngIf="isWorker || isAdmin"></mat-divider>
    <a mat-list-item [routerLink]="['/manager-view']" *ngIf="isOperatorOrManager || isAdmin">{{ 'MENU.MANAGER_VIEW' | translate }}</a>
    <a mat-list-item [routerLink]="['/dashboard']" *ngIf="isOperatorOrManager || isAdmin">{{ 'MENU.DASHBOARD' | translate }}</a>
    <mat-divider *ngIf="isOperatorOrManager || isAdmin"></mat-divider>
    <a mat-list-item [routerLink]="['/project-admin']" *ngIf="isOperatorOrManager || isAdmin">{{ 'MENU.PROJECT_ADMIN' | translate }}</a>
    <a mat-list-item [routerLink]="['/user-management']" *ngIf="isAdmin">{{ 'MENU.USER_MANAGEMENT' | translate }}</a>
    <a mat-list-item [routerLink]="['/contractor-management']" *ngIf="isAdmin">{{ 'MENU.CONTRACTOR_MANAGEMENT' | translate }}</a>
    <a mat-list-item [routerLink]="['/sync-jobs']" *ngIf="isAdmin">{{ 'MENU.SYNC_JOBS' | translate }}</a>
    <mat-divider></mat-divider>
    <a mat-list-item [routerLink]="['/docs/overview_en']">{{ 'MENU.USER_MANUAL' | translate }}</a>
    <a mat-list-item [routerLink]="['/about']">{{ 'MENU.ABOUT' | translate }}</a>
</mat-nav-list>
