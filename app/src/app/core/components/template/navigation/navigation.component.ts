import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Subscription } from 'rxjs';
import { AuthService } from 'src/app/core/services/auth.service';
import { UserService } from 'src/app/services/user.service';

@Component({
  selector: 'app-navigation',
  templateUrl: './navigation.component.html',
  styleUrls: ['./navigation.component.css']
})
export class NavigationComponent implements OnInit, OnDestroy {
  private authSubscription: Subscription;
  roles: string[] = [];
  isWorker = false;
  isOperatorOrManager = false;
  isAdmin = false;
  username: string = '';

  constructor(
    public authService: AuthService,
    private userService: UserService
  ) { }

  ngOnInit() {
    this.loadUserGroups();
    
    // Subscribe to authentication state changes
    this.authSubscription = this.authService.authenticationSubject$.subscribe(() => {
      this.loadUserGroups();
    });
  }
  
  ngOnDestroy() {
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
  }

  async loadUserGroups() {
    try {
      // Get basic user groups from auth service
      const userGroups = await this.authService.getUserGroups();
      this.roles = Array.isArray(userGroups) ? userGroups : [];
      this.isAdmin = this.roles.includes('admin');
      
      // Get current user
      const user = await this.authService.getUser();
      if (user) {
        this.username = user.username;
        // Get detailed user info including group types
        this.userService.getUser(this.username).subscribe(userDetails => {
          this.isWorker = userDetails.workerGroups && userDetails.workerGroups.length > 0;
          this.isOperatorOrManager = 
            (userDetails.operatorGroups && userDetails.operatorGroups.length > 0) || 
            (userDetails.managerGroups && userDetails.managerGroups.length > 0);
        });
      }
    } catch (error) {
      console.error('Error loading user groups:', error);
      this.roles = [];
    }
  }

  hasRole(role: string): boolean {
    return this.roles?.includes(role) ?? false;
  }
}
