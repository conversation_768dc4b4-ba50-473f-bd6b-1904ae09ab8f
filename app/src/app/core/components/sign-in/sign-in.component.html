<div class="row justify-content-center my-5">
  <div class="col-4">
    <!-- Sign In Title -->
    <h2 class="text-center">Sign In</h2>

    <!-- Sign In Form -->
    <mat-card>
      <mat-card-content>
        <form (ngSubmit)="signIn()">
          <div class="row">
            <div class="col mb-2">
              <mat-form-field appearance="outline" class="w-100">
                <mat-label>Username</mat-label>
                <input matInput type="name" id="name" name="name" #usernameInput #name="ngModel" [(ngModel)]="user.name" 
                       required autofocus (keydown.enter)="passwordInput.focus()">
              </mat-form-field>
            </div>
          </div>

          <div class="row">
            <div class="col mb-2">
              <mat-form-field appearance="outline" class="w-100">
                <mat-label>Password</mat-label>
                <input matInput [type]="user.showPassword ? 'text' : 'password'" id="password" name="password" 
                       #passwordInput #password="ngModel" [(ngModel)]="user.password" required>
                <button mat-icon-button matSuffix type="button" (click)="user.showPassword = !user.showPassword">
                  <mat-icon>{{ user.showPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                </button>
              </mat-form-field>
            </div>
          </div>

          <div class="row">
            <div class="col d-grid">
              <button mat-raised-button color="primary" type="submit" [disabled]="loading">
                <mat-spinner *ngIf="loading" diameter="20" class="me-2 d-inline-block"></mat-spinner>
                <ng-container *ngIf="!loading">Sign in</ng-container>
              </button>
            </div>
          </div>
        </form>
      </mat-card-content>
    </mat-card>
  </div>
</div>
