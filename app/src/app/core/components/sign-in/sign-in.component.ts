import { Component, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService, IUser } from '../../services/auth.service';
import { MessageService } from '../../services/message.service';


@Component({
  selector: 'app-sign-in',
  templateUrl: './sign-in.component.html',
  styleUrls: ['./sign-in.component.css'],
})
export class SignInComponent implements AfterViewInit {
  @ViewChild('usernameInput') usernameInput!: ElementRef;

  loading: boolean;
  user: IUser;

  constructor(
    private router: Router, 
    private authService: AuthService,
    private messageService: MessageService
  ) {
    this.loading = false;
    this.user = {} as IUser;
  }
  
  ngAfterViewInit(): void {
    // Set focus to username input after view initialization
    setTimeout(() => {
      if (this.usernameInput) {
        this.usernameInput.nativeElement.focus();
      }
    }, 0);
  }

  public signIn(): void {
    this.loading = true;
    this.authService.signIn(this.user)
    .then(() => {
      this.router.navigate(['/']);
    }).catch((err) => {
      this.loading = false;
      console.log(err);
      this.messageService.showError('Sign in failed. Please check your credentials and try again.');
    });
  }

}
