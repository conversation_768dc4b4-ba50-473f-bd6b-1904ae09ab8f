// profile.component.ts
import { Component, OnInit } from '@angular/core';
import { AuthUser } from 'aws-amplify/auth';
import { Router } from '@angular/router'; // Import Router
import { AuthService } from '../../services/auth.service';
import { TranslateService } from '../../services/translate.service';
import { MessageService } from '../../services/message.service';
import { ROUTER_PATHS } from 'src/app/shared/constant.routing';
import { UserService } from 'src/app/services/user.service';
import { Contractor, Group, User } from 'src/app/graphql/generated';

interface UserPermissions {
  managerGroups: Group[];
  operatorGroups: Group[];
  workerGroups: Group[];
  disciplines: string[];
  equipments: string[];
  contractors: Contractor[];
}

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css'],
})
export class ProfileComponent implements OnInit {
  user: AuthUser | null = null;
  currentLanguage: string = '';
  groups: string[] = [];
  permissions: UserPermissions = {
    managerGroups: [],
    operatorGroups: [],
    workerGroups: [],
    disciplines: [],
    equipments: [],
    contractors: []
  };

  constructor(
    private authService: AuthService,
    private translateService: TranslateService,
    private messageService: MessageService,
    private router: Router,
    private userService: UserService
  ) {}

  async ngOnInit(): Promise<void> {
    try {
      // Fetch user and groups from auth service
      this.user = await this.authService.getUser();
      this.groups = await this.authService.getUserGroups();
      
      // Fetch permissions from user service
      if (this.user) {
        this.userService.getUser(this.user.username).subscribe({
          next: (userData: User) => {
            this.permissions = {
              managerGroups: userData.managerGroups || [],
              operatorGroups: userData.operatorGroups || [],
              workerGroups: userData.workerGroups || [],
              disciplines: userData.disciplines?.filter(Boolean) as string[] || [],
              equipments: userData.equipments?.filter(Boolean) as string[] || [],
              contractors: userData.contractors?.filter(Boolean) as Contractor[] || []
            };
          },
          error: (err) => {
            console.error('Error fetching user permissions', err);
            this.messageService.showError('Failed to load user permissions');
          }
        });
      }
    } catch (error) {
      console.error('Error fetching user data', error);
      this.messageService.showError('Failed to load profile data');
    }
    this.currentLanguage = this.translateService.getCurrentLanguage();
  }

  switchLanguage(language: string) {
    this.translateService.useLanguage(language);
    this.messageService.showSuccess(`Language switched to ${language}`);
  }

  async logout() {
    try {
      await this.authService.signOut(); // Call the logout method from AuthService
      this.messageService.showSuccess('Successfully logged out');
      this.router.navigate([ROUTER_PATHS.SIGN_IN]); // Redirect to login page
    } catch (error) {
      console.error('Error during logout', error);
      this.messageService.showError('Failed to logout');
    }
  }
}
