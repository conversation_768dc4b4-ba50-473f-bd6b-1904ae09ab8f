<!-- User Profile Section -->
<div *ngIf="user" class="mb-2">
  <mat-card>
    <mat-card-header>
      <mat-card-title>{{ 'PROFILE.USER_PROFILE' | translate }}</mat-card-title>
  
    </mat-card-header>
    <mat-card-content>
      <p><strong>UserId:</strong> {{ user.userId }}</p>
      <p><strong>Name:</strong> {{ user.username }}</p>
    </mat-card-content>
  </mat-card>
</div>

<!-- Groups Section -->
<div *ngIf="groups && groups.length > 0" class="mb-2">
  <mat-card>
    <mat-card-header>
      <mat-card-title>{{ 'PROFILE.USER_GROUPS' | translate }}</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <ul>
        <li *ngFor="let group of groups">
          {{ group }}
        </li>
      </ul>
    </mat-card-content>
  </mat-card>
</div>

<!-- Permissions Section -->
<div class="mb-2">
  <mat-card>
    <mat-card-header>
      <mat-card-title>{{ 'PROFILE.PERMISSIONS' | translate }}</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <!-- Manager Groups -->
      <div *ngIf="permissions.managerGroups && permissions.managerGroups.length > 0" class="permission-section">
        <h3>{{ 'PROFILE.MANAGER_GROUPS' | translate }}</h3>
        <ul>
          <li *ngFor="let group of permissions.managerGroups">{{ group.name }}</li>
        </ul>
      </div>

      <!-- Operator Groups -->
      <div *ngIf="permissions.operatorGroups && permissions.operatorGroups.length > 0" class="permission-section">
        <h3>{{ 'PROFILE.OPERATOR_GROUPS' | translate }}</h3>
        <ul>
          <li *ngFor="let group of permissions.operatorGroups">{{ group.name }}</li>
        </ul>
      </div>

      <!-- Worker Groups -->
      <div *ngIf="permissions.workerGroups && permissions.workerGroups.length > 0" class="permission-section">
        <h3>{{ 'PROFILE.WORKER_GROUPS' | translate }}</h3>
        <ul>
          <li *ngFor="let group of permissions.workerGroups">{{ group.name }}</li>
        </ul>
      </div>

      <!-- Disciplines -->
      <div *ngIf="permissions.disciplines && permissions.disciplines.length > 0" class="permission-section">
        <h3>{{ 'PROFILE.DISCIPLINES' | translate }}</h3>
        <ul>
          <li *ngFor="let discipline of permissions.disciplines">{{ discipline }}</li>
        </ul>
      </div>

      <!-- Equipments -->
      <div *ngIf="permissions.equipments && permissions.equipments.length > 0" class="permission-section">
        <h3>{{ 'PROFILE.EQUIPMENT_TYPES' | translate }}</h3>
        <ul>
          <li *ngFor="let type of permissions.equipments">{{ type }}</li>
        </ul>
      </div>

      <!-- Contractors -->
      <div *ngIf="permissions.contractors && permissions.contractors.length > 0" class="permission-section">
        <h3>{{ 'PROFILE.CONTRACTORS' | translate }}</h3>
        <ul>
          <li *ngFor="let contractor of permissions.contractors">
            {{ contractor.name }} ({{ contractor.contractorNumber }})
            <span *ngIf="contractor.description" class="contractor-description">- {{ contractor.description }}</span>
          </li>
        </ul>
      </div>

      <!-- No permissions message -->
      <div *ngIf="!permissions.managerGroups.length && !permissions.operatorGroups.length && 
                  !permissions.workerGroups.length && !permissions.disciplines.length && 
                  !permissions.equipments.length && !permissions.contractors.length" class="no-permissions">
        {{ 'PROFILE.NO_PERMISSIONS' | translate }}
      </div>
    </mat-card-content>
  </mat-card>
</div>

<!-- Preferences Section -->
<mat-card class="mb-2">
  <mat-card-header>
    <mat-card-title>{{ 'PROFILE.PREFERENCES' | translate }}</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <div class="language-selection">
      <mat-form-field appearance="fill">
        <mat-label>{{ 'PROFILE.SELECT_LANGUAGE' | translate }}</mat-label>
        <mat-select [(value)]="currentLanguage" (selectionChange)="switchLanguage($event.value)">
          <mat-option value="en">English</mat-option>
          <mat-option value="de">Deutsch</mat-option>
          <!-- Add more languages as needed -->
        </mat-select>
      </mat-form-field>
    </div>
  </mat-card-content>

</mat-card>


<!-- Logout -->
<mat-card  class="logout">
  <mat-card-header>
  <mat-card-title></mat-card-title>
</mat-card-header>
  <mat-card-content>
  
      <button (click)="logout()" mat-button>{{ 'PROFILE.LOGOUT' | translate }}</button>
    
</mat-card-content>
</mat-card>
