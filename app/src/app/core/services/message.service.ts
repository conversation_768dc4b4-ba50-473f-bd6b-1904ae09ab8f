import { Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';

@Injectable({
  providedIn: 'root'
})
export class MessageService {

  constructor(private snackBar: MatSnackBar) { }

  showMessage(message: string, action: string = 'Close', duration: number = 3000, panelClass: string = 'info') {
    this.snackBar.open(message, action, {
      duration: duration,
      panelClass: [panelClass]
    });
  }

  showSuccess(message: string, action: string = 'Close', duration: number = 3000) {
    this.showMessage(message, action, duration, 'success');
  }

  showError(message: string, action: string = 'Close', duration: number = 3000) {
    this.showMessage(message, action, duration, 'error');
  }
}
