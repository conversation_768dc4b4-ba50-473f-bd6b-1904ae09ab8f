import { Injectable } from '@angular/core';
import { TranslateService as NgxTranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})
export class TranslateService {

  private readonly LANGUAGE_KEY = 'selectedLanguage';

  constructor(private translate: NgxTranslateService) {
    const savedLanguage = localStorage.getItem(this.LANGUAGE_KEY);
    if (savedLanguage) {
      this.translate.use(savedLanguage);
    } else {
      this.setDefaultLanguage('en');
    }
  }

  setDefaultLanguage(lang: string) {
    this.translate.setDefaultLang(lang);
    this.useLanguage(lang); // Also set the default language as the current language
  }

  useLanguage(lang: string) {
    this.translate.use(lang);
    localStorage.setItem(this.LANGUAGE_KEY, lang);
  }

  getCurrentLanguage(): string {
    return this.translate.currentLang;
  }

  getBrowserLanguage(): string {
    return this.translate.getBrowserLang() || '';
  }
}
