import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LoadingService {
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private loadingSource = new BehaviorSubject<string | null>(null);

  loading$: Observable<boolean> = this.loadingSubject.asObservable();
  loadingSource$: Observable<string | null> = this.loadingSource.asObservable();

  show(source: string): void {
    this.loadingSubject.next(true);
    this.loadingSource.next(source);
  }

  hide(): void {
    this.loadingSubject.next(false);
    this.loadingSource.next(null);
  }
}
