import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import {Amplify, type ResourcesConfig  } from 'aws-amplify';
import { getCurrentUser, signUp, confirmSignUp, signIn, signOut, fetchAuthSession, AuthSession } from 'aws-amplify/auth';
import { cognitoUserPoolsTokenProvider } from 'aws-amplify/auth/cognito';
import { CookieStorage } from 'aws-amplify/utils';

import { environment } from '../../../environments/environment';

export interface IUser {
  email: string;
  password: string;
  showPassword: boolean;
  code: string;
  name: string;
}

@Injectable({
  providedIn: 'root',
})
export class AuthService {

  private authenticationSubject = new BehaviorSubject<boolean>(false);
  private userGroups = new BehaviorSubject<string[]>([]);
  authConfig: ResourcesConfig['Auth'] = environment.Auth;
  authenticationSubject$ = this.authenticationSubject.asObservable(); 

  constructor() {
    Amplify.configure({
      Auth: this.authConfig
    });

    // Use Cookie Storage as the CloudFront Edge Lambda sets JWTs to Cookies
    cognitoUserPoolsTokenProvider.setKeyValueStorage(new CookieStorage());
    this.isAuthenticated()
    this.getUserGroups()
  }

  public signUp(user: IUser): Promise<any> {
    return signUp({
      username: user.name,
      password: user.password,
    });
  }

  public getAuthSession(): Promise<AuthSession> {
    return fetchAuthSession();
  }

  public confirmSignUp(user: IUser): Promise<any> {
    return confirmSignUp({username: user.name, confirmationCode: user.code});
  }

  public signIn(user: IUser): Promise<any> {
    return signIn({username: user.name, password: user.password})
    .then(() => {
      this.authenticationSubject.next(true);
    });
  }

  public signOut(): Promise<any> {
    return signOut()
    .then(() => {
      this.authenticationSubject.next(false);
    });
  }

  public isAuthenticated(): Promise<boolean> {
    if (this.authenticationSubject.value) {
      return Promise.resolve(true);
    } else {
      return this.getUser()
      .then((user: any) => {
        if (user) {
          this.authenticationSubject.next(true)
          return true;
        } else {
          return false;
        }
      }).catch(() => {
        return false;
      });
    }
  }

  public getUser(): Promise<any> {
    return getCurrentUser()
  }


  public getUserGroups(): Promise<string[]> {
    return fetchAuthSession().then(
      (data: AuthSession) => {
        if (!data.tokens?.accessToken.payload['cognito:groups']) {
          this.userGroups.next([])
          return []; // or throw an error if no groups should be treated as an error
        }
        this.userGroups.next(data.tokens.accessToken.payload['cognito:groups'] as string[])
        return data.tokens.accessToken.payload['cognito:groups'] as string[];
      }
    ).catch(()=> {
      return []
    })
  }

  public isAdmin(): Promise<boolean> {
    return this.getUserGroups().then((groups) => {
      if (!groups) {
        return false;
      }
      return groups.includes('admin');
    });
  }

  hasUserGroup(role: string): boolean {
    return this.userGroups?.getValue().includes(role) ?? false;
  }

  hasUserGroups(roles: string[]): boolean {
    return roles.some(role => this.userGroups?.getValue().includes(role));
  }

  hasAllUserGroups(roles: (string | null | undefined)[]): boolean {
    const validRoles = roles.filter(role => !!role);
    return validRoles.every(role => this.userGroups?.getValue().includes(role));
  }

  public async isUserAdminOrInGroups(groups: string | string[]): Promise<boolean> {
    // Retrieve the user's groups from the AuthService
    const userGroups = await this.getUserGroups();
    
    if (!userGroups) {
      return false;
    }
  
    // Check if the user is an admin
    if (userGroups.includes('admin')) {
      return true;
    }
  
    // Normalize the input groups to an array if necessary
    const groupsArray = typeof groups === 'string'
      ? groups.split(',').map(group => group.trim())
      : groups;
  
    // Check if the user belongs to any of the specified groups
    const isInGroup = groupsArray.some(group => userGroups.includes(group));
  
    // Debug log (optional; remove for production)
    console.log('Authorization check:', { isAdmin: false, isInGroup });
  
    return isInGroup;
  }
  
}
