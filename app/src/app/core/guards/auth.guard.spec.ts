import { TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { Router } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { authGuard } from './auth.guard';
import { ROUTER_PATHS } from 'src/app/shared/constant.routing';

describe('authGuard', () => {
  const mockAuthService = {
    isAuthenticated: jest.fn(),
    getUserGroups: jest.fn()
  };
  
  let router: Router;
  
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      providers: [
        { provide: AuthService, useValue: mockAuthService }
      ]
    });
    
    router = TestBed.inject(Router);
  });
  
  it('should allow access when user is authenticated', async () => {
    mockAuthService.isAuthenticated.mockResolvedValue(true);
    const result = await TestBed.runInInjectionContext(() => 
      authGuard({} as any, {} as any)
    );
    expect(result).toBe(true);
  });
  
  it('should redirect to login when user is not authenticated', async () => {
    mockAuthService.isAuthenticated.mockResolvedValue(false);
    const navigateSpy = jest.spyOn(router, 'navigate');
    
    const result = await TestBed.runInInjectionContext(() => 
      authGuard({} as any, {} as any)
    );
    
    expect(result).toBe(false);
    expect(navigateSpy).toHaveBeenCalledWith([ROUTER_PATHS.SIGN_IN]);
  });
  
  it('should check roles when specified in route data', async () => {
    mockAuthService.isAuthenticated.mockResolvedValue(true);
    mockAuthService.getUserGroups.mockResolvedValue(['admin']);
    
    const result = await TestBed.runInInjectionContext(() => 
      authGuard({ data: { roles: ['admin'] } } as any, {} as any)
    );
    
    expect(result).toBe(true);
  });
});
