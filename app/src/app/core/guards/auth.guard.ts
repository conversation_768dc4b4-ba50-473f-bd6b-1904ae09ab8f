import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { ROUTER_PATHS } from 'src/app/shared/constant.routing';
import { UserService } from 'src/app/services/user.service';
import { USER_GROUPS } from 'src/app/shared/constant.common';
import { firstValueFrom } from 'rxjs';

export const authGuard: CanActivateFn = async (route, state) => {
  const authService = inject(AuthService);
  const userService = inject(UserService);
  const router = inject(Router);
  
  try {
    const isLoginPage = (state.url === '/' + ROUTER_PATHS.SIGN_IN)
    const isAuthenticated = await authService.isAuthenticated();
    if (!isAuthenticated && !isLoginPage) {
      router.navigate([ROUTER_PATHS.SIGN_IN]);
      return false;
    } else if (isLoginPage &&  isAuthenticated) {
        router.navigate([ROUTER_PATHS.LANDING_PAGE]);
    }
    
    // Check for required roles if specified in route data
    if (route.data?.['roles']) {
      const userGroups = await authService.getUserGroups();
      
      // Check if user is admin - admins have access to everything
      if (userGroups.includes(USER_GROUPS.ADMIN)) {
        return true;
      }
      
      // If not admin, check if user is in any manager, operator, or worker group
      try {
        const currentUser = await authService.getUser();
        const username = currentUser.username;
        const userDetails = await firstValueFrom(userService.getUser(username));
        
        // Check if user has any manager groups
        if (userDetails.managerGroups && userDetails.managerGroups.length > 0) {
          return true;
        }
        
        // Check if user has any operator groups
        if (userDetails.operatorGroups && userDetails.operatorGroups.length > 0) {
          return true;
        }
        
        // Check if user has any worker groups
        if (userDetails.workerGroups && userDetails.workerGroups.length > 0) {
          return true;
        }
      } catch (err) {
        console.error('Error fetching user details:', err);
      }
      
      // If we reach here, the user doesn't have the required roles
      router.navigate([ROUTER_PATHS.DASH_BOARD]);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Auth guard error:', error);
    router.navigate([ROUTER_PATHS.SIGN_IN]);
    return false;
  }
};
