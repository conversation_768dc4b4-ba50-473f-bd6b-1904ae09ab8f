import { APOLLO_OPTIONS, ApolloModule } from 'apollo-angular';
import { HttpLink } from 'apollo-angular/http';
import { NgModule } from '@angular/core';
import { ApolloClientOptions, ApolloLink, InMemoryCache } from '@apollo/client/core';
import { setContext } from '@apollo/client/link/context';
import { fetchAuthSession } from 'aws-amplify/auth';
import { environment } from '../../environments/environment';
import { AuthService } from './services/auth.service';

const uri = environment.appsyncUri

export function createApollo(httpLink: HttpLink): ApolloClientOptions<any> {

  const auth = setContext(() => {
    return fetchAuthSession().then(data => {
      const token = data.tokens?.idToken
      return {  
        headers: {
          Authorization: `${token}`,
        },
      }
    })
  });

  return {
    link: ApolloLink.from([auth, httpLink.create({ uri })]),
    cache: new InMemoryCache({
      // Disable normalization and caching by returning null for dataIdFromObject
      dataIdFromObject: () => null,
      // Disable result caching
      resultCaching: false,
    }),
    defaultOptions: {
      watchQuery: {
        fetchPolicy: 'no-cache',
      },
      query: {
        fetchPolicy: 'no-cache',
      },
      mutate: {
        fetchPolicy: 'no-cache',
      },
    },
    headers: {
    },
  };
}

@NgModule({
  exports: [ApolloModule],
  providers: [
    {
      provide: APOLLO_OPTIONS,
      useFactory: createApollo,
      deps: [HttpLink, AuthService],
    },
  ],
})
export class GraphQLModule {
}

