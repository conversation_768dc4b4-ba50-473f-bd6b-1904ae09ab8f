import { NgModule, Optional, SkipSelf } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ProfileComponent } from './components/profile/profile.component';
import { MatCardModule } from '@angular/material/card';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { HttpClient, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { GraphQLModule } from './graphql.module';
import { MatIconModule } from '@angular/material/icon';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatListModule } from '@angular/material/list';
import { SignInComponent } from './components/sign-in/sign-in.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CoreRoutingModule } from './core-routing.module';
import { MatFormFieldModule } from '@angular/material/form-field';
import { NavigationComponent } from './components/template/navigation/navigation.component';
import { FooterComponent } from './components/template/footer/footer.component';
import { CoreLayoutComponent } from './components/template/core-layout/core-layout.component';
import { RouterModule } from '@angular/router';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatButtonModule } from '@angular/material/button';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { ConfirmationDialogComponent } from './components/confirmation-dialog/confirmation-dialog.component';
import { MatDialogModule } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { GlobalLoadingComponent } from './components/global-loading/global-loading.component';

export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

@NgModule({ declarations: [ProfileComponent, SignInComponent, NavigationComponent, FooterComponent, CoreLayoutComponent, ConfirmationDialogComponent, GlobalLoadingComponent],
    exports: [ProfileComponent, NavigationComponent, FooterComponent, CoreLayoutComponent, GlobalLoadingComponent] // Export if used outside CoreModule
    , imports: [CommonModule,
        CoreRoutingModule,
        MatCardModule,
        MatOptionModule,
        MatSelectModule,
        MatIconModule,
        MatInputModule,
        MatDialogModule,
        MatToolbarModule,
        MatFormFieldModule,
        MatListModule,
        RouterModule,
        ReactiveFormsModule,
        MatSidenavModule,
        FormsModule,
        MatButtonModule,
        MatSnackBarModule,
        MatProgressSpinnerModule,
        GraphQLModule,
        TranslateModule.forRoot({
            loader: {
                provide: TranslateLoader,
                useFactory: HttpLoaderFactory,
                deps: [HttpClient],
            },
        })], providers: [provideHttpClient(withInterceptorsFromDi())] })
export class CoreModule {
  constructor(@Optional() @SkipSelf() parentModule: CoreModule, translate: TranslateService) {
    if (parentModule) {
      throw new Error('CoreModule is already loaded. Import it only in AppModule.');
    }


    translate.setDefaultLang('en');

  }
}
