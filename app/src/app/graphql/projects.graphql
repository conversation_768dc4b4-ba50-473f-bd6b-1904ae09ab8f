query GetProject($id: ID!) {
  getProject(id: $id) {
    id
    projectId
    name
    displayName
    description
    status
    startDate
    finishDate
    allowStatusReview
    activityCount
    obsName
    obsObjectId
    parentEPSObjectId
    parentEPSId
    parentEPSName
    wbsObjectId
    isSyncing
    isVisible
    createdAt
    updatedAt
    managerGroups {
      id
      name
    }
    operatorGroups {
      id
      name
    }
    workerGroups {
      id
      name
    }
  }
}

query ListProjectsShort($limit: Int, $nextToken: String) {
  listProjects(limit: $limit, nextToken: $nextToken) {
    items {
      id
      projectId
      name
      managerGroups {
        id
        name
      }
      operatorGroups {
        id
        name
      }
      workerGroups {
        id
        name
      }
    }
  }
}

query ListProjects($limit: Int, $nextToken: String) {
  listProjects(limit: $limit, nextToken: $nextToken) {
    items {
      id
      projectId
      name
      description
      status
      startDate
      finishDate
      allowStatusReview
      activityCount
      obsName
      obsObjectId
      parentEPSObjectId
      parentEPSId
      parentEPSName
      wbsObjectId
      isSyncing
      isVisible
      createdAt
      updatedAt
      managerGroups {
        id
        name
      }
      operatorGroups {
        id
        name
      }
      workerGroups {
        id
        name
      }
    }
    nextToken
    totalCount
  }
}

query ListProjectsWithActivities($limit: Int, $nextToken: String) {
  listProjects(limit: $limit, nextToken: $nextToken) {
    items {
      id
      name
      description
      activities {
        items {
          name
          status
          unableToWork
          activityId
          id
          percentComplete
          startDate
          finishDate
          plannedFinishDate
          plannedStartDate
        }
      }
    }
    nextToken
    totalCount
  }
}

query GetProjectWithActivities(
  $id: ID!, 
  $limit: Int, 
  $nextToken: String,
  $status: Status,
  $unableToWork: Boolean,
  $plannedStartDateFrom: AWSDateTime,
  $plannedStartDateTo: AWSDateTime,
  $plannedFinishDateFrom: AWSDateTime,
  $plannedFinishDateTo: AWSDateTime
) {
  getProject(id: $id) {
    id
    name
    description
    activities(
      limit: $limit, 
      nextToken: $nextToken,
      status: $status,
      unableToWork: $unableToWork,
      plannedStartDateFrom: $plannedStartDateFrom,
      plannedStartDateTo: $plannedStartDateTo,
      plannedFinishDateFrom: $plannedFinishDateFrom,
      plannedFinishDateTo: $plannedFinishDateTo
    ) {
      items {
        activityId
        id
        percentComplete
        lastPercentComplete
        name
        description
        floc
        workorderNo
        jobOrderNumber
        discipline
        scopeNr
        scopeId
        status
        reviewStatus
        unableToWork
        plannedStartDate
        plannedFinishDate
      }
      nextToken
      totalCount
    }
  }
}


mutation UpdateProject($id: ID!, $input: ProjectInput!) {
  updateProject(id: $id, input: $input) {
    id
    isSyncing
    isVisible
    managerGroups {
      id
      name
    }
    operatorGroups {
      id
      name
    }
    workerGroups {
      id
      name
    }
  }
}
