query GetUser($username: String!) {
  getUser(username: $username) {
    username
    email
    managerGroups {
      id
      name
      groupType
    }
    operatorGroups {
      id
      name
      groupType
    }
    workerGroups {
      id
      name
      groupType
    }
    disciplines
    equipments
    contractors {
      id
      name
      contractorNumber
      description
    }
    createdAt
    updatedAt
  }
}

query ListUsers($limit: Int, $nextToken: String) {
  listUsers(limit: $limit, nextToken: $nextToken) {
    items {
      username
      email
      managerGroups {
        id
        name
      }
      operatorGroups {
        id
        name
      }
      workerGroups {
        id
        name
      }
      disciplines
      equipments
      createdAt
      updatedAt
    }
    nextToken
    totalCount
  }
}

mutation CreateUser($input: UserInput!) {
  createUser(input: $input) {
    username
    email
    createdAt
    updatedAt
  }
}

mutation DeleteUser($username: String!) {
  deleteUser(username: $username)
}

mutation AddUserToGroup($username: String!, $groupId: ID!) {
  addUserToGroup(username: $username, groupId: $groupId) {
    username
    managerGroups {
      id
      name
      groupType
    }
    operatorGroups {
      id
      name
      groupType
    }
    workerGroups {
      id
      name
      groupType
    }
  }
}

mutation RemoveUserFromGroup($username: String!, $groupId: ID!) {
  removeUserFromGroup(username: $username, groupId: $groupId) {
    username
    managerGroups {
      id
      name
      groupType
    }
    operatorGroups {
      id
      name
      groupType
    }
    workerGroups {
      id
      name
      groupType
    }
  }
}

mutation UpdateUser($username: String!, $input: UserInput!) {
  updateUser(username: $username, input: $input) {
    username
    email
    disciplines
    equipments
    updatedAt
  }
}
