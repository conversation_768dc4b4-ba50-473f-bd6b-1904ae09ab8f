mutation UpdateActivity($id: ID!, $input: ActivityInput!) {
  updateActivity(id: $id, input: $input) {
    id
    activityId
    name
    description
    
    # Status fields
    status
    statusCode
    reviewStatus
    unableToWork
    
    # Progress fields
    percentComplete
    progress
    lastPercentComplete
    
    # Date fields
    startDate
    finishDate
    actualStartDate
    actualFinishDate
    baselineStartDate
    baselineFinishDate
    plannedFinishDate
    plannedStartDate
    
    # Project related fields
    projectId
    projectName
    
    # Assignment fields
    assignedTo
    executorEmail
    
    # Effort and duration fields
    plannedDuration
    actualDuration
    plannedEffort
    actualEffort
    
    # Activity code fields
    sequenceNo
    disciplineCode
    companyCode
    
    # Activity codes
    activityCodes {
      id
      codeTypeName
      codeTypeDescription
      codeTypeValue
      createdAt
      updatedAt
    }
    
    # Contractor information
    contractor {
      id
      name
      contractorNumber
      description
    }
    
    # Related objects
    resources {
      resourceCode
      resourceName
      discipline
    }
    comments {
      author
      text
      createdAt
    }
    attachments {
      name
      contentType
      size  
      key
      uploadDate
      uploadedBy
    }
    evidences {
      name
      description
      contentType
      size  
      key
      uploadDate
      uploadedBy
    }
    logEvents {
      timestamp
      event
      changedBy
    }
    allowedGroups
  }
}

mutation UpdateMultipleActivities($input: [UpdateActivityInput!]!) {
  updateActivities(input: $input) {
    id
    activityId
    name
    status
    statusCode
    reviewStatus
    progress
    percentComplete
    lastPercentComplete
    unableToWork
    assignedTo
    plannedEffort
    actualEffort
    companyCode
    disciplineCode
    contractor {
      id
      name
      contractorNumber
    }
  }
}

query GetActivity($id: ID!) {
  getActivity(id: $id) {
    id
    activityId
    name
    description
    
    # Status fields
    status
    statusCode
    reviewStatus
    unableToWork
    
    # Progress fields
    percentComplete
    progress
    lastPercentComplete
    
    # Date fields
    startDate
    finishDate
    actualStartDate
    actualFinishDate
    baselineStartDate
    baselineFinishDate
    plannedFinishDate
    plannedStartDate
    
    # Project related fields
    projectId
    projectName
    projectObjectId
    
    # Assignment fields
    assignedTo
    executorEmail
    
    # Effort and duration fields
    plannedDuration
    actualDuration
    plannedEffort
    actualEffort
    
    # Scope and equipment fields
    scopeNr
    scopeId
    scopeDescription
    equipment
    equipmentDescription
    floc
    
    # Job fields
    jobOrderNumber
    notificationNumber
    discipline
    
    # Activity code fields
    sequenceNo
    disciplineCode
    companyCode
    
    # Activity codes
    activityCodes {
      id
      codeTypeName
      codeTypeDescription
      codeTypeValue
      createdAt
      updatedAt
    }
    
    # Relationship fields
    predecessorActivities {
      id
      name
      status
      activityId
      percentComplete
      plannedStartDate
      plannedFinishDate
    }
    successorActivities {
      id
      name
      status
      activityId
      percentComplete
      plannedStartDate
      plannedFinishDate
    }
    
    # Contractor information
    contractor {
      id
      name
      contractorNumber
      description
    }
    resourceId
    resourceName
    resourceObjectId
    comments {
      author
      text
      createdAt
    }
    attachments {
      name
      contentType
      size  
      key
      uploadDate
      uploadedBy
    }
    evidences {
      name
      contentType
      description
      size  
      key
      uploadDate
      uploadedBy
    }
    logEvents {
      timestamp
      event
      changedBy
    }
    allowedGroups
  }
}

query ListActivities {
  listActivities {
    id
    activityId
    name
    description
    status
    statusCode
    reviewStatus
    progress
    percentComplete
    unableToWork
    assignedTo
    executorEmail
    plannedEffort
    actualEffort
    startDate
    finishDate
    plannedStartDate
    plannedFinishDate
    sequenceNo
    companyCode
    disciplineCode
    contractor {
      id
      name
      contractorNumber
    }
  }
}
