query GetContractor($id: ID!) {
  getContractor(id: $id) {
    id
    contractorNumber
    name
    description
    users {
      username
      email
    }
    createdAt
    updatedAt
  }
}

query ListContractors($limit: Int, $nextToken: String) {
  listContractors(limit: $limit, nextToken: $nextToken) {
    items {
      id
      contractorNumber
      name
      description
      users {
        username
        email
      }
      createdAt
      updatedAt
    }
    nextToken
    totalCount
  }
}

mutation CreateContractor($input: ContractorInput!) {
  createContractor(input: $input) {
    id
    contractorNumber
    name
    description
    createdAt
    updatedAt
  }
}

mutation UpdateContractor($id: ID!, $input: ContractorInput!) {
  updateContractor(id: $id, input: $input) {
    id
    contractorNumber
    name
    description
    createdAt
    updatedAt
  }
}

mutation DeleteContractor($id: ID!) {
  deleteContractor(id: $id)
}

mutation AddUserToContractor($username: String!, $contractorId: ID!) {
  addUserToContractor(username: $username, contractorId: $contractorId) {
    username
    contractors {
      id
      name
      contractorN<PERSON>ber
    }
  }
}

mutation RemoveUserFromContractor($username: String!, $contractorId: ID!) {
  removeUserFromContractor(username: $username, contractorId: $contractorId) {
    username
    contractors {
      id
      name
      contractorNumber
    }
  }
}
