import { gql } from 'apollo-angular';
import { Injectable } from '@angular/core';
import * as Apollo from 'apollo-angular';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  AWSDate: { input: any; output: any; }
  AWSDateTime: { input: any; output: any; }
  AWSEmail: { input: any; output: any; }
  AWSIPAddress: { input: any; output: any; }
  AWSJSON: { input: any; output: any; }
  AWSPhone: { input: any; output: any; }
  AWSTime: { input: any; output: any; }
  AWSTimestamp: { input: any; output: any; }
  AWSURL: { input: any; output: any; }
  BigInt: { input: any; output: any; }
  Double: { input: any; output: any; }
};

export type Activity = {
  __typename?: 'Activity';
  activityCodes?: Maybe<Array<ActivityCode>>;
  activityId?: Maybe<Scalars['ID']['output']>;
  actualDuration?: Maybe<Scalars['Float']['output']>;
  actualEffort?: Maybe<Scalars['Int']['output']>;
  actualFinishDate?: Maybe<Scalars['AWSDateTime']['output']>;
  actualStartDate?: Maybe<Scalars['AWSDateTime']['output']>;
  allowedGroups?: Maybe<Array<Scalars['String']['output']>>;
  assignedTo?: Maybe<Scalars['String']['output']>;
  attachments?: Maybe<Array<Attachment>>;
  baselineFinishDate?: Maybe<Scalars['AWSDateTime']['output']>;
  baselineStartDate?: Maybe<Scalars['AWSDateTime']['output']>;
  comments?: Maybe<Array<Comment>>;
  companyCode?: Maybe<Scalars['String']['output']>;
  contractor?: Maybe<Contractor>;
  description?: Maybe<Scalars['String']['output']>;
  discipline?: Maybe<Scalars['String']['output']>;
  disciplineCode?: Maybe<Scalars['String']['output']>;
  equipment?: Maybe<Scalars['String']['output']>;
  equipmentDescription?: Maybe<Scalars['String']['output']>;
  evidences?: Maybe<Array<Attachment>>;
  executorEmail?: Maybe<Scalars['String']['output']>;
  finishDate?: Maybe<Scalars['AWSDateTime']['output']>;
  floc?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  jobOrderNumber?: Maybe<Scalars['String']['output']>;
  lastPercentComplete?: Maybe<Scalars['Float']['output']>;
  logEvents?: Maybe<Array<Maybe<LogEvent>>>;
  name: Scalars['String']['output'];
  notificationNumber?: Maybe<Scalars['String']['output']>;
  percentComplete?: Maybe<Scalars['Float']['output']>;
  phase?: Maybe<Scalars['String']['output']>;
  plannedDuration?: Maybe<Scalars['Float']['output']>;
  plannedEffort?: Maybe<Scalars['Int']['output']>;
  plannedFinishDate?: Maybe<Scalars['AWSDateTime']['output']>;
  plannedStartDate?: Maybe<Scalars['AWSDateTime']['output']>;
  predecessorActivities?: Maybe<Array<Maybe<Activity>>>;
  progress?: Maybe<Scalars['Int']['output']>;
  projectId?: Maybe<Scalars['String']['output']>;
  projectName?: Maybe<Scalars['String']['output']>;
  projectObjectId?: Maybe<Scalars['Int']['output']>;
  resourceId?: Maybe<Scalars['String']['output']>;
  resourceName?: Maybe<Scalars['String']['output']>;
  resourceObjectId?: Maybe<Scalars['Int']['output']>;
  resources?: Maybe<Array<Resource>>;
  reviewStatus?: Maybe<ReviewStatus>;
  revisedPlannedTimeline?: Maybe<Timeline>;
  scopeDescription?: Maybe<Scalars['String']['output']>;
  scopeId?: Maybe<Scalars['String']['output']>;
  scopeNr?: Maybe<Scalars['String']['output']>;
  sequenceNo?: Maybe<Scalars['Int']['output']>;
  startDate?: Maybe<Scalars['AWSDateTime']['output']>;
  status?: Maybe<Status>;
  statusCode?: Maybe<StatusCode>;
  successorActivities?: Maybe<Array<Maybe<Activity>>>;
  syncStatus?: Maybe<ActivitySync>;
  unableToWork?: Maybe<Scalars['Boolean']['output']>;
  workorderNo?: Maybe<Scalars['String']['output']>;
};

export type ActivityCode = {
  __typename?: 'ActivityCode';
  codeTypeDescription?: Maybe<Scalars['String']['output']>;
  codeTypeName?: Maybe<Scalars['String']['output']>;
  codeTypeValue?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['AWSDateTime']['output']>;
  id: Scalars['ID']['output'];
  updatedAt?: Maybe<Scalars['AWSDateTime']['output']>;
};

export type ActivityCodeInput = {
  codeTypeDescription?: InputMaybe<Scalars['String']['input']>;
  codeTypeName?: InputMaybe<Scalars['String']['input']>;
  codeTypeValue?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
};

export type ActivityConnection = {
  __typename?: 'ActivityConnection';
  items: Array<Activity>;
  nextToken?: Maybe<Scalars['String']['output']>;
  totalCount: Scalars['Int']['output'];
};

export type ActivityInput = {
  activityCodes?: InputMaybe<Array<ActivityCodeInput>>;
  activityId?: InputMaybe<Scalars['ID']['input']>;
  actualTimeline?: InputMaybe<TimelineInput>;
  allowedGroups?: InputMaybe<Array<Scalars['String']['input']>>;
  assignedTo?: InputMaybe<Scalars['String']['input']>;
  attachments?: InputMaybe<Array<AttachmentInput>>;
  comments?: InputMaybe<Array<CommentInput>>;
  companyCode?: InputMaybe<Scalars['String']['input']>;
  contractorId?: InputMaybe<Scalars['ID']['input']>;
  dependencies?: InputMaybe<Array<ActivityInput>>;
  description?: InputMaybe<Scalars['String']['input']>;
  disciplineCode?: InputMaybe<Scalars['String']['input']>;
  evidences?: InputMaybe<Array<AttachmentInput>>;
  executorEmail?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  percentComplete?: InputMaybe<Scalars['Float']['input']>;
  plannedEffort?: InputMaybe<Scalars['Int']['input']>;
  predecessorActivityId?: InputMaybe<Scalars['String']['input']>;
  predecessorActivityStatus?: InputMaybe<Status>;
  progress?: InputMaybe<Scalars['Int']['input']>;
  resources?: InputMaybe<Array<ResourceInput>>;
  revisedPlannedTimeline?: InputMaybe<TimelineInput>;
  sequenceNo?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  successorActivityId?: InputMaybe<Scalars['String']['input']>;
  successorActivityStatus?: InputMaybe<Status>;
  unableToWork?: InputMaybe<Scalars['Boolean']['input']>;
};

export type ActivitySync = {
  __typename?: 'ActivitySync';
  lastSyncedAt?: Maybe<Scalars['AWSDateTime']['output']>;
  syncStatus?: Maybe<Scalars['String']['output']>;
  targetSystem?: Maybe<Scalars['String']['output']>;
};

export type Attachment = {
  __typename?: 'Attachment';
  contentType?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  key?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  size?: Maybe<Scalars['Int']['output']>;
  uploadDate?: Maybe<Scalars['AWSDateTime']['output']>;
  uploadedBy?: Maybe<Scalars['String']['output']>;
};

export type AttachmentInput = {
  contentType?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  key?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  size?: InputMaybe<Scalars['Int']['input']>;
  uploadDate?: InputMaybe<Scalars['AWSDateTime']['input']>;
  uploadedBy?: InputMaybe<Scalars['String']['input']>;
};

export type Comment = {
  __typename?: 'Comment';
  author?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['AWSDateTime']['output']>;
  text?: Maybe<Scalars['String']['output']>;
};

export type CommentInput = {
  author?: InputMaybe<Scalars['String']['input']>;
  createdAt?: InputMaybe<Scalars['AWSDateTime']['input']>;
  text?: InputMaybe<Scalars['String']['input']>;
};

export type Contractor = {
  __typename?: 'Contractor';
  activities?: Maybe<Array<Maybe<Activity>>>;
  contractorNumber: Scalars['String']['output'];
  createdAt: Scalars['AWSDateTime']['output'];
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  updatedAt: Scalars['AWSDateTime']['output'];
  users?: Maybe<Array<Maybe<User>>>;
};

export type ContractorConnection = {
  __typename?: 'ContractorConnection';
  items: Array<Contractor>;
  nextToken?: Maybe<Scalars['String']['output']>;
  totalCount?: Maybe<Scalars['Int']['output']>;
};

export type ContractorInput = {
  contractorNumber: Scalars['String']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
};

export type DailyActivityCount = {
  __typename?: 'DailyActivityCount';
  count: Scalars['Int']['output'];
  date: Scalars['String']['output'];
};

export type Group = {
  __typename?: 'Group';
  createdAt?: Maybe<Scalars['AWSDateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  groupType: GroupType;
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  updatedAt?: Maybe<Scalars['AWSDateTime']['output']>;
  users?: Maybe<Array<User>>;
};

export type GroupConnection = {
  __typename?: 'GroupConnection';
  items: Array<Group>;
  nextToken?: Maybe<Scalars['String']['output']>;
  totalCount?: Maybe<Scalars['Int']['output']>;
};

export type GroupInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  groupType: GroupType;
  name: Scalars['String']['input'];
};

export enum GroupType {
  Manager = 'MANAGER',
  Operator = 'OPERATOR',
  Worker = 'WORKER'
}

export type LogEvent = {
  __typename?: 'LogEvent';
  changedBy?: Maybe<Scalars['String']['output']>;
  event?: Maybe<Scalars['String']['output']>;
  timestamp?: Maybe<Scalars['AWSDateTime']['output']>;
};

export type LogEventInput = {
  changedBy?: InputMaybe<Scalars['String']['input']>;
  event?: InputMaybe<Scalars['String']['input']>;
  timestamp?: InputMaybe<Scalars['AWSDateTime']['input']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  addActivity: Activity;
  addProject: Project;
  addUserToContractor: User;
  addUserToGroup: User;
  createContractor: Contractor;
  createGroup: Group;
  createUser: User;
  deleteActivity: Scalars['String']['output'];
  deleteContractor: Scalars['String']['output'];
  deleteGroup: Scalars['String']['output'];
  deleteProject: Scalars['String']['output'];
  deleteUser: Scalars['String']['output'];
  getDeleteUrl?: Maybe<S3Payload>;
  getDownloadUrl?: Maybe<S3Payload>;
  getUploadUrl?: Maybe<S3Payload>;
  removeUserFromContractor: User;
  removeUserFromGroup: User;
  updateActivities: Array<Activity>;
  updateActivity: Activity;
  updateContractor: Contractor;
  updateGroup: Group;
  updateProject: Project;
  updateUser: User;
};


export type MutationAddActivityArgs = {
  input: ActivityInput;
};


export type MutationAddProjectArgs = {
  input: ProjectInput;
};


export type MutationAddUserToContractorArgs = {
  contractorId: Scalars['ID']['input'];
  username: Scalars['String']['input'];
};


export type MutationAddUserToGroupArgs = {
  groupId: Scalars['ID']['input'];
  username: Scalars['String']['input'];
};


export type MutationCreateContractorArgs = {
  input: ContractorInput;
};


export type MutationCreateGroupArgs = {
  input: GroupInput;
};


export type MutationCreateUserArgs = {
  input: UserInput;
};


export type MutationDeleteActivityArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteContractorArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteGroupArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteProjectArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteUserArgs = {
  username: Scalars['String']['input'];
};


export type MutationGetDeleteUrlArgs = {
  activityId: Scalars['ID']['input'];
  key: Scalars['String']['input'];
};


export type MutationGetDownloadUrlArgs = {
  activityId: Scalars['ID']['input'];
  key: Scalars['String']['input'];
};


export type MutationGetUploadUrlArgs = {
  activityId: Scalars['ID']['input'];
  fileName: Scalars['String']['input'];
};


export type MutationRemoveUserFromContractorArgs = {
  contractorId: Scalars['ID']['input'];
  username: Scalars['String']['input'];
};


export type MutationRemoveUserFromGroupArgs = {
  groupId: Scalars['ID']['input'];
  username: Scalars['String']['input'];
};


export type MutationUpdateActivitiesArgs = {
  input: Array<UpdateActivityInput>;
};


export type MutationUpdateActivityArgs = {
  id: Scalars['ID']['input'];
  input: ActivityInput;
};


export type MutationUpdateContractorArgs = {
  id: Scalars['ID']['input'];
  input: ContractorInput;
};


export type MutationUpdateGroupArgs = {
  id: Scalars['ID']['input'];
  input: GroupInput;
};


export type MutationUpdateProjectArgs = {
  id: Scalars['ID']['input'];
  input: ProjectInput;
};


export type MutationUpdateUserArgs = {
  input: UserInput;
  username: Scalars['String']['input'];
};

export type Project = {
  __typename?: 'Project';
  activities: ActivityConnection;
  activityCount?: Maybe<Scalars['Int']['output']>;
  allowStatusReview?: Maybe<Scalars['Boolean']['output']>;
  autoClose?: Maybe<Scalars['Boolean']['output']>;
  createdAt?: Maybe<Scalars['AWSDateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  displayName: Scalars['String']['output'];
  finishDate?: Maybe<Scalars['AWSDateTime']['output']>;
  id: Scalars['ID']['output'];
  isSyncing?: Maybe<Scalars['Boolean']['output']>;
  isVisible?: Maybe<Scalars['Boolean']['output']>;
  managerGroups?: Maybe<Array<Group>>;
  name: Scalars['String']['output'];
  obsName?: Maybe<Scalars['String']['output']>;
  obsObjectId?: Maybe<Scalars['Int']['output']>;
  operatorGroups?: Maybe<Array<Group>>;
  parentEPSId?: Maybe<Scalars['String']['output']>;
  parentEPSName?: Maybe<Scalars['String']['output']>;
  parentEPSObjectId?: Maybe<Scalars['Int']['output']>;
  projectId: Scalars['ID']['output'];
  startDate?: Maybe<Scalars['AWSDateTime']['output']>;
  status?: Maybe<ProjectStatus>;
  updatedAt?: Maybe<Scalars['AWSDateTime']['output']>;
  wbsObjectId?: Maybe<Scalars['Int']['output']>;
  workerGroups?: Maybe<Array<Group>>;
};


export type ProjectActivitiesArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  nextToken?: InputMaybe<Scalars['String']['input']>;
  plannedFinishDateFrom?: InputMaybe<Scalars['AWSDateTime']['input']>;
  plannedFinishDateTo?: InputMaybe<Scalars['AWSDateTime']['input']>;
  plannedStartDateFrom?: InputMaybe<Scalars['AWSDateTime']['input']>;
  plannedStartDateTo?: InputMaybe<Scalars['AWSDateTime']['input']>;
  status?: InputMaybe<Status>;
  unableToWork?: InputMaybe<Scalars['Boolean']['input']>;
};

export type ProjectConnection = {
  __typename?: 'ProjectConnection';
  items: Array<Project>;
  nextToken?: Maybe<Scalars['String']['output']>;
  totalCount?: Maybe<Scalars['Int']['output']>;
};

export type ProjectInput = {
  isSyncing?: InputMaybe<Scalars['Boolean']['input']>;
  isVisible?: InputMaybe<Scalars['Boolean']['input']>;
  managerGroups?: InputMaybe<Array<Scalars['ID']['input']>>;
  operatorGroups?: InputMaybe<Array<Scalars['ID']['input']>>;
  workerGroups?: InputMaybe<Array<Scalars['ID']['input']>>;
};

export enum ProjectStatus {
  Active = 'ACTIVE',
  Inactive = 'INACTIVE',
  Requested = 'REQUESTED',
  Template = 'TEMPLATE',
  Whatif = 'WHATIF'
}

export type Query = {
  __typename?: 'Query';
  getActivity?: Maybe<Activity>;
  getActivityCountGroupedByStatusAndDiscipline: Array<StatusDisciplineCount>;
  getCompletedActivitiesByDay: Array<DailyActivityCount>;
  getContractor?: Maybe<Contractor>;
  getCountByStatus: Array<StatusCount>;
  getCountOfUnableToWorkActivities: Scalars['Int']['output'];
  getGroup?: Maybe<Group>;
  getPlannedActivitiesByDay: Array<DailyActivityCount>;
  getProject?: Maybe<Project>;
  getUser?: Maybe<User>;
  listActivities: Array<Activity>;
  listContractors: ContractorConnection;
  listGroups: GroupConnection;
  listProjects: ProjectConnection;
  listSyncJobs: SyncJobConnection;
  listUsers: UserConnection;
};


export type QueryGetActivityArgs = {
  id: Scalars['ID']['input'];
};


export type QueryGetActivityCountGroupedByStatusAndDisciplineArgs = {
  projectId: Scalars['ID']['input'];
};


export type QueryGetCompletedActivitiesByDayArgs = {
  projectId: Scalars['ID']['input'];
};


export type QueryGetContractorArgs = {
  id: Scalars['ID']['input'];
};


export type QueryGetCountByStatusArgs = {
  projectId: Scalars['ID']['input'];
};


export type QueryGetCountOfUnableToWorkActivitiesArgs = {
  projectId: Scalars['ID']['input'];
};


export type QueryGetGroupArgs = {
  id: Scalars['ID']['input'];
};


export type QueryGetPlannedActivitiesByDayArgs = {
  projectId: Scalars['ID']['input'];
};


export type QueryGetProjectArgs = {
  id: Scalars['ID']['input'];
};


export type QueryGetUserArgs = {
  username: Scalars['String']['input'];
};


export type QueryListContractorsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  nextToken?: InputMaybe<Scalars['String']['input']>;
};


export type QueryListGroupsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  nextToken?: InputMaybe<Scalars['String']['input']>;
};


export type QueryListProjectsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  nextToken?: InputMaybe<Scalars['String']['input']>;
};


export type QueryListSyncJobsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  nextToken?: InputMaybe<Scalars['String']['input']>;
  projectId?: InputMaybe<Scalars['ID']['input']>;
  status?: InputMaybe<SyncJobStatus>;
  syncType?: InputMaybe<Scalars['String']['input']>;
};


export type QueryListUsersArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  nextToken?: InputMaybe<Scalars['String']['input']>;
};

export type Resource = {
  __typename?: 'Resource';
  discipline?: Maybe<Scalars['String']['output']>;
  resourceCode?: Maybe<Scalars['String']['output']>;
  resourceName?: Maybe<Scalars['String']['output']>;
};

export type ResourceInput = {
  discipline?: InputMaybe<Scalars['String']['input']>;
  resourceCode?: InputMaybe<Scalars['String']['input']>;
  resourceName?: InputMaybe<Scalars['String']['input']>;
};

export enum ReviewStatus {
  ForReview = 'FOR_REVIEW',
  Ok = 'OK',
  Rejected = 'REJECTED'
}

export type Role = {
  __typename?: 'Role';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
};

export type S3Payload = {
  __typename?: 'S3Payload';
  key: Scalars['String']['output'];
  url: Scalars['String']['output'];
};

export enum Status {
  Completed = 'COMPLETED',
  InProgress = 'IN_PROGRESS',
  NotStarted = 'NOT_STARTED'
}

export enum StatusCode {
  Active = 'ACTIVE',
  Inactive = 'INACTIVE',
  Planned = 'PLANNED',
  Requested = 'REQUESTED',
  Template = 'TEMPLATE',
  Whatif = 'WHATIF'
}

export type StatusCount = {
  __typename?: 'StatusCount';
  count: Scalars['Int']['output'];
  status: Status;
};

export type StatusDisciplineCount = {
  __typename?: 'StatusDisciplineCount';
  count: Scalars['Int']['output'];
  discipline: Scalars['String']['output'];
  status: Status;
};

export type SyncJob = {
  __typename?: 'SyncJob';
  activitiesProcessed?: Maybe<Scalars['Int']['output']>;
  activityCodesProcessed?: Maybe<Scalars['Int']['output']>;
  executionTime?: Maybe<Scalars['Float']['output']>;
  fetchTime?: Maybe<Scalars['Float']['output']>;
  id: Scalars['ID']['output'];
  projectId?: Maybe<Scalars['ID']['output']>;
  projectsProcessed?: Maybe<Scalars['Int']['output']>;
  relationshipsProcessed?: Maybe<Scalars['Int']['output']>;
  status: SyncJobStatus;
  syncType: Scalars['String']['output'];
  timestamp: Scalars['AWSDateTime']['output'];
  writeTime?: Maybe<Scalars['Float']['output']>;
};

export type SyncJobConnection = {
  __typename?: 'SyncJobConnection';
  items: Array<SyncJob>;
  nextToken?: Maybe<Scalars['String']['output']>;
  totalCount?: Maybe<Scalars['Int']['output']>;
};

export enum SyncJobStatus {
  Failed = 'FAILED',
  InProgress = 'IN_PROGRESS',
  Succeeded = 'SUCCEEDED'
}

export type Timeline = {
  __typename?: 'Timeline';
  end?: Maybe<Scalars['AWSDateTime']['output']>;
  start?: Maybe<Scalars['AWSDateTime']['output']>;
};

export type TimelineInput = {
  end?: InputMaybe<Scalars['AWSDateTime']['input']>;
  start?: InputMaybe<Scalars['AWSDateTime']['input']>;
};

export type UpdateActivityInput = {
  contractorId?: InputMaybe<Scalars['ID']['input']>;
  id: Scalars['ID']['input'];
  percentComplete?: InputMaybe<Scalars['Float']['input']>;
  status?: InputMaybe<Status>;
  unableToWork?: InputMaybe<Scalars['Boolean']['input']>;
};

export type User = {
  __typename?: 'User';
  contractors?: Maybe<Array<Maybe<Contractor>>>;
  createdAt?: Maybe<Scalars['AWSDateTime']['output']>;
  disciplines?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  email?: Maybe<Scalars['String']['output']>;
  equipments?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  managerGroups?: Maybe<Array<Group>>;
  operatorGroups?: Maybe<Array<Group>>;
  updatedAt?: Maybe<Scalars['AWSDateTime']['output']>;
  username: Scalars['String']['output'];
  workerGroups?: Maybe<Array<Group>>;
};

export type UserConnection = {
  __typename?: 'UserConnection';
  items: Array<User>;
  nextToken?: Maybe<Scalars['String']['output']>;
  totalCount?: Maybe<Scalars['Int']['output']>;
};

export type UserInput = {
  disciplines?: InputMaybe<Array<Scalars['String']['input']>>;
  email?: InputMaybe<Scalars['String']['input']>;
  equipments?: InputMaybe<Array<Scalars['String']['input']>>;
  username: Scalars['String']['input'];
};

export enum UserRoleGroup {
  Admin = 'Admin',
  Executor = 'Executor',
  Manager = 'Manager',
  Operator = 'Operator'
}

export type UpdateActivityMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  input: ActivityInput;
}>;


export type UpdateActivityMutation = { __typename?: 'Mutation', updateActivity: { __typename?: 'Activity', id: string, activityId?: string | null, name: string, description?: string | null, status?: Status | null, statusCode?: StatusCode | null, reviewStatus?: ReviewStatus | null, unableToWork?: boolean | null, percentComplete?: number | null, progress?: number | null, lastPercentComplete?: number | null, startDate?: any | null, finishDate?: any | null, actualStartDate?: any | null, actualFinishDate?: any | null, baselineStartDate?: any | null, baselineFinishDate?: any | null, plannedFinishDate?: any | null, plannedStartDate?: any | null, projectId?: string | null, projectName?: string | null, assignedTo?: string | null, executorEmail?: string | null, plannedDuration?: number | null, actualDuration?: number | null, plannedEffort?: number | null, actualEffort?: number | null, sequenceNo?: number | null, disciplineCode?: string | null, companyCode?: string | null, allowedGroups?: Array<string> | null, activityCodes?: Array<{ __typename?: 'ActivityCode', id: string, codeTypeName?: string | null, codeTypeDescription?: string | null, codeTypeValue?: string | null, createdAt?: any | null, updatedAt?: any | null }> | null, contractor?: { __typename?: 'Contractor', id: string, name: string, contractorNumber: string, description?: string | null } | null, resources?: Array<{ __typename?: 'Resource', resourceCode?: string | null, resourceName?: string | null, discipline?: string | null }> | null, comments?: Array<{ __typename?: 'Comment', author?: string | null, text?: string | null, createdAt?: any | null }> | null, attachments?: Array<{ __typename?: 'Attachment', name: string, contentType?: string | null, size?: number | null, key?: string | null, uploadDate?: any | null, uploadedBy?: string | null }> | null, evidences?: Array<{ __typename?: 'Attachment', name: string, description?: string | null, contentType?: string | null, size?: number | null, key?: string | null, uploadDate?: any | null, uploadedBy?: string | null }> | null, logEvents?: Array<{ __typename?: 'LogEvent', timestamp?: any | null, event?: string | null, changedBy?: string | null } | null> | null } };

export type UpdateMultipleActivitiesMutationVariables = Exact<{
  input: Array<UpdateActivityInput> | UpdateActivityInput;
}>;


export type UpdateMultipleActivitiesMutation = { __typename?: 'Mutation', updateActivities: Array<{ __typename?: 'Activity', id: string, activityId?: string | null, name: string, status?: Status | null, statusCode?: StatusCode | null, reviewStatus?: ReviewStatus | null, progress?: number | null, percentComplete?: number | null, lastPercentComplete?: number | null, unableToWork?: boolean | null, assignedTo?: string | null, plannedEffort?: number | null, actualEffort?: number | null, companyCode?: string | null, disciplineCode?: string | null, contractor?: { __typename?: 'Contractor', id: string, name: string, contractorNumber: string } | null }> };

export type GetActivityQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type GetActivityQuery = { __typename?: 'Query', getActivity?: { __typename?: 'Activity', id: string, activityId?: string | null, name: string, description?: string | null, status?: Status | null, statusCode?: StatusCode | null, reviewStatus?: ReviewStatus | null, unableToWork?: boolean | null, percentComplete?: number | null, progress?: number | null, lastPercentComplete?: number | null, startDate?: any | null, finishDate?: any | null, actualStartDate?: any | null, actualFinishDate?: any | null, baselineStartDate?: any | null, baselineFinishDate?: any | null, plannedFinishDate?: any | null, plannedStartDate?: any | null, projectId?: string | null, projectName?: string | null, projectObjectId?: number | null, assignedTo?: string | null, executorEmail?: string | null, plannedDuration?: number | null, actualDuration?: number | null, plannedEffort?: number | null, actualEffort?: number | null, scopeNr?: string | null, scopeId?: string | null, scopeDescription?: string | null, equipment?: string | null, equipmentDescription?: string | null, floc?: string | null, jobOrderNumber?: string | null, notificationNumber?: string | null, discipline?: string | null, sequenceNo?: number | null, disciplineCode?: string | null, companyCode?: string | null, resourceId?: string | null, resourceName?: string | null, resourceObjectId?: number | null, allowedGroups?: Array<string> | null, activityCodes?: Array<{ __typename?: 'ActivityCode', id: string, codeTypeName?: string | null, codeTypeDescription?: string | null, codeTypeValue?: string | null, createdAt?: any | null, updatedAt?: any | null }> | null, predecessorActivities?: Array<{ __typename?: 'Activity', id: string, name: string, status?: Status | null, activityId?: string | null, percentComplete?: number | null, plannedStartDate?: any | null, plannedFinishDate?: any | null } | null> | null, successorActivities?: Array<{ __typename?: 'Activity', id: string, name: string, status?: Status | null, activityId?: string | null, percentComplete?: number | null, plannedStartDate?: any | null, plannedFinishDate?: any | null } | null> | null, contractor?: { __typename?: 'Contractor', id: string, name: string, contractorNumber: string, description?: string | null } | null, comments?: Array<{ __typename?: 'Comment', author?: string | null, text?: string | null, createdAt?: any | null }> | null, attachments?: Array<{ __typename?: 'Attachment', name: string, contentType?: string | null, size?: number | null, key?: string | null, uploadDate?: any | null, uploadedBy?: string | null }> | null, evidences?: Array<{ __typename?: 'Attachment', name: string, contentType?: string | null, description?: string | null, size?: number | null, key?: string | null, uploadDate?: any | null, uploadedBy?: string | null }> | null, logEvents?: Array<{ __typename?: 'LogEvent', timestamp?: any | null, event?: string | null, changedBy?: string | null } | null> | null } | null };

export type ListActivitiesQueryVariables = Exact<{ [key: string]: never; }>;


export type ListActivitiesQuery = { __typename?: 'Query', listActivities: Array<{ __typename?: 'Activity', id: string, activityId?: string | null, name: string, description?: string | null, status?: Status | null, statusCode?: StatusCode | null, reviewStatus?: ReviewStatus | null, progress?: number | null, percentComplete?: number | null, unableToWork?: boolean | null, assignedTo?: string | null, executorEmail?: string | null, plannedEffort?: number | null, actualEffort?: number | null, startDate?: any | null, finishDate?: any | null, plannedStartDate?: any | null, plannedFinishDate?: any | null, sequenceNo?: number | null, companyCode?: string | null, disciplineCode?: string | null, contractor?: { __typename?: 'Contractor', id: string, name: string, contractorNumber: string } | null }> };

export type GetActivityWithSyncQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type GetActivityWithSyncQuery = { __typename?: 'Query', getActivity?: { __typename?: 'Activity', id: string, name: string, syncStatus?: { __typename?: 'ActivitySync', targetSystem?: string | null, lastSyncedAt?: any | null, syncStatus?: string | null } | null, logEvents?: Array<{ __typename?: 'LogEvent', timestamp?: any | null, event?: string | null, changedBy?: string | null } | null> | null } | null };

export type GetContractorQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type GetContractorQuery = { __typename?: 'Query', getContractor?: { __typename?: 'Contractor', id: string, contractorNumber: string, name: string, description?: string | null, createdAt: any, updatedAt: any, users?: Array<{ __typename?: 'User', username: string, email?: string | null } | null> | null } | null };

export type ListContractorsQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>;
  nextToken?: InputMaybe<Scalars['String']['input']>;
}>;


export type ListContractorsQuery = { __typename?: 'Query', listContractors: { __typename?: 'ContractorConnection', nextToken?: string | null, totalCount?: number | null, items: Array<{ __typename?: 'Contractor', id: string, contractorNumber: string, name: string, description?: string | null, createdAt: any, updatedAt: any, users?: Array<{ __typename?: 'User', username: string, email?: string | null } | null> | null }> } };

export type CreateContractorMutationVariables = Exact<{
  input: ContractorInput;
}>;


export type CreateContractorMutation = { __typename?: 'Mutation', createContractor: { __typename?: 'Contractor', id: string, contractorNumber: string, name: string, description?: string | null, createdAt: any, updatedAt: any } };

export type UpdateContractorMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  input: ContractorInput;
}>;


export type UpdateContractorMutation = { __typename?: 'Mutation', updateContractor: { __typename?: 'Contractor', id: string, contractorNumber: string, name: string, description?: string | null, createdAt: any, updatedAt: any } };

export type DeleteContractorMutationVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type DeleteContractorMutation = { __typename?: 'Mutation', deleteContractor: string };

export type AddUserToContractorMutationVariables = Exact<{
  username: Scalars['String']['input'];
  contractorId: Scalars['ID']['input'];
}>;


export type AddUserToContractorMutation = { __typename?: 'Mutation', addUserToContractor: { __typename?: 'User', username: string, contractors?: Array<{ __typename?: 'Contractor', id: string, name: string, contractorNumber: string } | null> | null } };

export type RemoveUserFromContractorMutationVariables = Exact<{
  username: Scalars['String']['input'];
  contractorId: Scalars['ID']['input'];
}>;


export type RemoveUserFromContractorMutation = { __typename?: 'Mutation', removeUserFromContractor: { __typename?: 'User', username: string, contractors?: Array<{ __typename?: 'Contractor', id: string, name: string, contractorNumber: string } | null> | null } };

export type GetCountByStatusQueryVariables = Exact<{
  projectId: Scalars['ID']['input'];
}>;


export type GetCountByStatusQuery = { __typename?: 'Query', getCountByStatus: Array<{ __typename?: 'StatusCount', count: number, status: Status }> };

export type GetCompletedActivitiesByDayQueryVariables = Exact<{
  projectId: Scalars['ID']['input'];
}>;


export type GetCompletedActivitiesByDayQuery = { __typename?: 'Query', getCompletedActivitiesByDay: Array<{ __typename?: 'DailyActivityCount', date: string, count: number }> };

export type GetPlannedActivitiesByDayQueryVariables = Exact<{
  projectId: Scalars['ID']['input'];
}>;


export type GetPlannedActivitiesByDayQuery = { __typename?: 'Query', getPlannedActivitiesByDay: Array<{ __typename?: 'DailyActivityCount', date: string, count: number }> };

export type GetCountOfUnableToWorkActivitiesQueryVariables = Exact<{
  projectId: Scalars['ID']['input'];
}>;


export type GetCountOfUnableToWorkActivitiesQuery = { __typename?: 'Query', getCountOfUnableToWorkActivities: number };

export type GetActivityCountGroupedByStatusAndDisciplineQueryVariables = Exact<{
  projectId: Scalars['ID']['input'];
}>;


export type GetActivityCountGroupedByStatusAndDisciplineQuery = { __typename?: 'Query', getActivityCountGroupedByStatusAndDiscipline: Array<{ __typename?: 'StatusDisciplineCount', status: Status, discipline: string, count: number }> };

export type GetDownloadUrlMutationVariables = Exact<{
  key: Scalars['String']['input'];
  activityId: Scalars['ID']['input'];
}>;


export type GetDownloadUrlMutation = { __typename?: 'Mutation', getDownloadUrl?: { __typename?: 'S3Payload', key: string, url: string } | null };

export type GetUploadUrlMutationVariables = Exact<{
  filename: Scalars['String']['input'];
  activityId: Scalars['ID']['input'];
}>;


export type GetUploadUrlMutation = { __typename?: 'Mutation', getUploadUrl?: { __typename?: 'S3Payload', key: string, url: string } | null };

export type GetDeleteUrlMutationVariables = Exact<{
  key: Scalars['String']['input'];
  activityId: Scalars['ID']['input'];
}>;


export type GetDeleteUrlMutation = { __typename?: 'Mutation', getDeleteUrl?: { __typename?: 'S3Payload', key: string, url: string } | null };

export type GetGroupQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type GetGroupQuery = { __typename?: 'Query', getGroup?: { __typename?: 'Group', id: string, name: string, groupType: GroupType, description?: string | null, createdAt?: any | null, updatedAt?: any | null, users?: Array<{ __typename?: 'User', username: string, email?: string | null }> | null } | null };

export type ListGroupsQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>;
  nextToken?: InputMaybe<Scalars['String']['input']>;
}>;


export type ListGroupsQuery = { __typename?: 'Query', listGroups: { __typename?: 'GroupConnection', nextToken?: string | null, totalCount?: number | null, items: Array<{ __typename?: 'Group', id: string, name: string, groupType: GroupType, description?: string | null, createdAt?: any | null, updatedAt?: any | null, users?: Array<{ __typename?: 'User', username: string, email?: string | null }> | null }> } };

export type CreateGroupMutationVariables = Exact<{
  input: GroupInput;
}>;


export type CreateGroupMutation = { __typename?: 'Mutation', createGroup: { __typename?: 'Group', id: string, name: string, groupType: GroupType, description?: string | null, createdAt?: any | null, updatedAt?: any | null } };

export type UpdateGroupMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  input: GroupInput;
}>;


export type UpdateGroupMutation = { __typename?: 'Mutation', updateGroup: { __typename?: 'Group', id: string, name: string, groupType: GroupType, description?: string | null, createdAt?: any | null, updatedAt?: any | null } };

export type DeleteGroupMutationVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type DeleteGroupMutation = { __typename?: 'Mutation', deleteGroup: string };

export type GetProjectQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type GetProjectQuery = { __typename?: 'Query', getProject?: { __typename?: 'Project', id: string, projectId: string, name: string, displayName: string, description?: string | null, status?: ProjectStatus | null, startDate?: any | null, finishDate?: any | null, allowStatusReview?: boolean | null, activityCount?: number | null, obsName?: string | null, obsObjectId?: number | null, parentEPSObjectId?: number | null, parentEPSId?: string | null, parentEPSName?: string | null, wbsObjectId?: number | null, isSyncing?: boolean | null, isVisible?: boolean | null, createdAt?: any | null, updatedAt?: any | null, managerGroups?: Array<{ __typename?: 'Group', id: string, name: string }> | null, operatorGroups?: Array<{ __typename?: 'Group', id: string, name: string }> | null, workerGroups?: Array<{ __typename?: 'Group', id: string, name: string }> | null } | null };

export type ListProjectsShortQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>;
  nextToken?: InputMaybe<Scalars['String']['input']>;
}>;


export type ListProjectsShortQuery = { __typename?: 'Query', listProjects: { __typename?: 'ProjectConnection', items: Array<{ __typename?: 'Project', id: string, projectId: string, name: string, managerGroups?: Array<{ __typename?: 'Group', id: string, name: string }> | null, operatorGroups?: Array<{ __typename?: 'Group', id: string, name: string }> | null, workerGroups?: Array<{ __typename?: 'Group', id: string, name: string }> | null }> } };

export type ListProjectsQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>;
  nextToken?: InputMaybe<Scalars['String']['input']>;
}>;


export type ListProjectsQuery = { __typename?: 'Query', listProjects: { __typename?: 'ProjectConnection', nextToken?: string | null, totalCount?: number | null, items: Array<{ __typename?: 'Project', id: string, projectId: string, name: string, description?: string | null, status?: ProjectStatus | null, startDate?: any | null, finishDate?: any | null, allowStatusReview?: boolean | null, activityCount?: number | null, obsName?: string | null, obsObjectId?: number | null, parentEPSObjectId?: number | null, parentEPSId?: string | null, parentEPSName?: string | null, wbsObjectId?: number | null, isSyncing?: boolean | null, isVisible?: boolean | null, createdAt?: any | null, updatedAt?: any | null, managerGroups?: Array<{ __typename?: 'Group', id: string, name: string }> | null, operatorGroups?: Array<{ __typename?: 'Group', id: string, name: string }> | null, workerGroups?: Array<{ __typename?: 'Group', id: string, name: string }> | null }> } };

export type ListProjectsWithActivitiesQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>;
  nextToken?: InputMaybe<Scalars['String']['input']>;
}>;


export type ListProjectsWithActivitiesQuery = { __typename?: 'Query', listProjects: { __typename?: 'ProjectConnection', nextToken?: string | null, totalCount?: number | null, items: Array<{ __typename?: 'Project', id: string, name: string, description?: string | null, activities: { __typename?: 'ActivityConnection', items: Array<{ __typename?: 'Activity', name: string, status?: Status | null, unableToWork?: boolean | null, activityId?: string | null, id: string, percentComplete?: number | null, startDate?: any | null, finishDate?: any | null, plannedFinishDate?: any | null, plannedStartDate?: any | null }> } }> } };

export type GetProjectWithActivitiesQueryVariables = Exact<{
  id: Scalars['ID']['input'];
  limit?: InputMaybe<Scalars['Int']['input']>;
  nextToken?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Status>;
  unableToWork?: InputMaybe<Scalars['Boolean']['input']>;
  plannedStartDateFrom?: InputMaybe<Scalars['AWSDateTime']['input']>;
  plannedStartDateTo?: InputMaybe<Scalars['AWSDateTime']['input']>;
  plannedFinishDateFrom?: InputMaybe<Scalars['AWSDateTime']['input']>;
  plannedFinishDateTo?: InputMaybe<Scalars['AWSDateTime']['input']>;
}>;


export type GetProjectWithActivitiesQuery = { __typename?: 'Query', getProject?: { __typename?: 'Project', id: string, name: string, description?: string | null, activities: { __typename?: 'ActivityConnection', nextToken?: string | null, totalCount: number, items: Array<{ __typename?: 'Activity', activityId?: string | null, id: string, percentComplete?: number | null, lastPercentComplete?: number | null, name: string, description?: string | null, floc?: string | null, workorderNo?: string | null, jobOrderNumber?: string | null, discipline?: string | null, scopeNr?: string | null, scopeId?: string | null, status?: Status | null, reviewStatus?: ReviewStatus | null, unableToWork?: boolean | null, plannedStartDate?: any | null, plannedFinishDate?: any | null }> } } | null };

export type UpdateProjectMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  input: ProjectInput;
}>;


export type UpdateProjectMutation = { __typename?: 'Mutation', updateProject: { __typename?: 'Project', id: string, isSyncing?: boolean | null, isVisible?: boolean | null, managerGroups?: Array<{ __typename?: 'Group', id: string, name: string }> | null, operatorGroups?: Array<{ __typename?: 'Group', id: string, name: string }> | null, workerGroups?: Array<{ __typename?: 'Group', id: string, name: string }> | null } };

export type ListSyncJobsQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>;
  nextToken?: InputMaybe<Scalars['String']['input']>;
  syncType?: InputMaybe<Scalars['String']['input']>;
  projectId?: InputMaybe<Scalars['ID']['input']>;
  status?: InputMaybe<SyncJobStatus>;
}>;


export type ListSyncJobsQuery = { __typename?: 'Query', listSyncJobs: { __typename?: 'SyncJobConnection', nextToken?: string | null, totalCount?: number | null, items: Array<{ __typename?: 'SyncJob', id: string, syncType: string, projectId?: string | null, timestamp: any, status: SyncJobStatus, executionTime?: number | null, fetchTime?: number | null, writeTime?: number | null, projectsProcessed?: number | null, activitiesProcessed?: number | null, activityCodesProcessed?: number | null, relationshipsProcessed?: number | null }> } };

export type GetUserQueryVariables = Exact<{
  username: Scalars['String']['input'];
}>;


export type GetUserQuery = { __typename?: 'Query', getUser?: { __typename?: 'User', username: string, email?: string | null, disciplines?: Array<string | null> | null, equipments?: Array<string | null> | null, createdAt?: any | null, updatedAt?: any | null, managerGroups?: Array<{ __typename?: 'Group', id: string, name: string, groupType: GroupType }> | null, operatorGroups?: Array<{ __typename?: 'Group', id: string, name: string, groupType: GroupType }> | null, workerGroups?: Array<{ __typename?: 'Group', id: string, name: string, groupType: GroupType }> | null, contractors?: Array<{ __typename?: 'Contractor', id: string, name: string, contractorNumber: string, description?: string | null } | null> | null } | null };

export type ListUsersQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>;
  nextToken?: InputMaybe<Scalars['String']['input']>;
}>;


export type ListUsersQuery = { __typename?: 'Query', listUsers: { __typename?: 'UserConnection', nextToken?: string | null, totalCount?: number | null, items: Array<{ __typename?: 'User', username: string, email?: string | null, disciplines?: Array<string | null> | null, equipments?: Array<string | null> | null, createdAt?: any | null, updatedAt?: any | null, managerGroups?: Array<{ __typename?: 'Group', id: string, name: string }> | null, operatorGroups?: Array<{ __typename?: 'Group', id: string, name: string }> | null, workerGroups?: Array<{ __typename?: 'Group', id: string, name: string }> | null }> } };

export type CreateUserMutationVariables = Exact<{
  input: UserInput;
}>;


export type CreateUserMutation = { __typename?: 'Mutation', createUser: { __typename?: 'User', username: string, email?: string | null, createdAt?: any | null, updatedAt?: any | null } };

export type DeleteUserMutationVariables = Exact<{
  username: Scalars['String']['input'];
}>;


export type DeleteUserMutation = { __typename?: 'Mutation', deleteUser: string };

export type AddUserToGroupMutationVariables = Exact<{
  username: Scalars['String']['input'];
  groupId: Scalars['ID']['input'];
}>;


export type AddUserToGroupMutation = { __typename?: 'Mutation', addUserToGroup: { __typename?: 'User', username: string, managerGroups?: Array<{ __typename?: 'Group', id: string, name: string, groupType: GroupType }> | null, operatorGroups?: Array<{ __typename?: 'Group', id: string, name: string, groupType: GroupType }> | null, workerGroups?: Array<{ __typename?: 'Group', id: string, name: string, groupType: GroupType }> | null } };

export type RemoveUserFromGroupMutationVariables = Exact<{
  username: Scalars['String']['input'];
  groupId: Scalars['ID']['input'];
}>;


export type RemoveUserFromGroupMutation = { __typename?: 'Mutation', removeUserFromGroup: { __typename?: 'User', username: string, managerGroups?: Array<{ __typename?: 'Group', id: string, name: string, groupType: GroupType }> | null, operatorGroups?: Array<{ __typename?: 'Group', id: string, name: string, groupType: GroupType }> | null, workerGroups?: Array<{ __typename?: 'Group', id: string, name: string, groupType: GroupType }> | null } };

export type UpdateUserMutationVariables = Exact<{
  username: Scalars['String']['input'];
  input: UserInput;
}>;


export type UpdateUserMutation = { __typename?: 'Mutation', updateUser: { __typename?: 'User', username: string, email?: string | null, disciplines?: Array<string | null> | null, equipments?: Array<string | null> | null, updatedAt?: any | null } };

export const UpdateActivityDocument = gql`
    mutation UpdateActivity($id: ID!, $input: ActivityInput!) {
  updateActivity(id: $id, input: $input) {
    id
    activityId
    name
    description
    status
    statusCode
    reviewStatus
    unableToWork
    percentComplete
    progress
    lastPercentComplete
    startDate
    finishDate
    actualStartDate
    actualFinishDate
    baselineStartDate
    baselineFinishDate
    plannedFinishDate
    plannedStartDate
    projectId
    projectName
    assignedTo
    executorEmail
    plannedDuration
    actualDuration
    plannedEffort
    actualEffort
    sequenceNo
    disciplineCode
    companyCode
    activityCodes {
      id
      codeTypeName
      codeTypeDescription
      codeTypeValue
      createdAt
      updatedAt
    }
    contractor {
      id
      name
      contractorNumber
      description
    }
    resources {
      resourceCode
      resourceName
      discipline
    }
    comments {
      author
      text
      createdAt
    }
    attachments {
      name
      contentType
      size
      key
      uploadDate
      uploadedBy
    }
    evidences {
      name
      description
      contentType
      size
      key
      uploadDate
      uploadedBy
    }
    logEvents {
      timestamp
      event
      changedBy
    }
    allowedGroups
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class UpdateActivityGQL extends Apollo.Mutation<UpdateActivityMutation, UpdateActivityMutationVariables> {
    override document = UpdateActivityDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const UpdateMultipleActivitiesDocument = gql`
    mutation UpdateMultipleActivities($input: [UpdateActivityInput!]!) {
  updateActivities(input: $input) {
    id
    activityId
    name
    status
    statusCode
    reviewStatus
    progress
    percentComplete
    lastPercentComplete
    unableToWork
    assignedTo
    plannedEffort
    actualEffort
    companyCode
    disciplineCode
    contractor {
      id
      name
      contractorNumber
    }
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class UpdateMultipleActivitiesGQL extends Apollo.Mutation<UpdateMultipleActivitiesMutation, UpdateMultipleActivitiesMutationVariables> {
    override document = UpdateMultipleActivitiesDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const GetActivityDocument = gql`
    query GetActivity($id: ID!) {
  getActivity(id: $id) {
    id
    activityId
    name
    description
    status
    statusCode
    reviewStatus
    unableToWork
    percentComplete
    progress
    lastPercentComplete
    startDate
    finishDate
    actualStartDate
    actualFinishDate
    baselineStartDate
    baselineFinishDate
    plannedFinishDate
    plannedStartDate
    projectId
    projectName
    projectObjectId
    assignedTo
    executorEmail
    plannedDuration
    actualDuration
    plannedEffort
    actualEffort
    scopeNr
    scopeId
    scopeDescription
    equipment
    equipmentDescription
    floc
    jobOrderNumber
    notificationNumber
    discipline
    sequenceNo
    disciplineCode
    companyCode
    activityCodes {
      id
      codeTypeName
      codeTypeDescription
      codeTypeValue
      createdAt
      updatedAt
    }
    predecessorActivities {
      id
      name
      status
      activityId
      percentComplete
      plannedStartDate
      plannedFinishDate
    }
    successorActivities {
      id
      name
      status
      activityId
      percentComplete
      plannedStartDate
      plannedFinishDate
    }
    contractor {
      id
      name
      contractorNumber
      description
    }
    resourceId
    resourceName
    resourceObjectId
    comments {
      author
      text
      createdAt
    }
    attachments {
      name
      contentType
      size
      key
      uploadDate
      uploadedBy
    }
    evidences {
      name
      contentType
      description
      size
      key
      uploadDate
      uploadedBy
    }
    logEvents {
      timestamp
      event
      changedBy
    }
    allowedGroups
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class GetActivityGQL extends Apollo.Query<GetActivityQuery, GetActivityQueryVariables> {
    override document = GetActivityDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const ListActivitiesDocument = gql`
    query ListActivities {
  listActivities {
    id
    activityId
    name
    description
    status
    statusCode
    reviewStatus
    progress
    percentComplete
    unableToWork
    assignedTo
    executorEmail
    plannedEffort
    actualEffort
    startDate
    finishDate
    plannedStartDate
    plannedFinishDate
    sequenceNo
    companyCode
    disciplineCode
    contractor {
      id
      name
      contractorNumber
    }
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class ListActivitiesGQL extends Apollo.Query<ListActivitiesQuery, ListActivitiesQueryVariables> {
    override document = ListActivitiesDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const GetActivityWithSyncDocument = gql`
    query GetActivityWithSync($id: ID!) {
  getActivity(id: $id) {
    id
    name
    syncStatus {
      targetSystem
      lastSyncedAt
      syncStatus
    }
    logEvents {
      timestamp
      event
      changedBy
    }
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class GetActivityWithSyncGQL extends Apollo.Query<GetActivityWithSyncQuery, GetActivityWithSyncQueryVariables> {
    override document = GetActivityWithSyncDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const GetContractorDocument = gql`
    query GetContractor($id: ID!) {
  getContractor(id: $id) {
    id
    contractorNumber
    name
    description
    users {
      username
      email
    }
    createdAt
    updatedAt
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class GetContractorGQL extends Apollo.Query<GetContractorQuery, GetContractorQueryVariables> {
    override document = GetContractorDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const ListContractorsDocument = gql`
    query ListContractors($limit: Int, $nextToken: String) {
  listContractors(limit: $limit, nextToken: $nextToken) {
    items {
      id
      contractorNumber
      name
      description
      users {
        username
        email
      }
      createdAt
      updatedAt
    }
    nextToken
    totalCount
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class ListContractorsGQL extends Apollo.Query<ListContractorsQuery, ListContractorsQueryVariables> {
    override document = ListContractorsDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const CreateContractorDocument = gql`
    mutation CreateContractor($input: ContractorInput!) {
  createContractor(input: $input) {
    id
    contractorNumber
    name
    description
    createdAt
    updatedAt
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class CreateContractorGQL extends Apollo.Mutation<CreateContractorMutation, CreateContractorMutationVariables> {
    override document = CreateContractorDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const UpdateContractorDocument = gql`
    mutation UpdateContractor($id: ID!, $input: ContractorInput!) {
  updateContractor(id: $id, input: $input) {
    id
    contractorNumber
    name
    description
    createdAt
    updatedAt
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class UpdateContractorGQL extends Apollo.Mutation<UpdateContractorMutation, UpdateContractorMutationVariables> {
    override document = UpdateContractorDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const DeleteContractorDocument = gql`
    mutation DeleteContractor($id: ID!) {
  deleteContractor(id: $id)
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class DeleteContractorGQL extends Apollo.Mutation<DeleteContractorMutation, DeleteContractorMutationVariables> {
    override document = DeleteContractorDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const AddUserToContractorDocument = gql`
    mutation AddUserToContractor($username: String!, $contractorId: ID!) {
  addUserToContractor(username: $username, contractorId: $contractorId) {
    username
    contractors {
      id
      name
      contractorNumber
    }
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class AddUserToContractorGQL extends Apollo.Mutation<AddUserToContractorMutation, AddUserToContractorMutationVariables> {
    override document = AddUserToContractorDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const RemoveUserFromContractorDocument = gql`
    mutation RemoveUserFromContractor($username: String!, $contractorId: ID!) {
  removeUserFromContractor(username: $username, contractorId: $contractorId) {
    username
    contractors {
      id
      name
      contractorNumber
    }
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class RemoveUserFromContractorGQL extends Apollo.Mutation<RemoveUserFromContractorMutation, RemoveUserFromContractorMutationVariables> {
    override document = RemoveUserFromContractorDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const GetCountByStatusDocument = gql`
    query GetCountByStatus($projectId: ID!) {
  getCountByStatus(projectId: $projectId) {
    count
    status
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class GetCountByStatusGQL extends Apollo.Query<GetCountByStatusQuery, GetCountByStatusQueryVariables> {
    override document = GetCountByStatusDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const GetCompletedActivitiesByDayDocument = gql`
    query GetCompletedActivitiesByDay($projectId: ID!) {
  getCompletedActivitiesByDay(projectId: $projectId) {
    date
    count
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class GetCompletedActivitiesByDayGQL extends Apollo.Query<GetCompletedActivitiesByDayQuery, GetCompletedActivitiesByDayQueryVariables> {
    override document = GetCompletedActivitiesByDayDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const GetPlannedActivitiesByDayDocument = gql`
    query GetPlannedActivitiesByDay($projectId: ID!) {
  getPlannedActivitiesByDay(projectId: $projectId) {
    date
    count
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class GetPlannedActivitiesByDayGQL extends Apollo.Query<GetPlannedActivitiesByDayQuery, GetPlannedActivitiesByDayQueryVariables> {
    override document = GetPlannedActivitiesByDayDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const GetCountOfUnableToWorkActivitiesDocument = gql`
    query GetCountOfUnableToWorkActivities($projectId: ID!) {
  getCountOfUnableToWorkActivities(projectId: $projectId)
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class GetCountOfUnableToWorkActivitiesGQL extends Apollo.Query<GetCountOfUnableToWorkActivitiesQuery, GetCountOfUnableToWorkActivitiesQueryVariables> {
    override document = GetCountOfUnableToWorkActivitiesDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const GetActivityCountGroupedByStatusAndDisciplineDocument = gql`
    query GetActivityCountGroupedByStatusAndDiscipline($projectId: ID!) {
  getActivityCountGroupedByStatusAndDiscipline(projectId: $projectId) {
    status
    discipline
    count
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class GetActivityCountGroupedByStatusAndDisciplineGQL extends Apollo.Query<GetActivityCountGroupedByStatusAndDisciplineQuery, GetActivityCountGroupedByStatusAndDisciplineQueryVariables> {
    override document = GetActivityCountGroupedByStatusAndDisciplineDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const GetDownloadUrlDocument = gql`
    mutation getDownloadUrl($key: String!, $activityId: ID!) {
  getDownloadUrl(key: $key, activityId: $activityId) {
    key
    url
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class GetDownloadUrlGQL extends Apollo.Mutation<GetDownloadUrlMutation, GetDownloadUrlMutationVariables> {
    override document = GetDownloadUrlDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const GetUploadUrlDocument = gql`
    mutation getUploadUrl($filename: String!, $activityId: ID!) {
  getUploadUrl(fileName: $filename, activityId: $activityId) {
    key
    url
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class GetUploadUrlGQL extends Apollo.Mutation<GetUploadUrlMutation, GetUploadUrlMutationVariables> {
    override document = GetUploadUrlDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const GetDeleteUrlDocument = gql`
    mutation getDeleteUrl($key: String!, $activityId: ID!) {
  getDeleteUrl(key: $key, activityId: $activityId) {
    key
    url
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class GetDeleteUrlGQL extends Apollo.Mutation<GetDeleteUrlMutation, GetDeleteUrlMutationVariables> {
    override document = GetDeleteUrlDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const GetGroupDocument = gql`
    query GetGroup($id: ID!) {
  getGroup(id: $id) {
    id
    name
    groupType
    description
    users {
      username
      email
    }
    createdAt
    updatedAt
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class GetGroupGQL extends Apollo.Query<GetGroupQuery, GetGroupQueryVariables> {
    override document = GetGroupDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const ListGroupsDocument = gql`
    query ListGroups($limit: Int, $nextToken: String) {
  listGroups(limit: $limit, nextToken: $nextToken) {
    items {
      id
      name
      groupType
      description
      users {
        username
        email
      }
      createdAt
      updatedAt
    }
    nextToken
    totalCount
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class ListGroupsGQL extends Apollo.Query<ListGroupsQuery, ListGroupsQueryVariables> {
    override document = ListGroupsDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const CreateGroupDocument = gql`
    mutation CreateGroup($input: GroupInput!) {
  createGroup(input: $input) {
    id
    name
    groupType
    description
    createdAt
    updatedAt
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class CreateGroupGQL extends Apollo.Mutation<CreateGroupMutation, CreateGroupMutationVariables> {
    override document = CreateGroupDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const UpdateGroupDocument = gql`
    mutation UpdateGroup($id: ID!, $input: GroupInput!) {
  updateGroup(id: $id, input: $input) {
    id
    name
    groupType
    description
    createdAt
    updatedAt
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class UpdateGroupGQL extends Apollo.Mutation<UpdateGroupMutation, UpdateGroupMutationVariables> {
    override document = UpdateGroupDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const DeleteGroupDocument = gql`
    mutation DeleteGroup($id: ID!) {
  deleteGroup(id: $id)
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class DeleteGroupGQL extends Apollo.Mutation<DeleteGroupMutation, DeleteGroupMutationVariables> {
    override document = DeleteGroupDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const GetProjectDocument = gql`
    query GetProject($id: ID!) {
  getProject(id: $id) {
    id
    projectId
    name
    displayName
    description
    status
    startDate
    finishDate
    allowStatusReview
    activityCount
    obsName
    obsObjectId
    parentEPSObjectId
    parentEPSId
    parentEPSName
    wbsObjectId
    isSyncing
    isVisible
    createdAt
    updatedAt
    managerGroups {
      id
      name
    }
    operatorGroups {
      id
      name
    }
    workerGroups {
      id
      name
    }
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class GetProjectGQL extends Apollo.Query<GetProjectQuery, GetProjectQueryVariables> {
    override document = GetProjectDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const ListProjectsShortDocument = gql`
    query ListProjectsShort($limit: Int, $nextToken: String) {
  listProjects(limit: $limit, nextToken: $nextToken) {
    items {
      id
      projectId
      name
      managerGroups {
        id
        name
      }
      operatorGroups {
        id
        name
      }
      workerGroups {
        id
        name
      }
    }
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class ListProjectsShortGQL extends Apollo.Query<ListProjectsShortQuery, ListProjectsShortQueryVariables> {
    override document = ListProjectsShortDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const ListProjectsDocument = gql`
    query ListProjects($limit: Int, $nextToken: String) {
  listProjects(limit: $limit, nextToken: $nextToken) {
    items {
      id
      projectId
      name
      description
      status
      startDate
      finishDate
      allowStatusReview
      activityCount
      obsName
      obsObjectId
      parentEPSObjectId
      parentEPSId
      parentEPSName
      wbsObjectId
      isSyncing
      isVisible
      createdAt
      updatedAt
      managerGroups {
        id
        name
      }
      operatorGroups {
        id
        name
      }
      workerGroups {
        id
        name
      }
    }
    nextToken
    totalCount
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class ListProjectsGQL extends Apollo.Query<ListProjectsQuery, ListProjectsQueryVariables> {
    override document = ListProjectsDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const ListProjectsWithActivitiesDocument = gql`
    query ListProjectsWithActivities($limit: Int, $nextToken: String) {
  listProjects(limit: $limit, nextToken: $nextToken) {
    items {
      id
      name
      description
      activities {
        items {
          name
          status
          unableToWork
          activityId
          id
          percentComplete
          startDate
          finishDate
          plannedFinishDate
          plannedStartDate
        }
      }
    }
    nextToken
    totalCount
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class ListProjectsWithActivitiesGQL extends Apollo.Query<ListProjectsWithActivitiesQuery, ListProjectsWithActivitiesQueryVariables> {
    override document = ListProjectsWithActivitiesDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const GetProjectWithActivitiesDocument = gql`
    query GetProjectWithActivities($id: ID!, $limit: Int, $nextToken: String, $status: Status, $unableToWork: Boolean, $plannedStartDateFrom: AWSDateTime, $plannedStartDateTo: AWSDateTime, $plannedFinishDateFrom: AWSDateTime, $plannedFinishDateTo: AWSDateTime) {
  getProject(id: $id) {
    id
    name
    description
    activities(
      limit: $limit
      nextToken: $nextToken
      status: $status
      unableToWork: $unableToWork
      plannedStartDateFrom: $plannedStartDateFrom
      plannedStartDateTo: $plannedStartDateTo
      plannedFinishDateFrom: $plannedFinishDateFrom
      plannedFinishDateTo: $plannedFinishDateTo
    ) {
      items {
        activityId
        id
        percentComplete
        lastPercentComplete
        name
        description
        floc
        workorderNo
        jobOrderNumber
        discipline
        scopeNr
        scopeId
        status
        reviewStatus
        unableToWork
        plannedStartDate
        plannedFinishDate
      }
      nextToken
      totalCount
    }
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class GetProjectWithActivitiesGQL extends Apollo.Query<GetProjectWithActivitiesQuery, GetProjectWithActivitiesQueryVariables> {
    override document = GetProjectWithActivitiesDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const UpdateProjectDocument = gql`
    mutation UpdateProject($id: ID!, $input: ProjectInput!) {
  updateProject(id: $id, input: $input) {
    id
    isSyncing
    isVisible
    managerGroups {
      id
      name
    }
    operatorGroups {
      id
      name
    }
    workerGroups {
      id
      name
    }
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class UpdateProjectGQL extends Apollo.Mutation<UpdateProjectMutation, UpdateProjectMutationVariables> {
    override document = UpdateProjectDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const ListSyncJobsDocument = gql`
    query ListSyncJobs($limit: Int, $nextToken: String, $syncType: String, $projectId: ID, $status: SyncJobStatus) {
  listSyncJobs(
    limit: $limit
    nextToken: $nextToken
    syncType: $syncType
    projectId: $projectId
    status: $status
  ) {
    items {
      id
      syncType
      projectId
      timestamp
      status
      executionTime
      fetchTime
      writeTime
      projectsProcessed
      activitiesProcessed
      activityCodesProcessed
      relationshipsProcessed
    }
    nextToken
    totalCount
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class ListSyncJobsGQL extends Apollo.Query<ListSyncJobsQuery, ListSyncJobsQueryVariables> {
    override document = ListSyncJobsDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const GetUserDocument = gql`
    query GetUser($username: String!) {
  getUser(username: $username) {
    username
    email
    managerGroups {
      id
      name
      groupType
    }
    operatorGroups {
      id
      name
      groupType
    }
    workerGroups {
      id
      name
      groupType
    }
    disciplines
    equipments
    contractors {
      id
      name
      contractorNumber
      description
    }
    createdAt
    updatedAt
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class GetUserGQL extends Apollo.Query<GetUserQuery, GetUserQueryVariables> {
    override document = GetUserDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const ListUsersDocument = gql`
    query ListUsers($limit: Int, $nextToken: String) {
  listUsers(limit: $limit, nextToken: $nextToken) {
    items {
      username
      email
      managerGroups {
        id
        name
      }
      operatorGroups {
        id
        name
      }
      workerGroups {
        id
        name
      }
      disciplines
      equipments
      createdAt
      updatedAt
    }
    nextToken
    totalCount
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class ListUsersGQL extends Apollo.Query<ListUsersQuery, ListUsersQueryVariables> {
    override document = ListUsersDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const CreateUserDocument = gql`
    mutation CreateUser($input: UserInput!) {
  createUser(input: $input) {
    username
    email
    createdAt
    updatedAt
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class CreateUserGQL extends Apollo.Mutation<CreateUserMutation, CreateUserMutationVariables> {
    override document = CreateUserDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const DeleteUserDocument = gql`
    mutation DeleteUser($username: String!) {
  deleteUser(username: $username)
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class DeleteUserGQL extends Apollo.Mutation<DeleteUserMutation, DeleteUserMutationVariables> {
    override document = DeleteUserDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const AddUserToGroupDocument = gql`
    mutation AddUserToGroup($username: String!, $groupId: ID!) {
  addUserToGroup(username: $username, groupId: $groupId) {
    username
    managerGroups {
      id
      name
      groupType
    }
    operatorGroups {
      id
      name
      groupType
    }
    workerGroups {
      id
      name
      groupType
    }
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class AddUserToGroupGQL extends Apollo.Mutation<AddUserToGroupMutation, AddUserToGroupMutationVariables> {
    override document = AddUserToGroupDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const RemoveUserFromGroupDocument = gql`
    mutation RemoveUserFromGroup($username: String!, $groupId: ID!) {
  removeUserFromGroup(username: $username, groupId: $groupId) {
    username
    managerGroups {
      id
      name
      groupType
    }
    operatorGroups {
      id
      name
      groupType
    }
    workerGroups {
      id
      name
      groupType
    }
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class RemoveUserFromGroupGQL extends Apollo.Mutation<RemoveUserFromGroupMutation, RemoveUserFromGroupMutationVariables> {
    override document = RemoveUserFromGroupDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }
export const UpdateUserDocument = gql`
    mutation UpdateUser($username: String!, $input: UserInput!) {
  updateUser(username: $username, input: $input) {
    username
    email
    disciplines
    equipments
    updatedAt
  }
}
    `;

  @Injectable({
    providedIn: 'root'
  })
  export class UpdateUserGQL extends Apollo.Mutation<UpdateUserMutation, UpdateUserMutationVariables> {
    override document = UpdateUserDocument;
    
    constructor(apollo: Apollo.Apollo) {
      super(apollo);
    }
  }