query GetGroup($id: ID!) {
  getGroup(id: $id) {
    id
    name
    groupType
    description
    users {
      username
      email
    }
    createdAt
    updatedAt
  }
}

query ListGroups($limit: Int, $nextToken: String) {
  listGroups(limit: $limit, nextToken: $nextToken) {
    items {
      id
      name
      groupType
      description
      users {
        username
        email
      }
      createdAt
      updatedAt
    }
    nextToken
    totalCount
  }
}

mutation CreateGroup($input: GroupInput!) {
  createGroup(input: $input) {
    id
    name
    groupType
    description
    createdAt
    updatedAt
  }
}

mutation UpdateGroup($id: ID!, $input: GroupInput!) {
  updateGroup(id: $id, input: $input) {
    id
    name
    groupType
    description
    createdAt
    updatedAt
  }
}

mutation DeleteGroup($id: ID!) {
  deleteGroup(id: $id)
}
