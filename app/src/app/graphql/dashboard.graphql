query GetCountByStatus($projectId: ID!) {
  getCountByStatus(projectId: $projectId) {
    count
    status
  }
}

query GetCompletedActivitiesByDay($projectId: ID!) {
  getCompletedActivitiesByDay(projectId: $projectId) {
    date
    count
  }
}

query GetPlannedActivitiesByDay($projectId: ID!) {
  getPlannedActivitiesByDay(projectId: $projectId) {
    date
    count
  }
}

query GetCountOfUnableToWorkActivities($projectId: ID!) {
  getCountOfUnableToWorkActivities(projectId: $projectId)
}

query GetActivityCountGroupedByStatusAndDiscipline($projectId: ID!) {
  getActivityCountGroupedByStatusAndDiscipline(projectId: $projectId) {
    status
    discipline
    count
  }
}
