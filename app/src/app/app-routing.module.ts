import { authGuard } from './core/guards/auth.guard';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ActivityListComponent } from './components/activity-list/activity-list.component';
import { ActivityComponent } from './components/activity/activity.component';
import { DashboardComponent } from './components/dashboard/dashboard.component';
import { AboutComponent } from './components/about/about.component';
import { ManagerViewComponent } from './components/manager-view/manager-view.component';
import { LandingPageComponent } from './components/landing-page/landing-page.component';
import { UserManagementComponent } from './components/user-management/user-management.component';
import { ContractorManagementComponent } from './components/contractor-management/contractor-management.component';
import { USER_GROUPS } from './shared/constant.common';
import { ROUTER_PATHS } from './shared/constant.routing';
import { ProjectAdminComponent } from './components/project-admin/project-admin.component';
import { SyncJobsComponent } from './components/sync-jobs/sync-jobs.component';
import { DocPageComponent } from './components/doc-page/doc-page.component';


const routes: Routes = [
  { path: ROUTER_PATHS.ACTIVITY_LIST, component: ActivityListComponent, canActivate: [authGuard] },
  { path: ROUTER_PATHS.ACTIVITY, component: ActivityComponent, canActivate: [authGuard] },
  { path: ROUTER_PATHS.DASH_BOARD, component: DashboardComponent, canActivate: [authGuard], data: { roles: [USER_GROUPS.ADMIN, USER_GROUPS.MANAGER, USER_GROUPS.OPERATOR] } },
  { path: ROUTER_PATHS.MANAGER_VIEW, component: ManagerViewComponent, canActivate: [authGuard], data: { roles: [USER_GROUPS.ADMIN, USER_GROUPS.MANAGER, USER_GROUPS.OPERATOR] } },
  { path: ROUTER_PATHS.LANDING_PAGE, component: LandingPageComponent },
  { path: ROUTER_PATHS.ABOUT, component: AboutComponent },
  { path: ROUTER_PATHS.PROJECT_ADMIN, component: ProjectAdminComponent, canActivate: [authGuard], data: { roles: [USER_GROUPS.ADMIN] }},
  { path: ROUTER_PATHS.USER_MANAGEMENT, component: UserManagementComponent, canActivate: [authGuard], data: { roles: [USER_GROUPS.ADMIN] }},
  { path: ROUTER_PATHS.CONTRACTOR_MANAGEMENT, component: ContractorManagementComponent, canActivate: [authGuard], data: { roles: [USER_GROUPS.ADMIN] }},
  { path: ROUTER_PATHS.SYNC_JOBS, component: SyncJobsComponent, canActivate: [authGuard], data: { roles: [USER_GROUPS.ADMIN] }},
  { path: 'docs/:slug', component: DocPageComponent },
  { path: '**', redirectTo: ROUTER_PATHS.LANDING_PAGE, pathMatch: 'full' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
