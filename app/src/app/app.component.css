.main-content {
    flex: 1;
    padding: 16px;
    height: 100%;
    overflow: auto;
  }
  
  .footer {
    position: sticky;
    bottom: 0;
    width: 100%;
    height: 50px;
    text-align: center;
  }
  
  .sidenav-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
  }
  
  mat-sidenav-container,
  mat-sidenav-content,
  mat-sidenav {
    height: 100%;
  }
  
  .alda-sidenav-content {
    display: flex;
    flex-direction: column;
  }
  
  .sidenav {
    width: 200px;
  }
  
  .toolbar-title {
    flex: 1;
    text-decoration: none;
    color: inherit;
    cursor: pointer;
  }
  
  .logo {
    width: 40px;
    height: 40px;
    cursor: pointer;
    padding-right: 10px;
  }
  
  .spacer {
    flex: 1 1 auto;
  }
  
  .profile-picture {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
  }
  
  @media print {
    body, html {
      height: auto !important;
      overflow: visible !important;
    }
  
    mat-sidenav-container {
      overflow: visible !important;
      height: auto !important;
      display: block !important;
    }
  
    mat-sidenav {
      display: none !important;
    }
  
    mat-sidenav-content {
      overflow: visible !important;
      height: auto !important;
    }
  
    .no-print {
      display: none !important;
    }
  }
  
  
  