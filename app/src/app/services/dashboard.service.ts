import { Injectable } from '@angular/core';
import { Apollo } from 'apollo-angular';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { 
  GetCompletedActivitiesByDayGQL, 
  GetCountByStatusGQL, 
  GetCountOfUnableToWorkActivitiesGQL, 
  GetPlannedActivitiesByDayGQL
} from '../graphql/generated';
import gql from 'graphql-tag';

// Interface for the returned data structure
interface StatusCount {
  count: number;
  status: string;
}

interface DailyActivityCount {
  date: string;
  count: number;
}

interface StatusDisciplineCount {
  status: string;
  discipline: string;
  count: number;
}

@Injectable({
  providedIn: 'root'
})
export class DashboardService {
  constructor(
    private getCountByStatusGQL: GetCountByStatusGQL,
    private getCompletedActivitiesByDayGQL: GetCompletedActivitiesByDayGQL,
    private getPlannedActivitiesByDayGQL: GetPlannedActivitiesByDayGQL,
    private getCountOfUnableToWorkActivitiesGQL: GetCountOfUnableToWorkActivitiesGQL,
    private apollo: Apollo
  ) {}

  getCountByStatus(projectId: string): Observable<StatusCount[]> {
    return this.getCountByStatusGQL.watch({ projectId })
      .valueChanges
      .pipe(map(result => result.data.getCountByStatus as StatusCount[]));
  }

  getCompletedActivitiesByDay(projectId: string): Observable<DailyActivityCount[]> {
    return this.getCompletedActivitiesByDayGQL.fetch({ projectId })
      .pipe(map(result => {
        return result.data.getCompletedActivitiesByDay as DailyActivityCount[];
      }));
  }

  getPlannedActivitiesByDay(projectId: string): Observable<DailyActivityCount[]> {
    return this.getPlannedActivitiesByDayGQL.fetch({ projectId })
      .pipe(map(result => {
        return result.data.getPlannedActivitiesByDay as DailyActivityCount[];
      }));
  }

  getCountOfUnableToWorkActivities(projectId: string): Observable<number> {
    return this.getCountOfUnableToWorkActivitiesGQL.fetch({ projectId })
      .pipe(map(result => {
        return result.data.getCountOfUnableToWorkActivities;
      }));
  }

  getActivityCountGroupedByStatusAndDiscipline(projectId: string): Observable<StatusDisciplineCount[]> {
    const GET_ACTIVITY_COUNT_BY_STATUS_AND_DISCIPLINE = gql`
      query GetActivityCountGroupedByStatusAndDiscipline($projectId: ID!) {
        getActivityCountGroupedByStatusAndDiscipline(projectId: $projectId) {
          status
          discipline
          count
        }
      }
    `;

    return this.apollo.watchQuery<any>({
      query: GET_ACTIVITY_COUNT_BY_STATUS_AND_DISCIPLINE,
      variables: { projectId }
    })
    .valueChanges
    .pipe(
      map(result => result.data.getActivityCountGroupedByStatusAndDiscipline as StatusDisciplineCount[]),
      catchError(error => {
        console.error('Error fetching activity counts by status and discipline:', error);
        return of([]);
      })
    );
  }
}
