import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ListSyncJobsGQL, SyncJob, SyncJobConnection, SyncJobStatus } from '../graphql/generated';

@Injectable({
  providedIn: 'root'
})
export class SyncJobService {
  constructor(private listSyncJobsGQL: ListSyncJobsGQL) { }

  listSyncJobs(
    limit: number, 
    nextToken?: string
  ): Observable<SyncJobConnection> {
    return this.listSyncJobsGQL.watch({
      limit,
      nextToken
    })
    .valueChanges
    .pipe(map(result => result.data.listSyncJobs as SyncJobConnection));
  }
}
