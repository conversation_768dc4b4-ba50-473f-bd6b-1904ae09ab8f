import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ChartColorsService {
  // Custom color palette
  private colorPalette = {
    blue: '#009FE4',
    green: '#00BB7E',
    yellow: '#FFEE00',
    orange: '#FF7F41',
    pink: '#E6007E',
    purple: '#7D55C7',
    black: '#000000',
    white: '#FFFFFF',
  };

  // Pie chart colors
  public pieChartColors = {
    backgroundColor: [
      this.colorPalette.blue,
      this.colorPalette.green,
      this.colorPalette.yellow,
    ],
    hoverBackgroundColor: [
      this.colorPalette.blue,
      this.colorPalette.green,
      this.colorPalette.yellow,
    ],
  };

  // Line chart colors
  public lineChartColors = [
    {
      borderColor: this.colorPalette.blue,
      backgroundColor: this.colorPalette.green,
    },
    {
      borderColor: this.colorPalette.green,
      backgroundColor: this.colorPalette.orange,
    },
  ];

  constructor() {}

  getPieChartColors(): any {
    return this.pieChartColors;
  }

  getLineChartColors(): any[] {
    return this.lineChartColors;
  }

  getColorPalette(): Record<string, string> {
    return this.colorPalette;
  }
}
