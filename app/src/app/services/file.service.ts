import { Injectable } from '@angular/core';
import { GetDeleteUrlGQL, GetDownloadUrlGQL, GetUploadUrlGQL, S3Payload } from '../graphql/generated';
import { map, Observable } from 'rxjs';


@Injectable({
  providedIn: 'root',
})
export class FileService {

  constructor(
    private getDownloadUrlGQL: GetDownloadUrlGQL,
    private getUploadUrlGQL: GetUploadUrlGQL,
    private getDeleteUrlGQL: GetDeleteUrlGQL) {}

  getPresignedDownloadUrl(key: string, activityId: string): Observable<S3Payload> {
    return this.getDownloadUrlGQL.mutate({ key, activityId }).pipe(
        map(result => result.data.getDownloadUrl)
    );
  }

  getPresignedUploadUrl(fileName: string, activityId: string): Observable<S3Payload> {
    return this.getUploadUrlGQL.mutate({ filename: fileName, activityId }).pipe(
        map(result => result.data.getUploadUrl)
    );
  }

  getPresignedDeleteUrl(key: string, activityId: string): Observable<S3Payload> {
    return this.getDeleteUrlGQL.mutate({ key, activityId }).pipe(
        map(result => result.data.getDeleteUrl)
    );
  }
}
