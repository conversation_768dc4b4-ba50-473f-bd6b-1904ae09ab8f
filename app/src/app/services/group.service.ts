import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Apollo } from 'apollo-angular';
import { gql } from 'apollo-angular';
import { Group, GroupConnection, GroupInput } from '../graphql/generated';

@Injectable({
  providedIn: 'root'
})
export class GroupService {
  constructor(private apollo: Apollo) { }

  getGroup(id: string): Observable<Group> {
    return this.apollo.watchQuery<any>({
      query: gql`
        query GetGroup($id: ID!) {
          getGroup(id: $id) {
            id
            name
            groupType
            description
            users {
              username
              email
            }
            createdAt
            updatedAt
          }
        }
      `,
      variables: { id }
    })
      .valueChanges
      .pipe(map(result => result.data.getGroup as Group));
  }

  listGroups(limit: number = 10, nextToken?: string): Observable<GroupConnection> {
    return this.apollo.watchQuery<any>({
      query: gql`
        query ListGroups($limit: Int, $nextToken: String) {
          listGroups(limit: $limit, nextToken: $nextToken) {
            items {
              id
              name
              groupType
              description
              users {
                username
                email
              }
              createdAt
              updatedAt
            }
            nextToken
            totalCount
          }
        }
      `,
      variables: { limit, nextToken }
    })
      .valueChanges
      .pipe(map(result => result.data.listGroups as GroupConnection));
  }

  createGroup(input: GroupInput): Observable<Group> {
    return this.apollo.mutate<any>({
      mutation: gql`
        mutation CreateGroup($input: GroupInput!) {
          createGroup(input: $input) {
            id
            name
            groupType
            description
            createdAt
            updatedAt
          }
        }
      `,
      variables: { input }
    })
      .pipe(map(result => result.data.createGroup as Group));
  }

  updateGroup(id: string, input: GroupInput): Observable<Group> {
    return this.apollo.mutate<any>({
      mutation: gql`
        mutation UpdateGroup($id: ID!, $input: GroupInput!) {
          updateGroup(id: $id, input: $input) {
            id
            name
            groupType
            description
            createdAt
            updatedAt
          }
        }
      `,
      variables: { id, input }
    })
      .pipe(map(result => result.data.updateGroup as Group));
  }

  deleteGroup(id: string): Observable<string> {
    return this.apollo.mutate<any>({
      mutation: gql`
        mutation DeleteGroup($id: ID!) {
          deleteGroup(id: $id)
        }
      `,
      variables: { id }
    })
      .pipe(map(result => result.data.deleteGroup as string));
  }
}
