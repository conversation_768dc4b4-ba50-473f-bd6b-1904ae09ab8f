import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Apollo } from 'apollo-angular';
import { gql } from 'apollo-angular';
import { Contractor, ContractorConnection, ContractorInput, User } from '../graphql/generated';

@Injectable({
  providedIn: 'root'
})
export class ContractorService {
  constructor(private apollo: Apollo) { }

  getContractor(id: string): Observable<Contractor> {
    return this.apollo.watchQuery<any>({
      query: gql`
        query GetContractor($id: ID!) {
          getContractor(id: $id) {
            id
            contractorNumber
            name
            description
            users {
              username
              email
            }
            createdAt
            updatedAt
          }
        }
      `,
      variables: { id }
    })
      .valueChanges
      .pipe(map(result => result.data.getContractor as Contractor));
  }

  listContractors(limit: number = 10, nextToken?: string): Observable<ContractorConnection> {
    return this.apollo.watchQuery<any>({
      query: gql`
        query ListContractors($limit: Int, $nextToken: String) {
          listContractors(limit: $limit, nextToken: $nextToken) {
            items {
              id
              contractorNumber
              name
              description
              users {
                username
                email
              }
              createdAt
              updatedAt
            }
            nextToken
            totalCount
          }
        }
      `,
      variables: { limit, nextToken }
    })
      .valueChanges
      .pipe(map(result => result.data.listContractors as ContractorConnection));
  }

  createContractor(input: ContractorInput): Observable<Contractor> {
    return this.apollo.mutate<any>({
      mutation: gql`
        mutation CreateContractor($input: ContractorInput!) {
          createContractor(input: $input) {
            id
            contractorNumber
            name
            description
            createdAt
            updatedAt
          }
        }
      `,
      variables: { input }
    })
      .pipe(map(result => result.data.createContractor as Contractor));
  }

  updateContractor(id: string, input: ContractorInput): Observable<Contractor> {
    return this.apollo.mutate<any>({
      mutation: gql`
        mutation UpdateContractor($id: ID!, $input: ContractorInput!) {
          updateContractor(id: $id, input: $input) {
            id
            contractorNumber
            name
            description
            createdAt
            updatedAt
          }
        }
      `,
      variables: { id, input }
    })
      .pipe(map(result => result.data.updateContractor as Contractor));
  }

  deleteContractor(id: string): Observable<string> {
    return this.apollo.mutate<any>({
      mutation: gql`
        mutation DeleteContractor($id: ID!) {
          deleteContractor(id: $id)
        }
      `,
      variables: { id }
    })
      .pipe(map(result => result.data.deleteContractor as string));
  }

  addUserToContractor(username: string, contractorId: string): Observable<User> {
    return this.apollo.mutate<any>({
      mutation: gql`
        mutation AddUserToContractor($username: String!, $contractorId: ID!) {
          addUserToContractor(username: $username, contractorId: $contractorId) {
            username
            contractors {
              id
              name
              contractorNumber
            }
          }
        }
      `,
      variables: { username, contractorId }
    })
      .pipe(map(result => result.data.addUserToContractor as User));
  }

  removeUserFromContractor(username: string, contractorId: string): Observable<User> {
    return this.apollo.mutate<any>({
      mutation: gql`
        mutation RemoveUserFromContractor($username: String!, $contractorId: ID!) {
          removeUserFromContractor(username: $username, contractorId: $contractorId) {
            username
            contractors {
              id
              name
              contractorNumber
            }
          }
        }
      `,
      variables: { username, contractorId }
    })
      .pipe(map(result => result.data.removeUserFromContractor as User));
  }
}
