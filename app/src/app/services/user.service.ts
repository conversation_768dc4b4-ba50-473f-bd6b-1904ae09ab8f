import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Apollo } from 'apollo-angular';
import { gql } from 'apollo-angular';
import { User, UserConnection, UserInput } from '../graphql/generated';


@Injectable({
  providedIn: 'root'
})
export class UserService {
  constructor(private apollo: Apollo) { }

  getUser(username: string): Observable<User> {
    return this.apollo.watchQuery<any>({
      query: gql`
        query GetUser($username: String!) {
          getUser(username: $username) {
            username
            email
            managerGroups {
              id
              name
              groupType
            }
            operatorGroups {
              id
              name
              groupType
            }
            workerGroups {
              id
              name
              groupType
            }
            disciplines
            equipments
            contractors {
              id
              name
              contractorNumber
              description
            }
            createdAt
            updatedAt
          }
        }
      `,
      variables: { username }
    })
      .valueChanges
      .pipe(map(result => result.data.getUser as User));
  }

  listUsers(limit: number = 1000, nextToken?: string): Observable<UserConnection> {
    return this.apollo.watchQuery<any>({
      query: gql`
        query ListUsers($limit: Int, $nextToken: String) {
          listUsers(limit: $limit, nextToken: $nextToken) {
            items {
              username
              email
              managerGroups {
                id
                name
              }
              operatorGroups {
                id
                name
              }
              workerGroups {
                id
                name
              }
              disciplines
              equipments
              createdAt
              updatedAt
            }
            nextToken
            totalCount
          }
        }
      `,
      variables: { limit, nextToken }
    })
      .valueChanges
      .pipe(map(result => result.data.listUsers as UserConnection));
  }

  createUser(input: UserInput): Observable<User> {
    return this.apollo.mutate<any>({
      mutation: gql`
        mutation CreateUser($input: UserInput!) {
          createUser(input: $input) {
            username
            email
            createdAt
            updatedAt
          }
        }
      `,
      variables: { input }
    })
      .pipe(map(result => result.data.createUser as User));
  }

  deleteUser(username: string): Observable<string> {
    return this.apollo.mutate<any>({
      mutation: gql`
        mutation DeleteUser($username: String!) {
          deleteUser(username: $username)
        }
      `,
      variables: { username }
    })
      .pipe(map(result => result.data.deleteUser as string));
  }

  addUserToGroup(username: string, groupId: string): Observable<User> {
    return this.apollo.mutate<any>({
      mutation: gql`
        mutation AddUserToGroup($username: String!, $groupId: ID!) {
          addUserToGroup(username: $username, groupId: $groupId) {
            username
            managerGroups {
              id
              name
              groupType
            }
            operatorGroups {
              id
              name
              groupType
            }
            workerGroups {
              id
              name
              groupType
            }
          }
        }
      `,
      variables: { username, groupId }
    })
      .pipe(map(result => result.data.addUserToGroup as User));
  }

  removeUserFromGroup(username: string, groupId: string): Observable<User> {
    return this.apollo.mutate<any>({
      mutation: gql`
        mutation RemoveUserFromGroup($username: String!, $groupId: ID!) {
          removeUserFromGroup(username: $username, groupId: $groupId) {
            username
            managerGroups {
              id
              name
              groupType
            }
            operatorGroups {
              id
              name
              groupType
            }
            workerGroups {
              id
              name
              groupType
            }
          }
        }
      `,
      variables: { username, groupId }
    })
      .pipe(map(result => result.data.removeUserFromGroup as User));
  }

  updateUser(username: string, input: Partial<UserInput>): Observable<User> {
    return this.apollo.mutate<any>({
      mutation: gql`
        mutation UpdateUser($username: String!, $input: UserInput!) {
          updateUser(username: $username, input: $input) {
            username
            email
            disciplines
            equipments
            updatedAt
          }
        }
      `,
      variables: { username, input: { username, ...input } }
    })
      .pipe(map(result => result.data.updateUser as User));
  }
}
