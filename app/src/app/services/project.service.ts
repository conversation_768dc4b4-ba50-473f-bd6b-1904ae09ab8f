import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Activity, GetProjectGQL, GetProjectWithActivitiesGQL, ListProjectsGQL, ListProjectsShortGQL, ListProjectsWithActivitiesGQL, Project, ProjectConnection, ProjectInput, Status, UpdateProjectGQL } from '../graphql/generated';


@Injectable({
  providedIn: 'root'
})
export class ProjectService {
  constructor(private getProjectGQL: GetProjectGQL, private listProjectsGQL: ListProjectsGQL,
    private getProjectWithActivitiesGQL: GetProjectWithActivitiesGQL,
    private listProjectWithActivitiesGQL: ListProjectsWithActivitiesGQL,
    private updateProjectGQL: UpdateProjectGQL,
    private listProjectsShortGQL: ListProjectsShortGQL) { }

  getProject(id: string): Observable<Project> {
    return this.getProjectGQL.watch({ id })
      .valueChanges
      .pipe(map(result => result.data.getProject as Project));
  }

  updateProject(projectId: string, input: ProjectInput): Observable<Project> {
    return this.updateProjectGQL.mutate({ id: projectId, input })
      .pipe(map(result => result.data.updateProject as Project));
  }

  listProjects(limit: number, nextToken?: string): Observable<ProjectConnection> {
    return this.listProjectsGQL.watch({ limit, nextToken })
      .valueChanges
      .pipe(map(result => result.data.listProjects as ProjectConnection));
  }

  listProjectsShort(limit: number, nextToken?: string): Observable<ProjectConnection> {
    return this.listProjectsShortGQL.watch({ limit, nextToken })
      .valueChanges
      .pipe(map(result => result.data.listProjects as ProjectConnection));
  }

  getProjectWithActivities(
    id: string, 
    limit: number, 
    nextToken?: string,
    filters?: {
      status?: Status,
      unableToWork?: boolean,
      plannedStartDateFrom?: Date,
      plannedStartDateTo?: Date,
      plannedFinishDateFrom?: Date,
      plannedFinishDateTo?: Date
    }
  ): Observable<{ items: Activity[], nextToken: string | null, totalCount: number }> {
    // Convert dates to ISO strings if they exist - only include non-null values
    const variables = {
      id,
      limit,
      nextToken,
      ...(filters?.status !== null && filters?.status !== undefined && { status: filters.status }),
      ...(filters?.unableToWork !== null && filters?.unableToWork !== undefined && { unableToWork: filters.unableToWork }),
      ...(filters?.plannedStartDateFrom !== null && filters?.plannedStartDateFrom !== undefined && { 
        plannedStartDateFrom: filters.plannedStartDateFrom.toISOString() 
      }),
      ...(filters?.plannedStartDateTo !== null && filters?.plannedStartDateTo !== undefined && { 
        plannedStartDateTo: filters.plannedStartDateTo.toISOString() 
      }),
      ...(filters?.plannedFinishDateFrom !== null && filters?.plannedFinishDateFrom !== undefined && { 
        plannedFinishDateFrom: filters.plannedFinishDateFrom.toISOString() 
      }),
      ...(filters?.plannedFinishDateTo !== null && filters?.plannedFinishDateTo !== undefined && { 
        plannedFinishDateTo: filters.plannedFinishDateTo.toISOString() 
      })
    };

    return this.getProjectWithActivitiesGQL.watch(variables)
      .valueChanges
      .pipe(
        map(result => ({
          items: result.data.getProject.activities.items as Activity[],
          nextToken: result.data.getProject.activities.nextToken || null,
          totalCount: result.data.getProject.activities.totalCount
        }))
      );
  }

  listProjectWithActivities(limit: number, nextToken?: string): Observable<ProjectConnection> {
    return this.listProjectWithActivitiesGQL.watch({ limit, nextToken })
      .valueChanges
      .pipe(map(result => result.data.listProjects as ProjectConnection));
  }

  /**
   * Groups and counts the number of activities per status.
   * @param activities An array of activities.
   * @returns Record<Status, number> A record with statuses as keys and their corresponding counts as values.
   */
  private groupAndCountActivities(activities: Activity[]): Record<Status, number> {
    // Initialize counts for each possible status.
    const counts = Object.values(Status).reduce((acc, status) => {
      acc[status] = 0;
      return acc;
    }, {} as Record<Status, number>);

    // Increment the count for each activity's status.
    for (const activity of activities) {
      counts[activity.status] = (counts[activity.status] || 0) + 1;
    }
    return counts;
  }
}
