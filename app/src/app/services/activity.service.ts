import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { Activity, ActivityInput, GetActivityGQL, ListActivitiesGQL, UpdateActivityGQL, UpdateActivityInput, UpdateMultipleActivitiesGQL } from '../graphql/generated';

export interface ActivityError {
  type: 'ACCESS_DENIED' | 'GENERAL';
  message: string;
}

@Injectable({
  providedIn: 'root'
})
export class ActivityService {
  constructor(private getActivityGQL: GetActivityGQL, private updateActivityGQL: UpdateActivityGQL, private listActivitiesGQL: ListActivitiesGQL, private updateMultipleActivitiesGQL: UpdateMultipleActivitiesGQL) {}

  getActivity(id: string): Observable<Activity> {
    return this.getActivityGQL.fetch({ id })
      .pipe(
        map(result => result.data.getActivity as Activity),
        catchError(error => {
          // Check if this is an access denied error
          if (error.message && error.message.includes('Access denied: You do not have permission to access this activity')) {
            return throwError(() => ({
              type: 'ACCESS_DENIED',
              message: 'Access denied: You do not have permission to access this activity'
            } as ActivityError));
          }
          // For other errors, just pass them through
          return throwError(() => ({
            type: 'GENERAL',
            message: error.message || 'An error occurred while fetching the activity'
          } as ActivityError));
        })
      );
  }

  updateActivity(id: string, input: ActivityInput): Observable<Activity> {
    return this.updateActivityGQL.mutate({ id, input })
      .pipe(map(result => result.data.updateActivity as Activity));
  }

  updateMultipleActivities(input: UpdateActivityInput[]): Observable<Activity[]> {
    return this.updateMultipleActivitiesGQL.mutate({ input })
      .pipe(map(result => result.data.updateActivities as Activity[]));
  }

  listActivities(): Observable<Activity[]> {
    return this.listActivitiesGQL.fetch()
      .pipe(map(result => result.data.listActivities as Activity[]));
  }

}
