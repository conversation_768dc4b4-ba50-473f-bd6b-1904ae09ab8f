import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ActivityCodeMappingService {
  // Map of codeTypeName to translation key
  private codeTypeNameMap: Record<string, string> = {
    'ADIS': 'ACTIVITY_CODES.DISCIPLINE',
    'CTR': 'ACTIVITY_CODES.CONTRACTOR',
    'JOBORDERNO': 'ACTIVITY_CODES.JOBORDERNO',
    'NOTIFICATIONNR': 'ACTIVITY_CODES.NOTIFICATIONNR',
    'MPHA': 'ACTIVITY_CODES.MPHA',
    'SCOP': 'ACTIVITY_CODES.SCOP',
    'SCNR': 'ACTIVITY_CODES.SCNR',
    'OBS': 'ACTIVITY_CODES.OBS',
    'CBS': 'ACTIVITY_CODES.CBS',
    'SBS': 'ACTIVITY_CODES.SBS',
    'EQPT': 'ACTIVITY_CODES.EQUIPMENT',
    'SHCO': 'ACTIVITY_CODES.SHORTCODE'
  };

  /**
   * Get translation key for a given activity code type name
   * @param codeTypeName The code type name to get translation for
   * @returns The translation key or a fallback key based on the code type name
   */
  getTranslationKey(codeTypeName: string): string {
    // If we have a specific mapping, use it
    if (this.codeTypeNameMap[codeTypeName]) {
      return this.codeTypeNameMap[codeTypeName];
    }
    
    // Otherwise, create a generic key based on the code type name
    return `ACTIVITY_CODES.${codeTypeName}`;
  }

  /**
   * Add a new mapping to the code type name map
   * @param codeTypeName The code type name to add
   * @param translationKey The translation key to map to
   */
  addMapping(codeTypeName: string, translationKey: string): void {
    this.codeTypeNameMap[codeTypeName] = translationKey;
  }
}
