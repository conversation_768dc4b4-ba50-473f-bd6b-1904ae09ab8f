import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { Activity, Status, ReviewStatus } from 'src/app/graphql/generated';

@Component({
  selector: 'app-activity-relationships',
  templateUrl: './activity-relationships.component.html',
  styleUrls: ['./activity-relationships.component.css']
})
export class ActivityRelationshipsComponent {
  @Input() activity: Activity;

  constructor(private router: Router) { }

  /**
   * Navigate to the activity details page
   */
  viewActivity(activityId: string): void {
    this.router.navigate(['/activity', activityId]);
  }

  /**
   * Get CSS class based on activity status
   */
  getStatusClass(status: Status | null | undefined): string {
    if (!status) return '';
    
    switch (status) {
      case Status.Completed:
        return 'status-completed';
      case Status.InProgress:
        return 'status-in-progress';
      case Status.NotStarted:
        return 'status-not-started';
      default:
        return '';
    }
  }

  /**
   * Format percentage for display
   */
  formatPercentage(value: number | null | undefined): string {
    if (value === null || value === undefined) return 'N/A';
    return `${Math.round(value * 100)}%`;
  }
}
