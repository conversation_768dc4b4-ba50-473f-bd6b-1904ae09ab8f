<div class="relationships-container">
  <!-- Predecessor Activities Section -->
  <section class="relationship-section predecessors">
    <h3 class="section-title">
      <mat-icon>arrow_back</mat-icon>
      {{ 'ACTIVITY_RELATIONSHIPS.PREDECESSOR_ACTIVITIES' | translate }}
    </h3>
    
    <!-- Predecessor Activities Cards -->
    <div class="activity-cards" *ngIf="activity?.predecessorActivities && activity.predecessorActivities.length > 0">
      <mat-card class="activity-card" *ngFor="let pred of activity.predecessorActivities">
        <mat-card-header>
          <mat-card-title>
            {{ pred.name }}
          </mat-card-title>
          <mat-card-subtitle *ngIf="pred.activityId">
            {{ 'ACTIVITY_ID' | translate }}: {{ pred.activityId }}
          </mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <div class="activity-details">
            <div class="status-progress">
              <mat-chip-set>
                <mat-chip [ngClass]="getStatusClass(pred.status)">
                  {{ ('STATUS_VALUES.' + pred.status) | translate }}
                </mat-chip>
                <mat-chip *ngIf="pred.percentComplete !== null" class="progress-chip">
                  {{ formatPercentage(pred.percentComplete) }}
                </mat-chip>
              </mat-chip-set>
            </div>
            
            <div class="date-range" *ngIf="pred.plannedStartDate && pred.plannedFinishDate">
              <mat-icon class="small-icon">calendar_today</mat-icon>
              <span class="date-text">
                {{ pred.plannedStartDate | date:'shortDate' }} - {{ pred.plannedFinishDate | date:'shortDate' }}
              </span>
            </div>
          </div>
        </mat-card-content>
        
        <mat-card-actions>
          <button mat-button color="primary" (click)="viewActivity(pred.id)">
            <mat-icon>visibility</mat-icon>
            {{ 'ACTIVITY_RELATIONSHIPS.VIEW_ACTIVITY' | translate }}
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
    
    <!-- Legacy predecessor info removed as it's not supported in the current data model -->
    
    <!-- No predecessors message -->
    <div class="no-data-container" *ngIf="!activity?.predecessorActivities || activity.predecessorActivities.length === 0">
      <mat-icon class="no-data-icon">info</mat-icon>
      <p class="no-data-message">
        {{ 'ACTIVITY_RELATIONSHIPS.NO_PREDECESSOR_ACTIVITIES' | translate }}
      </p>
    </div>
  </section>

  <!-- Relationship Flow Visualization -->
  <div class="relationship-flow">
    <div class="flow-arrow">
      <mat-icon>arrow_downward</mat-icon>
    </div>
    <div class="current-activity-indicator">
      <div class="current-activity-dot"></div>
      <span class="current-activity-label">{{ 'ACTIVITY' | translate }}</span>
    </div>
    <div class="flow-arrow">
      <mat-icon>arrow_downward</mat-icon>
    </div>
  </div>

  <!-- Successor Activities Section -->
  <section class="relationship-section successors">
    <h3 class="section-title">
      <mat-icon>arrow_forward</mat-icon>
      {{ 'ACTIVITY_RELATIONSHIPS.SUCCESSOR_ACTIVITIES' | translate }}
    </h3>
    
    <!-- Successor Activities Cards -->
    <div class="activity-cards" *ngIf="activity?.successorActivities && activity.successorActivities.length > 0">
      <mat-card class="activity-card" *ngFor="let succ of activity.successorActivities">
        <mat-card-header>
          <mat-card-title>
            {{ succ.name }}
          </mat-card-title>
          <mat-card-subtitle *ngIf="succ.activityId">
            {{ 'ACTIVITY_ID' | translate }}: {{ succ.activityId }}
          </mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <div class="activity-details">
            <div class="status-progress">
              <mat-chip-set>
                <mat-chip [ngClass]="getStatusClass(succ.status)">
                  {{ ('STATUS_VALUES.' + succ.status) | translate }}
                </mat-chip>
                <mat-chip *ngIf="succ.percentComplete !== null" class="progress-chip">
                  {{ formatPercentage(succ.percentComplete) }}
                </mat-chip>
              </mat-chip-set>
            </div>
            
            <div class="date-range" *ngIf="succ.plannedStartDate && succ.plannedFinishDate">
              <mat-icon class="small-icon">calendar_today</mat-icon>
              <span class="date-text">
                {{ succ.plannedStartDate | date:'shortDate' }} - {{ succ.plannedFinishDate | date:'shortDate' }}
              </span>
            </div>
          </div>
        </mat-card-content>
        
        <mat-card-actions>
          <button mat-button color="primary" (click)="viewActivity(succ.id)">
            <mat-icon>visibility</mat-icon>
            {{ 'ACTIVITY_RELATIONSHIPS.VIEW_ACTIVITY' | translate }}
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
    
    <!-- Legacy successor info removed as it's not supported in the current data model -->
    
    <!-- No successors message -->
    <div class="no-data-container" *ngIf="!activity?.successorActivities || activity.successorActivities.length === 0">
      <mat-icon class="no-data-icon">info</mat-icon>
      <p class="no-data-message">
        {{ 'ACTIVITY_RELATIONSHIPS.NO_SUCCESSOR_ACTIVITIES' | translate }}
      </p>
    </div>
  </section>
</div>
