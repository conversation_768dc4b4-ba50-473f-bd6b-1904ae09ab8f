import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatChipsModule } from '@angular/material/chips';
import { ActivityRelationshipsComponent } from './activity-relationships.component';
import { Activity, Status } from 'src/app/graphql/generated';

describe('ActivityRelationshipsComponent', () => {
  let component: ActivityRelationshipsComponent;
  let fixture: ComponentFixture<ActivityRelationshipsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ActivityRelationshipsComponent],
      imports: [
        RouterTestingModule,
        TranslateModule.forRoot(),
        MatIconModule,
        MatListModule,
        MatChipsModule
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ActivityRelationshipsComponent);
    component = fixture.componentInstance;
    
    // Mock activity data
    component.activity = {
      id: '1',
      name: 'Test Activity',
      predecessorActivities: [],
      successorActivities: []
    } as Activity;
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should return correct status class', () => {
    expect(component.getStatusClass(Status.Completed)).toBe('status-completed');
    expect(component.getStatusClass(Status.InProgress)).toBe('status-in-progress');
    expect(component.getStatusClass(Status.NotStarted)).toBe('status-not-started');
    expect(component.getStatusClass(undefined)).toBe('');
  });

  it('should format percentage correctly', () => {
    expect(component.formatPercentage(0.75)).toBe('75%');
    expect(component.formatPercentage(0)).toBe('0%');
    expect(component.formatPercentage(null)).toBe('N/A');
    expect(component.formatPercentage(undefined)).toBe('N/A');
  });
});
