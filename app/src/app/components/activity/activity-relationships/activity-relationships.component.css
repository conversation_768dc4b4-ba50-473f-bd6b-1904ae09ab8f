.relationships-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem;
}

.relationship-section {
  border-radius: 8px;
  padding: 1rem;
  background-color: #f5f5f5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 18px;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
}

.activity-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.activity-card {
  transition: transform 0.2s, box-shadow 0.2s;
}

.activity-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.activity-link {
  color: #1976d2;
  cursor: pointer;
  text-decoration: none;
}

.activity-link:hover {
  text-decoration: underline;
}

.status-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.status-avatar.status-completed {
  background-color: #4caf50;
}

.status-avatar.status-in-progress {
  background-color: #2196f3;
}

.status-avatar.status-not-started {
  background-color: #9e9e9e;
}

.activity-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.date-range {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
  font-size: 0.9em;
}

.small-icon {
  font-size: 16px;
  height: 16px;
  width: 16px;
}

.relationship-flow {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 1rem 0;
}

.flow-arrow {
  
}

.current-activity-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0.5rem 0;
}

.current-activity-dot {
  width: 12px;
  height: 12px;
  background-color: black;
  border-radius: 50%;
}

.current-activity-label {
  font-weight: 500;
}

.no-data-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.no-data-icon {
  color: #9e9e9e;
}

.no-data-message {
  color: #666;
  margin: 0;
  font-style: italic;
}

.legacy-card {
  background-color: #fff3e0;
}

.legacy-avatar {
  background-color: #ff9800;
  display: flex;
  align-items: center;
  justify-content: center;
}

.legacy-avatar mat-icon {
  color: white;
}

.legacy-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.legacy-id {
  font-size: 0.9em;
  color: #666;
}

/* Status colors - keep existing ones */
.status-completed {
  background-color: #4caf50 !important;
  color: white !important;
}

.status-in-progress {
  background-color: #2196f3 !important;
  color: white !important;
}

.status-not-started {
  background-color: #9e9e9e !important;
  color: white !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .activity-cards {
    grid-template-columns: 1fr;
  }
  
  .relationships-container {
    padding: 0.5rem;
  }
  
  .relationship-section {
    padding: 0.75rem;
  }
}
