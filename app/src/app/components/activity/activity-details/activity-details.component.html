<div *ngIf="error && error.type === 'ACCESS_DENIED'" class="access-denied-container">
  <mat-card class="access-denied-card">
    <mat-card-header>
      <mat-icon mat-card-avatar color="warn">lock</mat-icon>
      <mat-card-title>{{ 'ACCESS_DENIED' | translate }}</mat-card-title>
      <mat-card-subtitle>{{ 'PERMISSION_ERROR' | translate }}</mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <p>{{ error.message }}</p>
      <p>{{ 'ACCESS_DENIED_EXPLANATION' | translate }}</p>
      <ul>
        <li>{{ 'WORKER_PROJECT_ASSIGNMENT' | translate }}</li>
        <li>{{ 'ACTIVITY_CONTRACTOR_ASSIGNMENT' | translate }}</li>
        <li>{{ 'WORKER_DISCIPLINE_EQUIPMENT_CLEARANCE' | translate }}</li>
      </ul>
      <p>
        <a mat-button color="primary" href="/help/activity-access" target="_blank">
          <mat-icon>help</mat-icon>
          {{ 'LEARN_MORE' | translate }}
        </a>
      </p>
    </mat-card-content>
  </mat-card>
</div>

<div *ngIf="!error" class="details-container">
  <div class="info-grid">
    <!-- Description Section -->
    <div class="info-section description-section">
      <h3>{{ 'DESCRIPTION' | translate }}</h3>
      <p>{{ activity.description || ('NO_DESCRIPTION' | translate) }}</p>
    </div>

    <!-- Effort and Resources Section -->
    <div class="info-section resources-section">
      <h3>{{ 'EFFORT_AND_RESOURCES' | translate }}</h3>
      
      <div class="effort-info">
        <p><strong>{{ 'PLANNED_EFFORT' | translate }}:</strong> {{ activity.plannedEffort || activity.plannedDuration || ('NOT_SPECIFIED' | translate) }} {{ 'HOURS' | translate }}</p>
        <p *ngIf="activity.actualEffort"><strong>{{ 'ACTUAL_EFFORT' | translate }}:</strong> {{ activity.actualEffort }} {{ 'HOURS' | translate }}</p>
      </div>
      
      <div *ngIf="activity.resourceId || activity.resourceName || activity.resourceObjectId">
        <h4>{{ 'REQUIRED_RESOURCES' | translate }}</h4>
        <p *ngIf="activity.resourceId"><strong>{{ 'RESOURCE_ID' | translate }}:</strong> {{ activity.resourceId }}</p>
        <p *ngIf="activity.resourceName"><strong>{{ 'RESOURCE_NAME' | translate }}:</strong> {{ activity.resourceName }}</p>
        <p *ngIf="activity.resourceObjectId"><strong>{{ 'RESOURCE_OBJECT_ID' | translate }}:</strong> {{ activity.resourceObjectId }}</p>
      </div>
      
    </div>

    <!-- Schedule Section -->
    <div class="info-section dates-section">
      <h3>{{ 'SCHEDULE' | translate }}</h3>
      <div class="date-info">
        <h4 *ngIf="activity?.plannedStartDate && activity?.plannedFinishDate">{{ 'PLANNED_DATES' | translate }}</h4>
        <p *ngIf="activity?.plannedStartDate && activity?.plannedFinishDate">{{ activity.plannedStartDate | date:'short' }} - {{ activity.plannedFinishDate | date:'short' }}</p>
        
        <h4 *ngIf="activity?.actualStartDate && activity?.actualFinishDate">{{ 'ACTUAL_DATES' | translate }}</h4>
        <p *ngIf="activity?.actualStartDate && activity?.actualFinishDate">{{ activity.actualStartDate | date:'short' }} - {{ activity.actualFinishDate | date:'short' }}</p>
        
        <h4 *ngIf="activity?.baselineStartDate && activity?.baselineFinishDate">{{ 'BASELINE_DATES' | translate }}</h4>
        <p *ngIf="activity?.baselineStartDate && activity?.baselineFinishDate">{{ activity.baselineStartDate | date:'short' }} - {{ activity.baselineFinishDate | date:'short' }}</p>
      </div>
    </div>

    <!-- Project Context Section -->
    <div class="info-section project-context-section">
      <h3>{{ 'PROJECT_CONTEXT' | translate }}</h3>
      <p *ngIf="activity.projectName"><strong>{{ 'PROJECT_NAME' | translate }}:</strong> {{ activity.projectName }}</p>
      <p *ngIf="activity.projectId"><strong>{{ 'PROJECT_ID' | translate }}:</strong> {{ activity.projectId }}</p>
      <p *ngIf="activity.sequenceNo"><strong>{{ 'SEQUENCE_NUMBER' | translate }}:</strong> {{ activity.sequenceNo }}</p>
      
    </div>

    <!-- Activity Codes Section -->
    <div class="info-section activity-codes-section">
      <h3>{{ 'ACTIVITY_CODES.TITLE' | translate }}</h3>
      <div *ngIf="activity.activityCodes && activity.activityCodes.length > 0">
        <mat-list>
          <mat-list-item *ngFor="let code of activity.activityCodes">
            <div matListItemTitle>{{ getActivityCodeTranslation(code.codeTypeName) | translate }}</div>
            <div matListItemLine>{{ code.codeTypeValue }}</div>
            <div matListItemLine *ngIf="code.codeTypeDescription">{{ code.codeTypeDescription }}</div>
            <mat-divider></mat-divider>
          </mat-list-item>
        </mat-list>
      </div>
      <p *ngIf="!activity.activityCodes || activity.activityCodes.length === 0">{{ 'NO_ACTIVITY_CODES' | translate }}</p>
    </div>

    <!-- Additional Info Accordion -->
    <mat-accordion class="additional-info-accordion">
      <mat-expansion-panel>
        <mat-expansion-panel-header>
          <mat-panel-title>
            {{ 'ADDITIONAL_INFO' | translate }}
          </mat-panel-title>
        </mat-expansion-panel-header>

        <div class="additional-info-content">
          <p><strong>{{ 'ACTIVITY_ID' | translate }}:</strong> {{ activity.id }}</p>
          <p *ngIf="activity.activityId"><strong>{{ 'EXTERNAL_ACTIVITY_ID' | translate }}:</strong> {{ activity.activityId }}</p>
          <p *ngIf="activity.allowedGroups && activity.allowedGroups.length > 0">
            <strong>{{ 'ALLOWED_GROUPS' | translate }}:</strong> {{ activity.allowedGroups.join(', ') }}
          </p>
        </div>
      </mat-expansion-panel>
    </mat-accordion>

  </div>
</div>
