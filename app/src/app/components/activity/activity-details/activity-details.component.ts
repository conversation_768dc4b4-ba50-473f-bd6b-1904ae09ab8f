// activity-details.component.ts
import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { Activity, ReviewStatus, Status } from 'src/app/graphql/generated';
import { ActivityCodeMappingService } from 'src/app/services/activity-code-mapping.service';
import { ActivityError } from 'src/app/services/activity.service';

@Component({
  selector: 'app-activity-details',
  templateUrl: './activity-details.component.html',
  styleUrls: ['./activity-details.component.css']
})
export class ActivityDetailsComponent {
  @Input() activity: Activity;
  @Input() error: ActivityError | null = null;

  constructor(
    private router: Router,
    private activityCodeMappingService: ActivityCodeMappingService
  ) { }

  /**
   * Get translation key for activity code type name
   */
  getActivityCodeTranslation(codeTypeName: string): string {
    return this.activityCodeMappingService.getTranslationKey(codeTypeName);
  }

  // Status-related methods removed as part of the rework
}
