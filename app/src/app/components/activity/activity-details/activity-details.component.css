.access-denied-container {
  display: flex;
  justify-content: center;
  padding: 2rem;
}

.access-denied-card {
  max-width: 600px;
  width: 100%;
  margin-bottom: 2rem;
  border-left: 4px solid #f44336;
}

.access-denied-card mat-card-header {
  margin-bottom: 1rem;
}

.access-denied-card mat-card-content {
  padding: 0 1rem 1rem 1rem;
}

.access-denied-card ul {
  margin-left: 1.5rem;
  margin-bottom: 1.5rem;
}

.access-denied-card li {
  margin-bottom: 0.5rem;
}

.access-denied-card a {
  display: inline-flex;
  align-items: center;
}

.access-denied-card mat-icon {
  margin-right: 0.5rem;
}

.details-container {
  padding: 24px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.info-section {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.info-section h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.87);
  font-size: 18px;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
}

.info-section h4 {
  font-size: 16px;
  font-weight: 500;
  margin: 16px 0 8px;
  color: rgba(0, 0, 0, 0.75);
}

.info-section h3 mat-icon {
  color: #1976d2;
}

/* Description section styles */
.description-section {
  grid-column: 1 / -1;
}

.description-section p {
  line-height: 1.6;
  color: rgba(0, 0, 0, 0.87);
}

/* Resources section styles */
.resources-section mat-list-item {
  height: auto;
  margin-bottom: 8px;
}

.effort-info {
  margin-bottom: 16px;
}

.primary-resource-info {
  margin-top: 16px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border-left: 3px solid #2196f3;
}

.primary-resource-info h4 {
  color: #2196f3;
  margin-top: 0;
}

.primary-resource-info p {
  margin-bottom: 8px;
}

/* Dates section styles */
.dates-section .date-info p {
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.87);
}

.dates-section .date-info p strong {
  color: rgba(0, 0, 0, 0.6);
  margin-right: 8px;
}

.dates-section .date-info h4 {
  margin-top: 16px;
  margin-bottom: 8px;
}

/* Equipment and scope section */
.equipment-section h4 {
  margin-top: 16px;
}

/* Project context section */
.project-context-section p {
  margin-bottom: 8px;
}

/* Job information section */
.job-info-section p {
  margin-bottom: 8px;
}

/* Contractor section */
.contractor-section p {
  margin-bottom: 8px;
}

.contractor-section strong {
  font-weight: 500;
}

/* Activity codes section */
.activity-codes-section mat-list-item {
  height: auto;
  margin-bottom: 8px;
}

.activity-codes-section mat-list {
  padding-top: 0;
}

.activity-codes-section [matListItemTitle] {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
}

.activity-codes-section [matListItemLine] {
  color: rgba(0, 0, 0, 0.6);
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
}

/* Action buttons */
.action-buttons {
  display: flex;
  gap: 16px;
  margin-top: 24px;
}

/* Additional info accordion */
.additional-info-accordion {
  margin-top: 24px;
  grid-column: 1 / -1;
}

.additional-info-content p {
  margin-bottom: 8px;
}

/* Common status styles */
.common-status {
  font-weight: 500;
}

@media (max-width: 768px) {
  .details-container {
    padding: 16px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .info-section {
    padding: 12px;
  }
  
  .info-section h3 {
    font-size: 16px;
  }
  
  .info-section h4 {
    font-size: 14px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .details-container {
    padding: 12px;
  }
  
  .info-grid {
    gap: 12px;
  }
  
  .info-section {
    padding: 10px;
  }
  
  .access-denied-card {
    margin-bottom: 1rem;
  }
  
  .access-denied-container {
    padding: 1rem;
  }
}
