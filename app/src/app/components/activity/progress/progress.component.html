<div class="progress-container">
  <div class="progress-section">
    <h3>
      {{ 'PROGRESS_TRACKING' | translate }}
    </h3>
    
    <div class="progress-bar-container">
      <mat-progress-bar 
        mode="determinate" 
        [value]="activity.percentComplete * 100"
        [color]="getProgressColor()">
      </mat-progress-bar>
      <span class="progress-label">{{ (activity.percentComplete * 100) | number:'1.0-0' }}% {{ 'COMPLETE' | translate }}</span>
    </div>

    <div *ngIf="isProgressVisible()" class="progress-buttons">
      <button mat-stroked-button
              *ngFor="let value of progressValues"
              (click)="setProgress(value / 100)"
              [class.active]="activity.percentComplete === (value / 100)"
              [disabled]="activity.unableToWork">
        {{ value }}%
      </button>
    
      <button mat-stroked-button
              (click)="setCustomProgress()"
              [disabled]="activity.unableToWork">
        {{ 'CUSTOM' | translate }}
      </button>
    </div>
  </div>

  <div *ngIf="isProgressVisible()" class="action-buttons">
    <mat-slide-toggle
      color="accent"
      [checked]="activity.unableToWork"
      (change)="onUnableToWorkToggle($event)">
      {{ 'UNABLE_TO_WORK' | translate }}
    </mat-slide-toggle>
  </div>

  <mat-divider></mat-divider>

  <div class="comments-section">
    <h3>
      {{ 'COMMENTS' | translate }}
    </h3>
    
    <mat-form-field appearance="outline" class="comment-input">
      <mat-label>{{ 'ADD_COMMENT' | translate }}</mat-label>
      <textarea matInput 
                [(ngModel)]="newComment" 
                rows="3" 
                placeholder="{{ 'COMMENT_PLACEHOLDER' | translate }}">
      </textarea>
    </mat-form-field>
    
    <div class="comment-actions">
      <button mat-raised-button 
              color="primary" 
              [disabled]="!newComment.trim()"
              (click)="addComment()">
        {{ 'POST_COMMENT' | translate }}
      </button>
    </div>

    <div class="comments-list">
      <mat-card *ngFor="let comment of activity.comments" class="comment-card">
        <mat-card-header>
          <mat-icon mat-card-avatar>account_circle</mat-icon>
          <mat-card-title>{{ comment.author }}</mat-card-title>
          <mat-card-subtitle>{{ comment.createdAt | date:'medium' }}</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <p>{{ comment.text }}</p>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
