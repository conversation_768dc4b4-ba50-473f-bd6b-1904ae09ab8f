// progress.component.ts
import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatSlideToggleChange } from '@angular/material/slide-toggle';
import { AuthUser } from 'aws-amplify/auth';
import { ConfirmationDialogComponent } from 'src/app/core/components/confirmation-dialog/confirmation-dialog.component';
import { AuthService } from 'src/app/core/services/auth.service';
import { Activity, ActivityInput, Status } from 'src/app/graphql/generated';
import { USER_GROUPS } from 'src/app/shared/constant.common';

@Component({
  selector: 'app-progress',
  templateUrl: './progress.component.html',
  styleUrls: ['./progress.component.css']
})
export class ProgressComponent implements OnInit {
  @Input() activity: Activity;
  @Output() activityChange = new EventEmitter<ActivityInput>();

  user: AuthUser | null = null;

  newComment: string = '';
  progressValues: number[] = [0, 25, 50, 75, 100];

  constructor(public authService: AuthService, private dialog: MatDialog) {}

  async ngOnInit(): Promise<void> {
    this.user = await this.authService.getUser();
  }

  setProgress(value: number): void {
    // If user sets progress to 100, prompt for confirmation
    if (value === 1) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        width: '400px',
        data: {
          title: 'Confirm Completion',
          message: 'Are you sure you want to set the activity progress to 100% and mark it as completed?',
          confirmText: 'Yes, complete it',
          cancelText: 'Cancel'
        }
      });

      dialogRef.afterClosed().subscribe((confirmed: boolean) => {
        if (confirmed) {
          // If confirmed, mark progress = 100 and status = Completed
          this.activity.percentComplete = 1;
          this.activity.status = Status.Completed;

          const activityInput: ActivityInput = {
            percentComplete: this.activity.percentComplete,
            status: Status.Completed,
            unableToWork: false
          };
          this.activityChange.emit(activityInput);
        } else {
          // If user cancels, do nothing (or revert to previous progress if desired)
          console.log('User canceled marking as complete.');
        }
      });
    } else if (this.activity.percentComplete === 0 && value > 0 && value < 1) {
      // If progress is changing from 0% to a value between 0% and 100%
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        width: '400px',
        data: {
          title: 'Activity Status Change',
          message: 'The activity will be set to "In progress".',
          confirmText: 'OK',
          cancelText: 'Cancel'
        }
      });

      dialogRef.afterClosed().subscribe((confirmed: boolean) => {
        if (confirmed) {
          // Update progress and set status to In Progress
          this.activity.percentComplete = value;
          this.activity.status = Status.InProgress;

          const activityInput: ActivityInput = {
            percentComplete: this.activity.percentComplete,
            status: Status.InProgress
          };
          this.activityChange.emit(activityInput);
          console.log('Progress set to:', value, 'and status set to In Progress');
        } else {
          // If user cancels, do nothing
          console.log('User canceled setting progress.');
        }
      });
    } else {
      // Normal logic if progress < 1
      this.activity.percentComplete = value;
      const activityInput: ActivityInput = {
        percentComplete: this.activity.percentComplete
      };
      this.activityChange.emit(activityInput);
      console.log('Progress set to:', value);
    }
  }

  setCustomProgress(): void {
    const progress = prompt('Enter progress value (0-100):');
    if (progress !== null) {
      const value = parseInt(progress);
      if (!isNaN(value) && value >= 0 && value <= 100) {
        this.setProgress(value / 100);
      }
    }
  }

  getProgressColor(): string {
    if (this.activity.percentComplete >= .75) return 'primary';
    if (this.activity.percentComplete >= .50) return 'accent';
    return 'warn';
  }

  addComment(): void {
    if (this.newComment.trim()) {

      // Ensure comments is initialized as an empty array if null
      this.activity.comments = this.activity.comments || [];

      this.activity.comments.unshift({
        author: this.user.username,
        text: this.newComment,
        createdAt: new Date().toISOString()
      });
      this.newComment = '';

      // Avoid copying __typename to activityInput
      const activityInput: ActivityInput = {
        comments: this.activity.comments.map(({ __typename, ...comment }) => comment)
      };
      this.activityChange.emit(activityInput);
    }
  }

  onUnableToWorkToggle(event: MatSlideToggleChange): void {

    if (event.checked) {
      // e.g., call the same logic you used in `reportIssue()`, or other relevant actions
      // this.reportIssue();
      this.activity.unableToWork = true;
      console.log(this.activity.status)
    } else {
      // handle if user toggles back to "able to work"
      this.activity.unableToWork = false;
      console.log('User is now able to work again.');
    }
    this.activityChange.emit({ unableToWork: this.activity.unableToWork });
  }

  isProgressVisible(): Boolean {
    // return this.authService.hasUserGroup(USER_GROUPS.ADMIN) ||
    //  this.authService.hasAllUserGroups([USER_GROUPS.EXECUTOR, `Company_${this.activity.companyCode}`, `Discipline_${this.activity.disciplineCode}`])
    return true;
  }
}
