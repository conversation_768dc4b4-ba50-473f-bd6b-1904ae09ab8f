/* progress.component.css */
.progress-container {
    padding: 24px;
  }
  
  .progress-section {
    margin-bottom: 32px;
  }
  
  .progress-section h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    color: rgba(0, 0, 0, 0.87);
    font-size: 18px;
    font-weight: 500;
  }
  
  .progress-section h3 mat-icon {
    color: #1976d2;
  }
  
  .progress-bar-container {
    margin: 24px 0;
  }
  
  .progress-bar-container mat-progress-bar {
    height: 8px;
    border-radius: 4px;
  }
  
  .progress-label {
    display: block;
    margin-top: 8px;
    color: rgba(0, 0, 0, 0.6);
  }
  
  .progress-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 16px;
  }
  
  .progress-buttons button {
    min-width: 80px;
  }
  
  .progress-buttons button.active {
    background-color: #e3f2fd;
  }
  
  .comments-section {
    margin-top: 32px;
  }
  
  .comments-section h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    color: rgba(0, 0, 0, 0.87);
    font-size: 18px;
    font-weight: 500;
  }
  
  .comment-input {
    width: 100%;
    margin-bottom: 16px;
  }
  
  .comment-actions {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 24px;
  }
  
  .comments-list {
    margin-top: 16px;
  }
  
  .comment-card {
    margin-bottom: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .comment-card mat-card-header {
    margin-bottom: 8px;
  }
  
  .comment-card mat-card-content {
    padding: 0 16px 16px;
  }
  
  .action-buttons {
    display: flex;
    gap: 16px;
    margin-top: 24px;
  }
  
  @media (max-width: 600px) {
    .progress-container {
      padding: 16px;
    }
  
    .progress-buttons button {
      flex: 1;
    }
  }
  