import { Component, Input, OnInit, On<PERSON><PERSON>roy, SimpleChanges, OnChanges } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subject, switchMap, takeUntil } from 'rxjs';
import { MessageService } from 'src/app/core/services/message.service';
import { LoadingService } from 'src/app/core/services/loading.service';
import { Activity, ActivityInput } from 'src/app/graphql/generated';
import { ActivityService, ActivityError } from 'src/app/services/activity.service';

@Component({
  selector: 'app-activity',
  templateUrl: './activity.component.html',
  styleUrls: ['./activity.component.css']
})
export class ActivityComponent implements OnInit, OnDestroy, OnChanges {
  @Input() id: string;

  activity: Activity;
  loading = true;
  error: ActivityError | null = null;

  /** Used to clean up observables on destroy */
  private destroy$ = new Subject<void>();

  constructor(
    private activityService: ActivityService,
    private messageService: MessageService,
    private route: ActivatedRoute,
    private loadingService: LoadingService
  ) {}

  ngOnInit(): void {
    // 1) Subscribe to route param changes
    this.route.params
      .pipe(
        switchMap((params) => {
          // If the route param changes, we fetch again
          const routeId = params['id'];
          // Only fetch if an ID is present
          if (routeId) {
            this.id = routeId;
            this.loading = true;
            return this.activityService.getActivity(routeId);
          }
          // If no ID present, return an empty observable (or do nothing)
          return [];
        }),
        takeUntil(this.destroy$)
      )
      .subscribe({
        next: (data: Activity) => {
          this.activity = data;
          this.loading = false;
          this.loadingService.hide();
        },
        error: (err: ActivityError) => {
          this.error = err;
          this.loading = false;
          this.loadingService.hide();
          
          // Show error message to user
          if (err.type !== 'ACCESS_DENIED') {
            this.messageService.showError('Failed to load Activity');
          }
          console.error(err);
        }
      });

    // 2) If an @Input() id is provided initially (without route usage)
    if (this.id) {
      this.fetchActivity();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['id'] && !changes['id'].firstChange) {
      const newId = changes['id'].currentValue;
      if (newId) {
        this.fetchActivity(); // fetch new ID
      }
    }
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.destroy$.next();
    this.destroy$.complete();
  }

  /** Fallback if an @Input() id is set without route params */
  private fetchActivity(): void {
    if (!this.id) {
      return;
    }
    this.loading = true;
    this.loadingService.show('activity');
    this.activityService.getActivity(this.id).subscribe({
      next: (data) => {
        this.activity = data;
        this.loading = false;
        this.loadingService.hide();
      },
      error: (err: ActivityError) => {
        this.error = err;
        this.loading = false;
        this.loadingService.hide();
        
        // Show error message to user
        if (err.type !== 'ACCESS_DENIED') {
          this.messageService.showError('Failed to load Activity');
        }
        console.error(err);
      }
    });
  }

  /** Called when activity form or data is updated externally */
  onActivityChange(updatedActivity: ActivityInput): void {
    console.log('Activity updated:', updatedActivity);
    if (!this.activity || !this.activity.id) {
      return;
    }
    this.updateActivity(this.activity.id, updatedActivity);
  }

  /** Updates the activity data on the server */
  private updateActivity(id: string, activity: ActivityInput): void {
    // Show loading indicator for updates
    this.loadingService.show('activity-update');
    this.activityService.updateActivity(id, activity).subscribe({
      next: (data) => {
        this.activity = data;  // Optionally store updated data
        this.loadingService.hide();
      },
      error: (err) => {
        this.error = {
          type: 'GENERAL',
          message: 'Failed to update Activity'
        };
        this.loading = false;
        this.loadingService.hide();
        
        // Show error message to user
        this.messageService.showError('Failed to update Activity');
        console.error(err);
      }
    });
  }
}
