.activity-log-container {
  padding: 24px;
}

/* Sync Status Styles */
.sync-status-container {
  margin-bottom: 20px;
}

.sync-status-card {
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sync-status-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.sync-status-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.sync-status-text {
  font-weight: 500;
  font-size: 16px;
}

.sync-status-details {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.7);
}

/* Status-specific styles */
.sync-status-success {
  background-color: rgba(76, 175, 80, 0.1);
  border-left: 4px solid #4CAF50;
}

.sync-status-success .sync-status-indicator {
  background-color: #4CAF50;
}

.sync-status-failed {
  background-color: rgba(244, 67, 54, 0.1);
  border-left: 4px solid #F44336;
}

.sync-status-failed .sync-status-indicator {
  background-color: #F44336;
}

.sync-status-unknown {
  background-color: rgba(255, 152, 0, 0.1);
  border-left: 4px solid #FF9800;
}

.sync-status-unknown .sync-status-indicator {
  background-color: #FF9800;
}

/* Log Events Styles */
.log-events-container {
  margin-bottom: 20px;
  max-height: 300px;
  overflow-y: auto;
  /* Custom scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
}

/* For Webkit browsers (Chrome, Safari) */
.log-events-container::-webkit-scrollbar {
  width: 6px;
}

.log-events-container::-webkit-scrollbar-track {
  background: transparent;
}

.log-events-container::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

.log-event-card {
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-color: rgba(33, 150, 243, 0.05);
  border-left: 4px solid #2196F3;
}

.log-event-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.log-event-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 8px;
  background-color: #2196F3;
}

.log-event-title {
  font-weight: 500;
  font-size: 16px;
}

.log-event-details {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.7);
}
