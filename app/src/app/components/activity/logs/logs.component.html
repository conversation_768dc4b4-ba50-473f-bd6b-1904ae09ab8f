<div class="activity-log-container">
  <h3>
    {{ 'ACTIVITY_LOGS.TITLE' | translate }}
  </h3>

  <!-- Sync Status Section -->
  <div class="sync-status-container">
    <h4>{{ 'ACTIVITY_LOGS.SYNC_STATUS' | translate }}</h4>
    <div *ngIf="activity?.syncStatus; else noSyncStatus" class="sync-status-card" [ngClass]="getSyncStatusClass()">
      <div class="sync-status-header">
        <span class="sync-status-indicator"></span>
        <span class="sync-status-text">{{ activity.syncStatus.syncStatus }}</span>
      </div>
      <div class="sync-status-details">
        <div>{{ 'ACTIVITY_LOGS.TARGET_SYSTEM' | translate }}: {{ activity.syncStatus.targetSystem }}</div>
        <div>{{ 'ACTIVITY_LOGS.LAST_SYNCED' | translate }}: {{ activity.syncStatus.lastSyncedAt | date:'medium' }}</div>
      </div>
    </div>
    
    <ng-template #noSyncStatus>
      <div class="sync-status-card sync-status-unknown">
        <div class="sync-status-header">
          <span class="sync-status-indicator"></span>
          <span class="sync-status-text">{{ 'ACTIVITY_LOGS.NO_SYNC_STATUS' | translate }}</span>
        </div>
      </div>
    </ng-template>
  </div>

  <h4>{{ 'ACTIVITY_LOGS.LOG_EVENTS' | translate }}</h4>
  <div *ngIf="activity?.logEvents?.length > 0; else noLogs" class="log-events-container">
    <div *ngFor="let log of getSortedLogEvents()" class="log-event-card">
      <div class="log-event-header">
        <span class="log-event-indicator"></span>
        <span class="log-event-title">{{ log.event }}</span>
      </div>
      <div class="log-event-details">
        {{ 'ACTIVITY_LOGS.CHANGED_BY' | translate }}: {{ log.changedBy }} - {{ log.timestamp | date:'medium' }}
      </div>
    </div>
  </div>
  <ng-template #noLogs>
    <p>{{ 'ACTIVITY_LOGS.NO_LOGS_AVAILABLE' | translate }}</p>
  </ng-template>
</div>
