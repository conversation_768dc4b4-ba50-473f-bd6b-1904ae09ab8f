import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { Activity, GetActivityWithSyncGQL } from 'src/app/graphql/generated';

@Component({
  selector: 'app-logs',
  templateUrl: './logs.component.html',
  styleUrl: './logs.component.css'
})
export class LogsComponent implements OnChanges {
  @Input() activity: Activity;

  constructor(private getActivityWithSyncGQL: GetActivityWithSyncGQL) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['activity'] && this.activity?.id) {
      this.loadActivityWithSync();
    }
  }

  /**
   * Load activity with sync status
   */
  loadActivityWithSync(): void {
    this.getActivityWithSyncGQL.fetch({ id: this.activity.id })
      .subscribe(({ data }) => {
        if (data?.getActivity) {
          // Update only the syncStatus property to avoid overwriting other properties
          this.activity = {
            ...this.activity,
            syncStatus: data.getActivity.syncStatus
          };
        }
      });
  }

  /**
   * Returns the appropriate CSS class based on the sync status
   * @returns string CSS class name
   */
  getSyncStatusClass(): string {
    if (!this.activity?.syncStatus) {
      return '';
    }

    switch (this.activity.syncStatus.syncStatus) {
      case 'SUCCEEDED':
        return 'sync-status-success';
      case 'FAILED':
        return 'sync-status-failed';
      default:
        return 'sync-status-unknown';
    }
  }

  /**
   * Returns log events sorted by timestamp (newest first)
   * @returns sorted log events array
   */
  getSortedLogEvents(): any[] {
    if (!this.activity?.logEvents?.length) {
      return [];
    }
    
    return [...this.activity.logEvents].sort((a, b) => {
      return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
    });
  }
}
