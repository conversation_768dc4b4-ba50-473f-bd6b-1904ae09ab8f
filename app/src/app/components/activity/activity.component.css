/* activity.component.css */

.header-content {
 
}

.id-status-container {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.main-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
}

.identifiers {
  display: flex;
  align-items: center;
  gap: 24px;
  color: rgba(0, 0, 0, 0.6);
}

.activity-id {
  display: flex;
  align-items: center;
  gap: 4px;
}

.activity-id mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.work-order-link {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #1976d2;
  padding: 0;
}

.work-order-link mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.status-section {
  margin-left: 16px;
  display: flex;
  align-items: center;
}

.work-order-info {
  background-color: #f8f9fa;
  margin-bottom: 16px;
}

.work-order-info h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
  margin-bottom: 8px;
}

.work-order-info .wo-name {
  margin: 0;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.87);
  margin-bottom: 4px;
}

.work-order-info .wo-type {
  margin: 0;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
}

mat-divider {
  margin: 0 24px;
}

@media (max-width: 768px) {
  .activity-container {
    padding: 16px;
  }

  .header-content {
    padding: 16px;
  }

  .id-status-container {
    flex-direction: row;
    align-items: flex-start;
    gap: 8px;
  }

  .main-info {
    flex-direction: column;
    gap: 16px;
  }

  .identifiers {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .status-section {
    margin-left: 0;
    margin-top: 8px;
  }
}
