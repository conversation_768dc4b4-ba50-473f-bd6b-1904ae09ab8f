import { 
  Component,
  OnInit,
  ViewChild,
  ElementRef,
  AfterViewInit,
  Input,
  Output,
  EventEmitter,
  SimpleChanges,
  OnChanges
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { lastValueFrom } from 'rxjs';
import { ConfirmationDialogComponent } from 'src/app/core/components/confirmation-dialog/confirmation-dialog.component';
import { AuthService } from 'src/app/core/services/auth.service';
import { Activity, ActivityInput, Attachment } from 'src/app/graphql/generated';
import { FileService } from 'src/app/services/file.service';
import { USER_GROUPS } from 'src/app/shared/constant.common';

@Component({
  selector: 'app-photo-list-and-capture',
  templateUrl: './photo-list-and-capture.component.html',
  styleUrls: ['./photo-list-and-capture.component.css']
})
export class PhotoListAndCaptureComponent implements OnInit, AfterViewInit, OnChanges {
  @ViewChild('videoElement') videoElement!: ElementRef<HTMLVideoElement>;
  @ViewChild('canvasElement') canvasElement!: ElementRef<HTMLCanvasElement>;
  @Input() activity: Activity;
  @Output() activityChange = new EventEmitter<ActivityInput>();

  // Camera capture state
  isCameraOpen = false;     // Controls the expansion panel
  isPhotoTaken = false;
  photoDataUrl: string | null = null;
  file: File;
  description = '';

  stream: MediaStream | undefined;

  constructor(private fileService: FileService, private dialog: MatDialog, private authService: AuthService) {}

  ngOnInit(): void {
    this.ensureEvidencesArray();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['activity'] && this.activity) {
      this.ensureEvidencesArray();
    }
  }

  ngAfterViewInit(): void {
    // We'll start the camera only when the panel is opened
  }

  /**
   * Makes sure `this.activity.evidences` is always an array.
   */
  private ensureEvidencesArray(): void {
    if (!this.activity.evidences) {
      this.activity.evidences = [];
    }
  }

  /**
   * Called when the expansion panel is opened. We can start the camera here.
   */
  async onCameraPanelOpened(): Promise<void> {
    if (!this.stream) {
      await this.startCamera();
    }
  }

  async startCamera(): Promise<void> {
    try {
      this.stream = await navigator.mediaDevices.getUserMedia({ video: true });
      if (this.videoElement?.nativeElement) {
        this.videoElement.nativeElement.srcObject = this.stream;
        this.videoElement.nativeElement.play();
      }
    } catch (err) {
      console.error('Error accessing camera: ', err);
    }
  }

  takePhoto(): void {
    if (!this.canvasElement?.nativeElement || !this.videoElement?.nativeElement) return;
    const canvas = this.canvasElement.nativeElement;
    const context = canvas.getContext('2d');
    if (!context) return;

    context.drawImage(this.videoElement.nativeElement, 0, 0, canvas.width, canvas.height);
    this.photoDataUrl = canvas.toDataURL('image/png');
    canvas.toBlob(
      (blob: Blob | null) => {
        if (!blob) {
          console.error('Could not obtain blob from canvas');
          return;
        }
        // Create a File from the Blob
        this.file = new File([blob], 'myPhoto.png', { type: blob.type });
        console.log('File object:', this.file);
    
        // Optionally, upload the file to a server:
        // const formData = new FormData();
        // formData.append('photo', file);
        // this.http.post('/upload-endpoint', formData).subscribe(...);
      },
      'image/png', 
      1.0 // optional quality parameter for image formats like JPEG
    );
    this.isPhotoTaken = true;
  }

  retakePhoto(): void {
    this.isPhotoTaken = false;
    this.photoDataUrl = null;
    this.description = '';
  }

  async uploadPhoto(): Promise<void> {
    if (!this.photoDataUrl) return;

    const fileName = 'abcfile';

    // Estimate size from base64 length (roughly).
    // This is optional and just for demonstration.
    const sizeInBytes = Math.round((this.photoDataUrl.length * 3) / 4);

    // 1. Get presigned upload URL - Pass activity ID
    const data = await lastValueFrom(this.fileService.getPresignedUploadUrl(fileName, this.activity.id));

    // 2. PUT the file to the presigned URL
    const uploadResponse = await fetch(data.url, {
      method: 'PUT',
      body: this.file,
      headers: { 'Content-Type': this.file.type }
    });

    if (!uploadResponse.ok) {
      throw new Error('File upload failed.');
    }

    console.log(`Data key ${data.key}`);

    // Create a photo object
    const newPhoto: Attachment = {
      name: "dummyValue",
      key: data.key,
      uploadDate: new Date(),
      description: this.description,
      uploadedBy: 'Current User',
      size: sizeInBytes
    };

    // Add to the list of photos
    this.activity.evidences.push(newPhoto);

    this.emitUpdatedAttachments();

    // Reset the camera capture for next photo
    // this.isPhotoTaken = false;
    // this.photoDataUrl = null;
    // this.description = '';
    this.isCameraOpen = false;

    this.ngOnInit()
  }

  /**
   * Fetches a presigned URL for download, downloads the file as a Blob, then triggers a download in the browser.
   */
  async downloadPhoto(photo: Attachment): Promise<void> {
    console.log('Download button clicked for key:', photo.key);
    try {
      const data = await lastValueFrom(this.fileService.getPresignedDownloadUrl(photo.key, this.activity.id));
      console.log('Presigned URL received:', data);

      // 1. Fetch the file from the URL
      const response = await fetch(data.url);
      if (!response.ok) {
        throw new Error('Network response was not ok.');
      }

      // 2. Get the file as a Blob
      const blob = await response.blob();

      // 3. Create a temporary anchor element to trigger the download
      const anchor = document.createElement('a');
      const url = window.URL.createObjectURL(blob);
      anchor.href = url;
      anchor.download = photo.description;
      anchor.click();

      // 4. Clean up
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  }

  private emitUpdatedAttachments(): void {
    const activityInput: ActivityInput = {
      evidences: this.activity.evidences.map(({ __typename, ...rest }) => rest)
    };
    this.activityChange.emit(activityInput);
  }

/**
   * Opens a confirmation dialog, and if confirmed, deletes the file from S3 (via a presigned URL)
   * and updates the local `activity.attachments`.
   */
async deletePhoto(photo: Attachment): Promise<void> {
  const confirmed = await this.confirmDeletion(photo.description);

  if (!confirmed) {
    console.log('Deletion canceled by user');
    return;
  }

  try {
    // 1. Get presigned DELETE URL
    const data = await lastValueFrom(this.fileService.getPresignedDeleteUrl(photo.key, this.activity.id));

    // 2. DELETE request to presigned URL
    const response = await fetch(data.url, { method: 'DELETE' });
    if (!response.ok) {
      throw new Error('Failed to delete file from S3.');
    }

    // 3. Remove from local array and emit
    const index = this.activity.evidences.indexOf(photo);
    if (index > -1) {
      this.activity.evidences.splice(index, 1);
    }
    this.emitUpdatedAttachments();

    console.log(`File "${photo.key}" deleted successfully.`);
  } catch (error) {
    console.error('Error deleting file:', error);
  }
}

/**
 * Opens a material dialog asking the user to confirm deletion. Returns a Promise<boolean>.
 */
private confirmDeletion(fileName: string): Promise<boolean> {
  const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
    width: '400px',
    data: {
      title: 'Confirm File Deletion',
      message: `Are you sure you want to delete "${fileName}"?`,
      confirmText: 'Delete',
      cancelText: 'Cancel'
    }
  });

  return dialogRef.afterClosed().toPromise(); // Returns a Promise<boolean>
}

  formatFileSize(bytes: number): string {
    if (bytes < 1024) {
      return bytes + ' B';
    } else if (bytes < 1024 * 1024) {
      return (bytes / 1024).toFixed(2) + ' KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
    } else {
      return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
    }
  }

  isVisibleOpenCamera(): Boolean {
    // return this.authService.hasUserGroup(USER_GROUPS.ADMIN) ||
    // this.authService.hasAllUserGroups([USER_GROUPS.EXECUTOR, `Company_${this.activity.companyCode}`, `Discipline_${this.activity.disciplineCode}`])
    return true;
  }

  isVisibleDownloadPhoto(): Boolean {
    // return this.authService.hasUserGroup(USER_GROUPS.ADMIN) ||
    // this.authService.hasAllUserGroups([USER_GROUPS.EXECUTOR, `Company_${this.activity.companyCode}`, `Discipline_${this.activity.disciplineCode}`])
    return true;
  }

  isVisibleDeletePhoto(): Boolean {
    // return this.authService.hasUserGroup(USER_GROUPS.ADMIN) ||
    // this.authService.hasAllUserGroups([USER_GROUPS.EXECUTOR, `Company_${this.activity.companyCode}`, `Discipline_${this.activity.disciplineCode}`])
    return true;
  }

}
