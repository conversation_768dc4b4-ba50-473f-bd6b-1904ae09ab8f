.evidences-container {
  padding: 24px;
}


.camera-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .camera-button-container {
    display: flex;
    justify-content: center;
    margin-top: 1rem;
  }
  
  .camera-actions {
    margin-top: 1rem;
  }
  
  .description-field {
    margin-top: 16px;
    width: 100%;
    max-width: 320px;
  }
  
  .photos-list {
    margin-top: 2rem;
  }
  
  .photo-card {
    margin-bottom: 1rem;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 5px;
  }
  
  .photo-info {
    display: flex;
    flex-direction: column;
  }
  
  .photo-details {
    margin-right: 16px;
  }
  
  .photo-actions {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  
  .no-photos {
    margin-top: 2rem;
    text-align: center;
  }
  
  .no-photos mat-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    display: block;
    margin: 0 auto;
  }
  