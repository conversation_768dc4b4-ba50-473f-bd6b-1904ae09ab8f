<div class="evidences-container">

  <!-- List of photos -->
  <div class="photos-list" *ngIf="activity.evidences && activity.evidences.length > 0">
    <mat-card
      class="photo-card"
      *ngFor="let photo of activity.evidences"
    >
      <div class="photo-info">
        <div class="photo-details">
          <h4>{{ photo.description ? photo.description : ('EVIDENCES.UNTITLED_PHOTO' | translate) }}</h4>
          <p>
            {{ formatFileSize(photo.size) }}
            • {{ 'EVIDENCES.UPLOADED' | translate }} 
            {{ photo.uploadDate | date:'medium' }}
          </p>
          <p class="uploader">
            {{ 'EVIDENCES.UPLOADED_BY' | translate }} {{ photo.uploadedBy }}
          </p>
        </div>
      </div>
      <div class="photo-actions">
        <button *ngIf="isVisibleDownloadPhoto()" mat-icon-button color="primary" (click)="downloadPhoto(photo)" aria-label="Download photo">
          <mat-icon>download</mat-icon>
        </button>
        <button *ngIf="isVisibleDeletePhoto()" mat-icon-button color="warn" (click)="deletePhoto(photo)" aria-label="Delete photo">
          <mat-icon>delete</mat-icon>
        </button>
      </div>
    </mat-card>
  </div>

  <!-- If no photos in the list -->
  <div class="no-photos" *ngIf="activity.evidences.length === 0">
    <mat-icon>folder_open</mat-icon>
    <p>{{ 'EVIDENCES.NO_PHOTOS_UPLOADED' | translate }}</p>
  </div>

  <!-- A button to toggle the camera drop-down (expansion panel) -->
  <div *ngIf="isVisibleOpenCamera()" class="camera-button-container">
    <button
      mat-raised-button
      color="primary"
      (click)="isCameraOpen = !isCameraOpen"
    >
      <mat-icon>camera_alt</mat-icon>
      {{ isCameraOpen ? ('EVIDENCES.CLOSE_CAMERA' | translate) : ('EVIDENCES.OPEN_CAMERA' | translate) }}
    </button>
  </div>

  <!-- Expansion Panel for camera -->
  <mat-accordion *ngIf="isCameraOpen">
    <mat-expansion-panel 
        [(expanded)]="isCameraOpen" 
        (opened)="onCameraPanelOpened()"
    >
      <mat-expansion-panel-header>
        <mat-panel-title>
          {{ 'EVIDENCES.TAKE_PHOTO' | translate }}
        </mat-panel-title>
      </mat-expansion-panel-header>

      <div class="camera-content">
        <!-- Video -->
        <video
          #videoElement
          width="320"
          height="240"
          *ngIf="!isPhotoTaken"
        ></video>

        <!-- Canvas (hidden) -->
        <canvas
          #canvasElement
          width="320"
          height="240"
          hidden
        ></canvas>

        <!-- Preview of the taken photo -->
        <img
          *ngIf="isPhotoTaken && photoDataUrl"
          [src]="photoDataUrl"
          width="320"
          height="240"
          alt="Captured Photo"
        />

        <!-- Description field -->
        <div *ngIf="isPhotoTaken" class="description-field">
          <mat-form-field appearance="fill">
            <mat-label>
              {{ 'EVIDENCES.PHOTO_DESCRIPTION' | translate }}
            </mat-label>
            <input matInput [(ngModel)]="description" />
          </mat-form-field>
        </div>
      </div>

      <div class="camera-actions">
        <!-- Actions -->
        <button
          mat-raised-button
          color="primary"
          *ngIf="!isPhotoTaken"
          (click)="takePhoto()"
        >
          {{ 'EVIDENCES.TAKE_PHOTO' | translate }}
        </button>

        <button
          mat-raised-button
          color="warn"
          *ngIf="isPhotoTaken"
          (click)="retakePhoto()"
        >
          {{ 'EVIDENCES.RETAKE_PHOTO' | translate }}
        </button>

        <button
          mat-raised-button
          color="accent"
          *ngIf="isPhotoTaken"
          (click)="uploadPhoto()"
        >
          {{ 'EVIDENCES.KEEP_UPLOAD_PHOTO' | translate }}
        </button>
      </div>
    </mat-expansion-panel>
  </mat-accordion>
</div>
