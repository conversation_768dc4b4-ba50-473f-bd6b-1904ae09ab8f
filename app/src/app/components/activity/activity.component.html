<!-- Show the content only if not loading -->
<div *ngIf="!loading">

  <h2>{{ activity.name }}</h2>
  <div class="id-status-container">
    <h4>{{ 'ACTIVITY_TITLE_ID' | translate }}: {{ activity.id }}</h4>
    <div class="status-section">
      <mat-chip-set>
        <mat-chip highlighted>
          {{ ('STATUS_VALUES.' + activity.status) | translate }}
        </mat-chip>
        <mat-chip *ngIf="activity.unableToWork">
          {{ 'UNABLE_TO_WORK' | translate }}
        </mat-chip>
      </mat-chip-set>
    </div>
  </div>

  <!-- Show access denied message if there's an access denied error -->
  <div *ngIf="error && error.type === 'ACCESS_DENIED'" class="header-content">
    <div class="main-info">
      <div class="title-section">
        <h2>{{ 'ACCESS_DENIED' | translate }}</h2>local
      </div>
    </div>
  </div>

  <mat-divider *ngIf="activity || (error && error.type === 'ACCESS_DENIED')"></mat-divider>

  <!-- Show all tabs if no access denied error -->
  <mat-tab-group *ngIf="!error || error.type !== 'ACCESS_DENIED'" class="activity-tabs">

    <!-- Actions Tab -->
    <mat-tab label="{{ 'PROGRESS_TAB' | translate }}">
      <app-progress [activity]="activity" (activityChange)="onActivityChange($event)"></app-progress>
    </mat-tab>

    <!-- Details Tab -->
    <mat-tab label="{{ 'DETAILS_TAB' | translate }}">
      <app-activity-details [activity]="activity" [error]="error"></app-activity-details>
    </mat-tab>

    <!-- Relationships Tab -->
    <mat-tab label="{{ 'RELATIONSHIPS_TAB' | translate }}">
      <app-activity-relationships [activity]="activity"></app-activity-relationships>
    </mat-tab>

    <!-- Evidences Tab -->
    <mat-tab label="{{ 'EVIDENCES_TAB' | translate }}">
      <app-photo-list-and-capture [activity]="activity"
        (activityChange)="onActivityChange($event)"></app-photo-list-and-capture>
    </mat-tab>

    <!-- Log Tab -->
    <mat-tab label="{{ 'LOGS_TAB' | translate }}">
      <app-logs [activity]="activity"></app-logs>
    </mat-tab>
  </mat-tab-group>

  <!-- Show only details tab if access denied error -->
  <div *ngIf="error && error.type === 'ACCESS_DENIED'">
    <app-activity-details [error]="error"></app-activity-details>
  </div>


</div>