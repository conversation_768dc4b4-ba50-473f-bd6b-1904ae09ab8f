import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { map, Observable, switchMap } from 'rxjs';

@Component({
  selector: 'app-doc-page',
  standalone: false,
  templateUrl: './doc-page.component.html',
  styleUrl: './doc-page.component.css'
})
export class DocPageComponent implements OnInit {
  content$: Observable<string>;

  constructor(
    private route: ActivatedRoute,
    private http: HttpClient
  ) {}

  ngOnInit() {
    this.content$ = this.route.paramMap.pipe(
      map(params => params.get('slug')),
      switchMap(slug =>
        this.http.get(`assets/docs/${slug}.md`, { responseType: 'text' })
      )
    );
  }
}
