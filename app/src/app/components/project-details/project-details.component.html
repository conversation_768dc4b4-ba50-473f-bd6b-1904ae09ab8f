<div *ngIf="project" class="project-details">
  <h2>{{ 'PROJECT_DETAILS.TITLE' | translate }}</h2>
  
  <mat-tab-group>
    <!-- Tab 1: Project Information -->
    <mat-tab [label]="'PROJECT_DETAILS.TABS.PROJECT_INFO' | translate">
      <div class="tab-content">
        <!-- Basic Project Information -->
        <div class="info-section">
          <h3>{{ 'PROJECT_DETAILS.SECTIONS.BASIC_INFO' | translate }}</h3>
          <p><strong>{{ 'PROJECT_DETAILS.FIELDS.NAME' | translate }}:</strong> {{ project.name }}</p>
          <p><strong>{{ 'PROJECT_DETAILS.FIELDS.DISPLAY_NAME' | translate }}:</strong> {{ project.displayName }}</p>
          <p><strong>{{ 'PROJECT_DETAILS.FIELDS.DESCRIPTION' | translate }}:</strong> {{ project.description }}</p>
          <p><strong>{{ 'PROJECT_DETAILS.FIELDS.STATUS' | translate }}:</strong> {{ project.status }}</p>
          <p><strong>{{ 'PROJECT_DETAILS.FIELDS.PROJECT_ID' | translate }}:</strong> {{ project.projectId }}</p>
          <p><strong>{{ 'PROJECT_DETAILS.FIELDS.ID' | translate }}:</strong> {{ project.id }}</p>
          <p><strong>{{ 'PROJECT_DETAILS.FIELDS.START_DATE' | translate }}:</strong> {{ project.startDate | date }}</p>
          <p><strong>{{ 'PROJECT_DETAILS.FIELDS.FINISH_DATE' | translate }}:</strong> {{ project.finishDate | date }}</p>
          <p><strong>{{ 'PROJECT_DETAILS.FIELDS.ALLOW_STATUS_REVIEW' | translate }}:</strong> {{ project.allowStatusReview }}</p>
          <p><strong>{{ 'PROJECT_DETAILS.FIELDS.ACTIVITY_COUNT' | translate }}:</strong> {{ project.activityCount }}</p>
        </div>

        <!-- OBS Section -->
        <div class="info-section">
          <h3>{{ 'PROJECT_DETAILS.SECTIONS.OBS' | translate }}</h3>
          <p><strong>{{ 'PROJECT_DETAILS.FIELDS.OBS_NAME' | translate }}:</strong> {{ project.obsName }}</p>
          <p><strong>{{ 'PROJECT_DETAILS.FIELDS.OBS_OBJECT_ID' | translate }}:</strong> {{ project.obsObjectId }}</p>
          <p><strong>{{ 'PROJECT_DETAILS.FIELDS.WBS_OBJECT_ID' | translate }}:</strong> {{ project.wbsObjectId }}</p>
        </div>

        <!-- EPS Section -->
        <div class="info-section">
          <h3>{{ 'PROJECT_DETAILS.SECTIONS.EPS' | translate }}</h3>
          <p><strong>{{ 'PROJECT_DETAILS.FIELDS.PARENT_EPS_NAME' | translate }}:</strong> {{ project.parentEPSName }}</p>
          <p><strong>{{ 'PROJECT_DETAILS.FIELDS.PARENT_EPS_ID' | translate }}:</strong> {{ project.parentEPSId }}</p>
          <p><strong>{{ 'PROJECT_DETAILS.FIELDS.PARENT_EPS_OBJECT_ID' | translate }}:</strong> {{ project.parentEPSObjectId }}</p>
        </div>

        <!-- TAEX Synchronization Information -->
        <div class="info-section">
          <h3>{{ 'PROJECT_DETAILS.SECTIONS.TAEX_SYNC' | translate }}</h3>
          <p><strong>{{ 'PROJECT_DETAILS.FIELDS.CREATED_AT' | translate }}:</strong> {{ project.createdAt | date:'medium' }}</p>
          <p><strong>{{ 'PROJECT_DETAILS.FIELDS.UPDATED_AT' | translate }}:</strong> {{ project.updatedAt | date:'medium' }}</p>
        </div>
      </div>
    </mat-tab>
    
    <!-- Tab 2: Project Configuration -->
    <mat-tab [label]="'PROJECT_DETAILS.TABS.PROJECT_CONFIG' | translate">
      <div class="tab-content">
        <div class="syncing-toggle">
          <mat-slide-toggle [(ngModel)]="project.isSyncing" (change)="toggleSyncing()">
            {{ 'PROJECT_DETAILS.SYNCING' | translate }}
          </mat-slide-toggle>
        </div>
        
        <div class="visibility-toggle">
          <mat-slide-toggle [(ngModel)]="project.isVisible" (change)="toggleVisibility()">
            {{ 'PROJECT_DETAILS.VISIBILITY' | translate }}
          </mat-slide-toggle>
          <div class="toggle-description">
            {{ 'PROJECT_DETAILS.VISIBILITY_DESCRIPTION' | translate }}
          </div>
        </div>
        
        <!-- Manager Groups -->
        <h3>{{ 'PROJECT_DETAILS.GROUPS.MANAGER' | translate }}</h3>
        <div class="group-selection">
          <mat-form-field>
            <mat-label>{{ 'PROJECT_DETAILS.ACTIONS.ADD_MANAGER_GROUP' | translate }}</mat-label>
            <mat-select>
              <mat-option *ngFor="let group of filteredManagerGroups" [value]="group" (click)="addManagerGroup(group)">
                {{ group.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          
          <div class="selected-groups">
            <mat-chip-set>
              <mat-chip *ngFor="let group of project.managerGroups; trackBy: trackByFn" 
                      (removed)="removeManagerGroup(group)">
                {{ group.name }}
                <button matChipRemove>
                  <mat-icon>cancel</mat-icon>
                </button>
              </mat-chip>
            </mat-chip-set>
          </div>
        </div>
      
        <!-- Operator Groups -->
        <h3>{{ 'PROJECT_DETAILS.GROUPS.OPERATOR' | translate }}</h3>
        <div class="group-selection">
          <mat-form-field>
            <mat-label>{{ 'PROJECT_DETAILS.ACTIONS.ADD_OPERATOR_GROUP' | translate }}</mat-label>
            <mat-select>
              <mat-option *ngFor="let group of filteredOperatorGroups" [value]="group" (click)="addOperatorGroup(group)">
                {{ group.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          
          <div class="selected-groups">
            <mat-chip-set>
              <mat-chip *ngFor="let group of project.operatorGroups; trackBy: trackByFn" 
                      (removed)="removeOperatorGroup(group)">
                {{ group.name }}
                <button matChipRemove>
                  <mat-icon>cancel</mat-icon>
                </button>
              </mat-chip>
            </mat-chip-set>
          </div>
        </div>
      
        <!-- Worker Groups -->
        <h3>{{ 'PROJECT_DETAILS.GROUPS.WORKER' | translate }}</h3>
        <div class="group-selection">
          <mat-form-field>
            <mat-label>{{ 'PROJECT_DETAILS.ACTIONS.ADD_WORKER_GROUP' | translate }}</mat-label>
            <mat-select>
              <mat-option *ngFor="let group of filteredWorkerGroups" [value]="group" (click)="addWorkerGroup(group)">
                {{ group.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          
          <div class="selected-groups">
            <mat-chip-set>
              <mat-chip *ngFor="let group of project.workerGroups; trackBy: trackByFn" 
                      (removed)="removeWorkerGroup(group)">
                {{ group.name }}
                <button matChipRemove>
                  <mat-icon>cancel</mat-icon>
                </button>
              </mat-chip>
            </mat-chip-set>
          </div>
        </div>
      </div>
    </mat-tab>
  </mat-tab-group>
</div>
