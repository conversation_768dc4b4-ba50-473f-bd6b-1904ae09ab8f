import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Group, GroupType, Project } from 'src/app/graphql/generated';
import { GroupService } from 'src/app/services/group.service';
import { Observable } from 'rxjs';
import { TranslateService } from 'src/app/core/services/translate.service';

@Component({
  selector: 'app-project-details',
  templateUrl: './project-details.component.html',
  styleUrls: ['./project-details.component.css']
})
export class ProjectDetailsComponent implements OnChanges, OnInit {
  @Input() project: Project | null = null;
  @Output() projectChanged = new EventEmitter<Project>();
  
  availableGroups: Group[] = [];
  filteredManagerGroups: Group[] = [];
  filteredOperatorGroups: Group[] = [];
  filteredWorkerGroups: Group[] = [];
  
  constructor(
    private groupService: GroupService,
    private translateService: TranslateService
  ) {}
  
  ngOnInit(): void {
    this.loadAvailableGroups();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['project'] && this.project) {
      // Initialize arrays if they don't exist
      this.project.managerGroups = this.project.managerGroups || [];
      this.project.operatorGroups = this.project.operatorGroups || [];
      this.project.workerGroups = this.project.workerGroups || [];
      
      // Initialize isVisible to false if it doesn't exist
      if (this.project.isVisible === undefined) {
        this.project.isVisible = false;
      }
      
      // Update filtered groups
      this.updateFilteredGroups();
    }
  }
  
  loadAvailableGroups(): void {
    this.groupService.listGroups(100).subscribe(result => {
      this.availableGroups = result.items;
      this.updateFilteredGroups();
    });
  }
  
  updateFilteredGroups(): void {
    if (!this.project || !this.availableGroups.length) return;
    
    // Filter out groups that are already assigned to this project
    const assignedGroupIds = [
      ...(this.project.managerGroups?.map(g => g.id) || []),
      ...(this.project.operatorGroups?.map(g => g.id) || []),
      ...(this.project.workerGroups?.map(g => g.id) || [])
    ];
    
    this.filteredManagerGroups = this.availableGroups.filter(
      group => !assignedGroupIds.includes(group.id) || 
               this.project?.managerGroups?.some(g => g.id === group.id)
    );
    
    this.filteredOperatorGroups = this.availableGroups.filter(
      group => !assignedGroupIds.includes(group.id) || 
               this.project?.operatorGroups?.some(g => g.id === group.id)
    );
    
    this.filteredWorkerGroups = this.availableGroups.filter(
      group => !assignedGroupIds.includes(group.id) || 
               this.project?.workerGroups?.some(g => g.id === group.id)
    );
  }

  toggleSyncing(): void {
    if (this.project) {
      this.projectChanged.emit(this.project);
    }
  }

  toggleVisibility(): void {
    if (this.project) {
      this.projectChanged.emit(this.project);
    }
  }

  trackByFn(index: number, item: Group): string {
    return item.id;
  }

  addManagerGroup(group: Group): void {
    if (this.project && group) {
      // Check if the group is not already in the list
      if (!this.project.managerGroups.some(g => g.id === group.id)) {
        this.project.managerGroups.push(group);
        this.projectChanged.emit(this.project);
        this.updateFilteredGroups();
      }
    }
  }

  removeManagerGroup(group: Group): void {
    if (this.project) {
      const index = this.project.managerGroups.findIndex(g => g.id === group.id);
      if (index >= 0) {
        this.project.managerGroups.splice(index, 1);
        this.projectChanged.emit(this.project);
        this.updateFilteredGroups();
      }
    }
  }

  addOperatorGroup(group: Group): void {
    if (this.project && group) {
      // Check if the group is not already in the list
      if (!this.project.operatorGroups.some(g => g.id === group.id)) {
        this.project.operatorGroups.push(group);
        this.projectChanged.emit(this.project);
        this.updateFilteredGroups();
      }
    }
  }

  removeOperatorGroup(group: Group): void {
    if (this.project) {
      const index = this.project.operatorGroups.findIndex(g => g.id === group.id);
      if (index >= 0) {
        this.project.operatorGroups.splice(index, 1);
        this.projectChanged.emit(this.project);
        this.updateFilteredGroups();
      }
    }
  }

  addWorkerGroup(group: Group): void {
    if (this.project && group) {
      // Check if the group is not already in the list
      if (!this.project.workerGroups.some(g => g.id === group.id)) {
        this.project.workerGroups.push(group);
        this.projectChanged.emit(this.project);
        this.updateFilteredGroups();
      }
    }
  }

  removeWorkerGroup(group: Group): void {
    if (this.project) {
      const index = this.project.workerGroups.findIndex(g => g.id === group.id);
      if (index >= 0) {
        this.project.workerGroups.splice(index, 1);
        this.projectChanged.emit(this.project);
        this.updateFilteredGroups();
      }
    }
  }
}
