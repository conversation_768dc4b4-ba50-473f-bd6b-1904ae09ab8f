import { Component, OnInit } from '@angular/core';
import { TranslateService } from 'src/app/core/services/translate.service';


@Component({
  selector: 'app-about',
  templateUrl: './about.component.html',
  styleUrls: ['./about.component.css']
})
export class AboutComponent implements OnInit {
  version: string = '1.0.0';
  supportEmail: string = '<EMAIL>';
  language: string;

  constructor(private translateService: TranslateService) {}

  ngOnInit(): void {
    this.language = this.translateService.getCurrentLanguage();
  }
}
