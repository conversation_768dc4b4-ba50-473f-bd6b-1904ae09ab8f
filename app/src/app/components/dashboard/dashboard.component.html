<div *ngIf="loading" class="loading-container">
  <mat-spinner></mat-spinner>
  <p>{{ 'LOADING_MESSAGE' | translate }}</p>
</div>

<div class="dashboard" *ngIf="!loading">

      <!-- Title and Select Project -->
    <div class="header-container">
      <h1>{{ 'DASHBOARD_TITLE' | translate }}</h1>
        <app-project-selector class="project-select" (projectSelected)="onProjectChange($event)"></app-project-selector>
    </div>

  <div class="dashboard-items">
    <!-- Message when no project is selected -->
    <div *ngIf="!isProjectSelected()" class="no-project-selected">
      <p>Please select a project to view dashboard data.</p>
    </div>
    
    <!-- Only show charts when a project is selected -->
    <ng-container *ngIf="isProjectSelected()">
      <!-- Flex container for Pie Chart & Unable-to-Work -->
      <div class="dashboard-row">
        <div class="dashboard-item piechart">
          <app-dashboard-piechartprogress [projectId]="selectedProjectId"></app-dashboard-piechartprogress>
        </div>
        <div class="dashboard-item unable-to-work">
          <app-unable-to-work [projectId]="selectedProjectId"></app-unable-to-work>
        </div>
      </div>

      <!--<div class="dashboard-item">
        <app-burndown-chart></app-burndown-chart>
      </div> -->

      <div class="dashboard-item">
        <app-s-curve-chart [projectId]="selectedProjectId"></app-s-curve-chart>
      </div>

      <div class="dashboard-item">
        <app-activities-by-status-discipline [projectId]="selectedProjectId"></app-activities-by-status-discipline>
      </div>
    </ng-container>
  </div>
</div>
