.dashboard-items {
  display: flex;
  flex-direction: column;
  gap: 16px; /* Space between sections */
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}


.no-project-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin: 20px 0;
}

.no-project-selected p {
  font-size: 18px;
  color: #666;
  text-align: center;
}

.dashboard-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: center; /* Center content on smaller screens */
  align-items: center;
}

/* Pie Chart should be at most 33% of the screen width */
.piechart {
  flex: 0 1 33%;  /* Flex-basis 33%, no growth */
  min-width: 250px; /* Prevents it from becoming too small */
  max-width: 400px; /* Ensures it doesn't grow too large */
}

/* Unable-to-Work takes the remaining space */
.unable-to-work {
  flex: 1;  /* Takes remaining space */
  min-width: 300px; /* Ensures it doesn’t shrink too much */
}

/* Responsive behavior: Stack items vertically on smaller screens */
@media (max-width: 768px) {
  .dashboard-row {
      flex-direction: column;
  }

  .piechart,
  .unable-to-work {
      max-width: 100%; /* Full width on small screens */
  }
}
