.chart-container {
  height: 300px;
  position: relative;
  margin: 20px 0;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.no-data-message {
  text-align: center;
  padding: 40px 0;
  color: rgba(0, 0, 0, 0.54);
}

.totals-container {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.total-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.54);
}

.value {
  font-size: 24px;
  font-weight: 500;
  margin-top: 5px;
}
