<mat-card>
  <mat-card-header>
    <mat-card-title>{{ 'S_CURVE_CHART' | translate }}</mat-card-title>
    <mat-card-subtitle>
      {{ 'PLANNED_VS_COMPLETED_ACTIVITIES' | translate }}
    </mat-card-subtitle>
  </mat-card-header>
  <mat-card-content>
    <div *ngIf="loading" class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
    </div>
    
    <ng-container *ngIf="!loading && !nothingToShow; else noDataTemplate">
      <div class="chart-container">
        <canvas baseChart
                [data]="lineChartData"
                [options]="lineChartOptions"
                type="line">
        </canvas>
      </div>
      
      <div class="totals-container">
        <div class="total-item">
          <span class="label">{{ 'TOTAL_PLANNED' | translate }}:</span>
          <span class="value">{{ totalPlanned }}</span>
        </div>
        <div class="total-item">
          <span class="label">{{ 'TOTAL_COMPLETED' | translate }}:</span>
          <span class="value">{{ totalCompleted }}</span>
        </div>
      </div>
    </ng-container>
    
    <ng-template #noDataTemplate>
      <p *ngIf="!loading" class="no-data-message">{{ 'NO_ACTIVITY_DATA' | translate }}</p>
    </ng-template>
  </mat-card-content>
</mat-card>
