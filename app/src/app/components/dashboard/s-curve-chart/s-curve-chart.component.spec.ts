import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SCurveChartComponent } from './s-curve-chart.component';
import { DashboardService } from 'src/app/services/dashboard.service';
import { ChartColorsService } from 'src/app/services/chartColors.service';
import { TranslateService } from '@ngx-translate/core';
import { of } from 'rxjs';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { BaseChartDirective } from 'ng2-charts';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('SCurveChartComponent', () => {
  let component: SCurveChartComponent;
  let fixture: ComponentFixture<SCurveChartComponent>;
  let dashboardServiceSpy: jasmine.SpyObj<DashboardService>;
  let chartColorsServiceSpy: jasmine.SpyObj<ChartColorsService>;
  let translateServiceSpy: jasmine.SpyObj<TranslateService>;

  beforeEach(async () => {
    const dashboardSpy = jasmine.createSpyObj('DashboardService', [
      'getCompletedActivitiesByDay',
      'getPlannedActivitiesByDay'
    ]);
    
    const chartColorsSpy = jasmine.createSpyObj('ChartColorsService', ['getColorPalette']);
    chartColorsSpy.getColorPalette.and.returnValue({
      blue: '#009FE4',
      green: '#00BB7E'
    });
    
    const translateSpy = jasmine.createSpyObj('TranslateService', ['instant']);
    translateSpy.instant.and.callFake((key: string) => key);

    await TestBed.configureTestingModule({
      declarations: [SCurveChartComponent],
      imports: [
        MatCardModule,
        MatProgressSpinnerModule,
        BaseChartDirective
      ],
      providers: [
        { provide: DashboardService, useValue: dashboardSpy },
        { provide: ChartColorsService, useValue: chartColorsSpy },
        { provide: TranslateService, useValue: translateSpy }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    dashboardServiceSpy = TestBed.inject(DashboardService) as jasmine.SpyObj<DashboardService>;
    chartColorsServiceSpy = TestBed.inject(ChartColorsService) as jasmine.SpyObj<ChartColorsService>;
    translateServiceSpy = TestBed.inject(TranslateService) as jasmine.SpyObj<TranslateService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SCurveChartComponent);
    component = fixture.componentInstance;
    
    // Mock data
    const completedData = [
      { date: '2025-01-01', count: 5 },
      { date: '2025-01-02', count: 3 },
      { date: '2025-01-03', count: 7 }
    ];
    
    const plannedData = [
      { date: '2025-01-01', count: 8 },
      { date: '2025-01-02', count: 6 },
      { date: '2025-01-03', count: 10 }
    ];
    
    dashboardServiceSpy.getCompletedActivitiesByDay.and.returnValue(of(completedData));
    dashboardServiceSpy.getPlannedActivitiesByDay.and.returnValue(of(plannedData));
    
    component.projectId = 'test-project-id';
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should fetch data on init', () => {
    component.ngOnInit();
    expect(dashboardServiceSpy.getCompletedActivitiesByDay).toHaveBeenCalledWith('test-project-id');
    expect(dashboardServiceSpy.getPlannedActivitiesByDay).toHaveBeenCalledWith('test-project-id');
  });

  it('should calculate cumulative data correctly', () => {
    const testData = [
      { date: '2025-01-01', count: 5 },
      { date: '2025-01-02', count: 3 },
      { date: '2025-01-03', count: 7 }
    ];
    
    const result = component.calculateCumulativeData(testData);
    
    expect(result.length).toBe(3);
    expect(result[0].y).toBe(5);
    expect(result[1].y).toBe(8);  // 5 + 3
    expect(result[2].y).toBe(15); // 5 + 3 + 7
  });

  it('should update chart data when project changes', () => {
    component.ngOnInit();
    
    const spy = spyOn(component, 'fetchData');
    component.projectId = 'new-project-id';
    
    component.ngOnChanges({
      projectId: {
        currentValue: 'new-project-id',
        previousValue: 'test-project-id',
        firstChange: false,
        isFirstChange: () => false
      }
    });
    
    expect(spy).toHaveBeenCalled();
  });
});
