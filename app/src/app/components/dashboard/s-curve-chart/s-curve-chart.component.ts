import { Component, Input, OnC<PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, SimpleChang<PERSON> } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ChartConfiguration, ChartOptions } from 'chart.js';
import { ChartColorsService } from 'src/app/services/chartColors.service';
import { DashboardService } from 'src/app/services/dashboard.service';
import { forkJoin, Subscription } from 'rxjs';
import { first } from 'rxjs/operators';

interface DailyActivityCount {
  date: string;
  count: number;
}

@Component({
  selector: 'app-s-curve-chart',
  templateUrl: './s-curve-chart.component.html',
  styleUrls: ['./s-curve-chart.component.css'],
})
export class SCurveChartComponent implements OnInit, OnChanges, OnDestroy {
  @Input() projectId!: string;
  
  public lineChartData!: ChartConfiguration<'line'>['data'];
  public lineChartOptions: ChartOptions = {
    responsive: true,
    scales: {
      x: {
        type: 'time',
        time: {
          unit: 'day',
          tooltipFormat: 'MMM d, yyyy',
          displayFormats: {
            day: 'MMM d'
          }
        },
        title: {
          display: true,
          text: 'Date'
        }
      },
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Cumulative Count'
        }
      }
    },
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
      tooltip: {
        mode: 'index',
        intersect: false,
      }
    }
  };
  
  public nothingToShow = false;
  public loading = false;
  public totalPlanned = 0;
  public totalCompleted = 0;
  
  private langChangeSubscription: Subscription;

  constructor(
    private dashboardService: DashboardService,
    private chartColorsService: ChartColorsService,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    // Update chart options with translated labels
    this.updateChartOptions();
    
    // Subscribe to language changes
    this.langChangeSubscription = this.translateService.onLangChange.subscribe(() => {
      this.updateChartOptions();
      // If we have data, update the chart labels
      if (this.lineChartData) {
        this.updateChartLabels();
      }
    });
    
    if (this.projectId) {
      this.fetchData();
    }
  }
  
  ngOnDestroy(): void {
    // Clean up subscription
    if (this.langChangeSubscription) {
      this.langChangeSubscription.unsubscribe();
    }
  }
  
  updateChartOptions(): void {
    this.lineChartOptions = {
      responsive: true,
      scales: {
        x: {
          type: 'time',
          time: {
            unit: 'day',
            tooltipFormat: 'MMM d, yyyy',
            displayFormats: {
              day: 'MMM d'
            }
          },
          title: {
            display: true,
            text: this.translateService.instant('CHART_AXIS_DATE')
          }
        },
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: this.translateService.instant('CHART_AXIS_CUMULATIVE_COUNT')
          }
        }
      },
      plugins: {
        legend: {
          display: true,
          position: 'top',
        },
        tooltip: {
          mode: 'index',
          intersect: false,
        }
      }
    };
  }
  
  updateChartLabels(): void {
    if (this.lineChartData && this.lineChartData.datasets) {
      // Update dataset labels with translated text
      this.lineChartData.datasets.forEach(dataset => {
        if (dataset.label === this.translateService.instant('COMPLETED_ACTIVITIES') || 
            dataset.label.includes('Completed')) {
          dataset.label = this.translateService.instant('COMPLETED_ACTIVITIES');
        } else if (dataset.label === this.translateService.instant('PLANNED_ACTIVITIES') || 
                  dataset.label.includes('Planned')) {
          dataset.label = this.translateService.instant('PLANNED_ACTIVITIES');
        }
      });
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['projectId']?.currentValue !== changes['projectId']?.previousValue) {
      this.fetchData();
    }
  }

  fetchData(): void {
    // Check if projectId is valid before proceeding
    if (!this.projectId) {
      console.warn('Cannot fetch data: projectId is null or undefined');
      this.loading = false;
      this.nothingToShow = true;
      // Reset data
      this.totalCompleted = 0;
      this.totalPlanned = 0;
      return;
    }
    
    this.loading = true;
    
    console.log('Fetching data for projectId:', this.projectId);
    
    forkJoin({
      completed: this.dashboardService.getCompletedActivitiesByDay(this.projectId),
      planned: this.dashboardService.getPlannedActivitiesByDay(this.projectId)
    }).subscribe({
      next: (result) => {
        console.log('Data received successfully:', result);
        this.processChartData(result.completed, result.planned);
        this.loading = false;
      },
      error: (err) => {
        console.error('Error fetching activity data:', err);
        this.loading = false;
        this.nothingToShow = true;
        // Reset data on error
        this.totalCompleted = 0;
        this.totalPlanned = 0;
        // Create empty chart data
        this.lineChartData = {
          datasets: []
        };
      },
      complete: () => {
        console.log('ForkJoin completed');
      }
    });
  }

  processChartData(completedData: DailyActivityCount[], plannedData: DailyActivityCount[]): void {
    // Sort data by date
    const sortedCompleted = [...completedData].sort((a, b) => 
      new Date(a.date).getTime() - new Date(b.date).getTime());
    
    const sortedPlanned = [...plannedData].sort((a, b) => 
      new Date(a.date).getTime() - new Date(b.date).getTime());
    
    // Calculate cumulative counts
    const completedCumulative = this.calculateCumulativeData(sortedCompleted);
    const plannedCumulative = this.calculateCumulativeData(sortedPlanned);
    
    // Get total counts
    this.totalCompleted = completedCumulative.length > 0 ? 
      completedCumulative[completedCumulative.length - 1].y : 0;
    
    this.totalPlanned = plannedCumulative.length > 0 ? 
      plannedCumulative[plannedCumulative.length - 1].y : 0;
    
    // Check if we have data to show
    this.nothingToShow = completedCumulative.length === 0 && plannedCumulative.length === 0;
    
    if (this.nothingToShow) {
      return;
    }
    
    // Create chart data
    const colors = this.chartColorsService.getColorPalette();
    
    this.lineChartData = {
      datasets: [
        {
          data: completedCumulative,
          label: this.translateService.instant('COMPLETED_ACTIVITIES'),
          borderColor: colors['green'],
          backgroundColor: 'rgba(0, 187, 126, 0.2)',
          pointBackgroundColor: colors['green'],
          fill: true,
          tension: 0.4
        },
        {
          data: plannedCumulative,
          label: this.translateService.instant('PLANNED_ACTIVITIES'),
          borderColor: colors['blue'],
          backgroundColor: 'rgba(0, 159, 228, 0.2)',
          pointBackgroundColor: colors['blue'],
          fill: true,
          tension: 0.4,
          borderDash: [5, 5]
        }
      ]
    };
    
    // Make sure chart options are using translated labels
    this.updateChartOptions();
  }
  
  calculateCumulativeData(data: DailyActivityCount[]): {x: number, y: number}[] {
    let cumulative = 0;
    return data.map(item => {
      cumulative += item.count;
      return {
        x: new Date(item.date).getTime(),
        y: cumulative
      };
    });
  }
}
