import { Component, OnInit } from '@angular/core';
import { ChartColorsService } from 'src/app/services/chartColors.service';


@Component({
  selector: 'app-burndown-chart',
  templateUrl: './burndown-chart.component.html',
  styleUrls: ['./burndown-chart.component.css'],
})
export class BurndownChartComponent implements OnInit {
  public burndownChartData: any;
  public burndownChartOptions: any;

  constructor(private chartColorsService: ChartColorsService) {}

  ngOnInit(): void {
    const colors = this.chartColorsService.getLineChartColors();

    this.burndownChartData = {
      labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'],
      datasets: [
        {
          label: 'Planned Remaining Activities',
          data: [50, 45, 40, 35, 30, 20, 0],
          borderColor: colors[0].borderColor,
          backgroundColor: colors[0].backgroundColor,
          fill: true,
          tension: 0.3,
        },
        {
          label: 'Actual Remaining Activities',
          data: [50, 40, 35, 30, 25, 15, 5],
          borderColor: colors[1].borderColor,
          backgroundColor: colors[1].backgroundColor,
          fill: true,
          tension: 0.3,
        },
      ],
    };

    this.burndownChartOptions = {
      responsive: true,
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          callbacks: {
            label: (tooltipItem: any) =>
              `${tooltipItem.dataset.label}: ${tooltipItem.raw} activities`,
          },
        },
      },
      scales: {
        x: {
          title: {
            display: true,
            text: 'Time (Days)',
          },
        },
        y: {
          title: {
            display: true,
            text: 'Number of Activities',
          },
          beginAtZero: true,
        },
      },
    };
  }
}
