import { Component, OnInit } from '@angular/core';
import { Project } from 'src/app/graphql/generated';
import { ProjectService } from 'src/app/services/project.service';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class DashboardComponent implements OnInit {

  projects: Project[] = [];
  selectedProjectId: string | null = null;
  loading = true;
  error: string | null = null;

  constructor() { }

  ngOnInit(): void {
    this.loading = false;
  }

  // Helper method to check if selectedProjectId is valid
  isProjectSelected(): boolean {
    return !!this.selectedProjectId;
  }

  onProjectChange(project: Project): void {
    this.selectedProjectId = project.id;
  }

}
