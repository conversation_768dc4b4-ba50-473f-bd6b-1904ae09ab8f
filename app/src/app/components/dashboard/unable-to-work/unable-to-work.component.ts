import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { DashboardService } from 'src/app/services/dashboard.service';

@Component({
  selector: 'app-unable-to-work',
  templateUrl: './unable-to-work.component.html',
  styleUrl: './unable-to-work.component.css'
})
export class UnableToWorkComponent implements OnInit, OnChanges {
  @Input() projectId!: string;
  activityCount: number = 0;

  constructor(private dashboardService: DashboardService) { }

  ngOnInit(): void {
    if (this.projectId) {
      this.fetchCountOfUnableToWorkActivities();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['projectId']) {
      this.fetchCountOfUnableToWorkActivities();
    }
  }

  fetchCountOfUnableToWorkActivities(): void {
    if (!this.projectId) {
      // Handle the case when projectId is null or undefined
      this.activityCount = 0;
      return;
    }
    
    this.dashboardService.getCountOfUnableToWorkActivities(this.projectId).subscribe({
      next: (unableToWorkCount) => {
        this.activityCount = unableToWorkCount;
      },
      error: (err) => {
        console.error('Error fetching unableToWork count:', err);
        // Set count to 0 on error
        this.activityCount = 0;
      }
    });
  }
}
