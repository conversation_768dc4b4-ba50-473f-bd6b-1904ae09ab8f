<div class="chart-container">
  <h2>{{ 'ACTIVITIES_BY_STATUS_DISCIPLINE' | translate }}</h2>
  
  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>{{ 'LOADING_DATA' | translate }}</p>
  </div>
  
  <!-- Error message -->
  <div *ngIf="errorMessage" class="error-container">
    <p class="error-message">{{ 'ERROR_LOADING_DATA' | translate }}</p>
  </div>
  
  <!-- No data message -->
  <div *ngIf="!isLoading && nothingToShow && !errorMessage" class="no-data-container">
    <p>{{ 'NO_DATA_AVAILABLE' | translate }}</p>
  </div>
  
  <!-- Chart -->
  <div *ngIf="!isLoading && !nothingToShow && !errorMessage" class="chart-wrapper">
    <canvas baseChart
      [data]="barChartData"
      [type]="barChartType"
      [options]="barChartOptions">
    </canvas>
  </div>
</div>
