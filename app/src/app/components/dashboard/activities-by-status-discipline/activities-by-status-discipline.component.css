.chart-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

h2 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
  color: #333;
}

.chart-wrapper {
  flex: 1;
  min-height: 300px;
  position: relative;
}

.loading-container,
.error-container,
.no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.error-message {
  color: #d32f2f;
  font-weight: bold;
}

mat-spinner {
  margin-bottom: 16px;
}
