import { Component, Input, On<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, SimpleChang<PERSON> } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ChartConfiguration, ChartOptions } from 'chart.js';
import { Status } from 'src/app/graphql/generated';
import { ChartColorsService } from 'src/app/services/chartColors.service';
import { DashboardService } from 'src/app/services/dashboard.service';

interface StatusDisciplineCount {
  status: string;
  discipline: string;
  count: number;
}

@Component({
  selector: 'app-activities-by-status-discipline',
  templateUrl: './activities-by-status-discipline.component.html',
  styleUrls: ['./activities-by-status-discipline.component.css']
})
export class ActivitiesByStatusDisciplineComponent implements OnInit, OnChanges, OnDestroy {
  @Input() projectId!: string;
  public nothingToShow: boolean = false;
  public isLoading: boolean = true;
  public errorMessage: string | null = null;
  private langChangeSubscription: any;
  private lastData: StatusDisciplineCount[] = [];

  // Chart configuration
  public barChartData!: ChartConfiguration<'bar'>['data'];
  public barChartType: ChartConfiguration<'bar'>['type'] = 'bar';
  public barChartOptions: ChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        stacked: true,
      },
      y: {
        stacked: true,
        beginAtZero: true
      }
    },
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
      title: {
        display: true,
        text: this.translateService.instant('ACTIVITIES_BY_STATUS_DISCIPLINE')
      },
      tooltip: {
        callbacks: {
          label: (context) => {
            const label = context.dataset.label || '';
            const value = context.parsed.y;
            return `${label}: ${value}`;
          }
        }
      }
    }
  };

  constructor(
    private dashboardService: DashboardService,
    private chartColorsService: ChartColorsService,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    // Subscribe to language changes to update chart title
    this.langChangeSubscription = this.translateService.onLangChange.subscribe(() => {
      this.updateChartTitle();
      if (this.barChartData && this.lastData.length > 0) {
        this.processChartData(this.lastData);
      }
    });
    
    if (this.projectId) {
      this.fetchData();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['projectId']?.currentValue !== changes['projectId']?.previousValue) {
      this.fetchData();
    }
  }

  fetchData(): void {
    if (!this.projectId) {
      console.warn('Cannot fetch activity counts: projectId is null or undefined');
      this.nothingToShow = true;
      this.isLoading = false;
      return;
    }

    this.isLoading = true;
    this.errorMessage = null;

    this.dashboardService.getActivityCountGroupedByStatusAndDiscipline(this.projectId).subscribe({
      next: (data: StatusDisciplineCount[]) => {
        this.lastData = data; // Store the data for language changes
        this.processChartData(data);
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Error fetching activity counts by status and discipline:', err);
        this.errorMessage = this.translateService.instant('ERROR_LOADING_DATA');
        this.nothingToShow = true;
        this.isLoading = false;
      }
    });
  }

  private updateChartTitle(): void {
    if (this.barChartOptions && this.barChartOptions.plugins && this.barChartOptions.plugins.title) {
      this.barChartOptions.plugins.title.text = this.translateService.instant('ACTIVITIES_BY_STATUS_DISCIPLINE');
    }
  }

  private processChartData(data: StatusDisciplineCount[]): void {
    if (!data || data.length === 0) {
      this.nothingToShow = true;
      return;
    }

    // Extract unique disciplines and statuses
    const disciplines = [...new Set(data.map(item => item.discipline))].sort();
    const statuses = Object.values(Status);
    
    // Create datasets for each status
    const datasets = statuses.map(status => {
      const statusData = disciplines.map(discipline => {
        const match = data.find(item => item.status === status && item.discipline === discipline);
        return match ? match.count : 0;
      });

      // Get colors from the palette based on status
      const colorPalette = this.chartColorsService.getColorPalette();
      let backgroundColor: string;
      
      // Assign colors based on status
      switch(status) {
        case 'NOT_STARTED':
          backgroundColor = colorPalette['yellow'] || '#FFEE00';
          break;
        case 'IN_PROGRESS':
          backgroundColor = colorPalette['blue'] || '#009FE4';
          break;
        case 'COMPLETED':
          backgroundColor = colorPalette['green'] || '#00BB7E';
          break;
        default:
          backgroundColor = colorPalette['orange'] || '#FF7F41';
      }
      
      return {
        data: statusData,
        label: this.translateService.instant(`STATUS_VALUES.${status}`),
        backgroundColor: backgroundColor,
        hoverBackgroundColor: backgroundColor,
        borderColor: colorPalette['black'] || '#000000',
        borderWidth: 1
      };
    });

    // Filter out datasets with all zeros
    const filteredDatasets = datasets.filter(dataset => 
      dataset.data.some(value => value > 0)
    );

    this.nothingToShow = filteredDatasets.length === 0;

    this.barChartData = {
      labels: disciplines,
      datasets: filteredDatasets
    };
  }

  ngOnDestroy(): void {
    // Unsubscribe to prevent memory leaks
    if (this.langChangeSubscription) {
      this.langChangeSubscription.unsubscribe();
    }
  }
}
