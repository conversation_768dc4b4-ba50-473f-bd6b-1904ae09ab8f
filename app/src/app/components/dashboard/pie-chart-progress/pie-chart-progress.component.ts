import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ChartConfiguration, ChartOptions } from 'chart.js';
import { Project, Status } from 'src/app/graphql/generated';
import { ChartColorsService } from 'src/app/services/chartColors.service';
import { DashboardService } from 'src/app/services/dashboard.service';

@Component({
  selector: 'app-dashboard-piechartprogress',
  templateUrl: './pie-chart-progress.component.html',
  styleUrls: ['./pie-chart-progress.component.css'],
})
export class PieChartProgressComponent implements OnInit, OnChanges {

  @Input() projectId!: string;
  project!: Project;
  activityCounts: Record<Status, number> = {} as Record<Status, number>;
  public nothingToShow: boolean = false;

  public pieChartData!: ChartConfiguration<'pie'>['data'];
  public pieChartType: ChartConfiguration<'pie'>['type'] = 'pie';
  public pieChartColors = this.chartColorsService.getPieChartColors();
  public pieChartLegend = true;
  public pieChartOptions: ChartOptions = {
    responsive: true,
    animation: {
      duration: 0,
    },
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: (tooltipItem) => `${tooltipItem.label}: ${tooltipItem.raw}`,
        },
      },
    },
  };

  constructor(
    private readonly chartColorsService: ChartColorsService,
    private translateService: TranslateService,
    private dashboardService: DashboardService
  ) {}

  ngOnInit(): void {
    if (this.projectId) {
      this.fetchActivitiesGroupedByStatus();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['projectId']?.currentValue !== changes['projectId']?.previousValue) {
      this.fetchActivitiesGroupedByStatus();
    }
  }

  fetchActivitiesGroupedByStatus(): void {
    // Check if projectId is valid before proceeding
    if (!this.projectId) {
      console.warn('Cannot fetch activity counts: projectId is null or undefined');
      this.updateChartData({} as Record<Status, number>);
      this.nothingToShow = true;
      return;
    }

    this.dashboardService.getCountByStatus(this.projectId).subscribe({
      next: (statusCounts: { status: Status; count: number }[]) => {
        const activityCounts: Record<Status, number> = {} as Record<Status, number>;
        statusCounts.forEach(({ status, count }) => {
          activityCounts[status] = count;
        });
        this.updateChartData(activityCounts);
      },
      error: (err) => {
        console.error('Error fetching Activity counts:', err);
        // Handle error by showing empty chart
        this.updateChartData({} as Record<Status, number>);
        this.nothingToShow = true;
      }
    });
    
  }

  private updateChartData(activityCounts: Record<Status, number>): void {
    const statuses = Object.values(Status);

    // Create an array of counts for all statuses.
    const counts = statuses.map((status) => activityCounts[status] || 0);

    // Set the boolean: if all counts are 0 (or the object is empty) then nothing to show.
    this.nothingToShow = counts.every(count => count === 0);

    // Translate status labels
    const translatedLabels = statuses.map((status) =>
      this.translateService.instant(`STATUS_VALUES.${status}`)
    );

    const data = statuses.map((status) => activityCounts[status] || 0);

    this.pieChartData = {
      labels: translatedLabels,
      datasets: [
        {
          data,
          backgroundColor: this.pieChartColors.backgroundColor,
          hoverBackgroundColor: this.pieChartColors.hoverBackgroundColor,
        },
      ],
    };
  }
}
