<h2 mat-dialog-title>{{ 'MANAGER_VIEW.SELECT_COLUMNS' | translate }}</h2>

<mat-dialog-content>
  <mat-selection-list [(ngModel)]="selectedColumns">
    <mat-list-option *ngFor="let column of data.availableColumns" 
                     [value]="column"
                     [selected]="isSelected(column)">
      {{ column | titlecase }}
    </mat-list-option>
  </mat-selection-list>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-button (click)="cancel()">
    {{ 'COMMON.CANCEL' | translate }}
  </button>
  <button mat-raised-button color="primary" (click)="save()">
    {{ 'COMMON.SAVE' | translate }}
  </button>
</mat-dialog-actions>