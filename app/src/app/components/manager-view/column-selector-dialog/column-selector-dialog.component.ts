import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

export interface ColumnSelectorData {
  availableColumns: string[];
  selectedColumns: string[];
}

@Component({
  selector: 'app-column-selector-dialog',
  templateUrl: './column-selector-dialog.component.html',
  styleUrls: ['./column-selector-dialog.component.css']
})
export class ColumnSelectorDialogComponent {
  selectedColumns: string[];

  constructor(
    public dialogRef: MatDialogRef<ColumnSelectorDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ColumnSelectorData
  ) {
    this.selectedColumns = [...data.selectedColumns];
  }

  toggleColumn(column: string): void {
    const index = this.selectedColumns.indexOf(column);
    if (index === -1) {
      this.selectedColumns.push(column);
    } else {
      this.selectedColumns.splice(index, 1);
    }
  }

  isSelected(column: string): boolean {
    return this.selectedColumns.includes(column);
  }

  save(): void {
    this.dialogRef.close(this.selectedColumns);
  }

  cancel(): void {
    this.dialogRef.close();
  }
}