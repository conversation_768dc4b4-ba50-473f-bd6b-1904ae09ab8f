.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.search-mode-indicator {
  background-color: #e3f2fd;
  border-radius: 4px;
  padding: 8px 16px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.table-container.hidden { display: none; }

.search-mode-indicator p {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.no-activities-message {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  text-align: center;
  margin-bottom: 16px;
}

.project-select {
  width: 300px; /* Adjust as needed for the dropdown */
}

.filter-panel {
  margin-bottom: 16px;
}

.filter-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.date-range {
  display: flex;
  gap: 16px;
}

.date-range mat-form-field {
  flex: 1;
}

.filter-actions-buttons {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 8px;
}

.filter-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

button[mat-icon-button] {
  margin-left: auto; /* Optional: Push the actions dropdown to the far right */
}

.table-container {
  overflow-x: auto;
  width: 100%;
}

table {
  width: 100%; /* Ensure table takes up full width inside its container */
}

.clickable-row {
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}

.clickable-row:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.clickable-row.selected {
  background-color: #d3ebf4;
  /* or use any highlight color you prefer */
}

/* Review Status Styles */
.review-status {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-ok {
  color: #4caf50;
}

.status-review {
  color: #ff9800;
}

.status-rejected {
  color: #f44336;
}

/* Status icons styling */
.status-completed {
  color: green;
  vertical-align: middle;
  margin-right: 4px;
  font-size: 18px;
}

.status-in-progress {
  color: #2196f3; /* Blue */
  vertical-align: middle;
  margin-right: 4px;
  font-size: 18px;
}

.status-not-started {
  color: #757575; /* Gray */
  vertical-align: middle;
  margin-right: 4px;
  font-size: 18px;
}

/* Completion column styles */
.completion-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-bar-container {
  width: 100%;
  height: 4px;
  background-color: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #2196f3; /* Blue */
  border-radius: 2px;
}
