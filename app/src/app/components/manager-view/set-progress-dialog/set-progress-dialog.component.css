/* Apply internal margins for the modal content */
mat-dialog-content {
  padding: 24px; /* Adds margin inside the modal */
}

/* Progress bar container styling */
.progress-bar-container {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

/* Label styling */
.progress-label {
  font-weight: bold;
  margin-left: 8px;
}

/* Progress buttons container */
.progress-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

/* Highlight the active button */
button.active {
  border: 2px solid #3f51b5; /* Primary color */
  background-color: rgba(63, 81, 181, 0.1); /* Light primary color */
}

/* Custom progress input styling */
.custom-progress {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
  padding: 16px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.03);
}

.custom-progress mat-form-field {
  width: 100%;
}

/* Ensure progress bar stretches as needed */
mat-progress-bar {
  flex: 1;
}

/* Dialog actions alignment */
mat-dialog-actions {
  padding: 16px 24px; /* Adds padding around action buttons */
  justify-content: flex-end; /* Aligns buttons to the right */
}

/* Progress color indicators */
mat-progress-bar.mat-primary {
  --mdc-linear-progress-active-indicator-color: #4caf50;
}

mat-progress-bar.mat-accent {
  --mdc-linear-progress-active-indicator-color: #ff9800;
}

mat-progress-bar.mat-warn {
  --mdc-linear-progress-active-indicator-color: #f44336;
}
