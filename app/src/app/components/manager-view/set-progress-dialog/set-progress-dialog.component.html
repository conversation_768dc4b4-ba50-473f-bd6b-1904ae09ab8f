<h2 mat-dialog-title>{{ 'SET_PROGRESS.TITLE' | translate }}</h2>

<mat-dialog-content>
  <!-- Progress Bar -->
  <div class="progress-bar-container">
    <mat-progress-bar 
      mode="determinate" 
      [value]="currentProgress"
      [color]="getProgressColor()">
    </mat-progress-bar>
    <span class="progress-label">{{ currentProgress }}% {{ 'COMPLETE' | translate }}</span>
  </div>

  <!-- Progress Buttons -->
  <div class="progress-buttons">
    <button mat-stroked-button 
            *ngFor="let value of progressValues" 
            (click)="setProgress(value)"
            [class.active]="currentProgress === value">
      {{ value }}%
    </button>
    <button mat-stroked-button (click)="setCustomProgress()">
      {{ 'CUSTOM' | translate }}
    </button>
  </div>

  <!-- Custom Progress Input -->
  <div *ngIf="customProgressMode" class="custom-progress">
    <mat-form-field appearance="outline">
      <mat-label>{{ 'SET_PROGRESS.CUSTOM_PROGRESS_LABEL' | translate }}</mat-label>
      <input matInput 
             type="number" 
             [(ngModel)]="customProgress" 
             (ngModelChange)="onCustomProgressChange()"
             min="0" 
             max="100" 
             step="1" />
      <mat-hint>Enter a value between 0 and 100</mat-hint>
    </mat-form-field>
    <button mat-raised-button color="primary" (click)="saveCustomProgress()" [disabled]="customProgress < 0 || customProgress > 100">
      {{ 'SET_PROGRESS.SAVE_CUSTOM' | translate }}
    </button>
  </div>
</mat-dialog-content>

<mat-dialog-actions>
  <button mat-button (click)="close()">{{ 'SET_PROGRESS.CANCEL' | translate }}</button>
  <button mat-raised-button color="primary" (click)="save()" [disabled]="!isValid()">
    {{ 'SET_PROGRESS.SAVE' | translate }}
  </button>
</mat-dialog-actions>
