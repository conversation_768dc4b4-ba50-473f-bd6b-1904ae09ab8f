import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-set-progress-dialog',
  templateUrl: './set-progress-dialog.component.html',
  styleUrls: ['./set-progress-dialog.component.css']
})
export class SetProgressDialogComponent {
  progressValues = [0, 25, 50, 75, 100];
  currentProgress: number = 0;
  customProgressMode: boolean = false;
  customProgress: number = 0;

  constructor(
    public dialogRef: MatDialogRef<SetProgressDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.currentProgress = this.data.initialProgress || 0;
  }

  getProgressColor(): string {
    if (this.currentProgress === 100) return 'primary';
    if (this.currentProgress > 0) return 'accent';
    return 'warn';
  }

  setProgress(value: number): void {
    this.currentProgress = value;
    this.customProgressMode = false;
  }

  setCustomProgress(): void {
    // Initialize custom progress with current progress value
    this.customProgress = this.currentProgress;
    this.customProgressMode = true;
  }

  saveCustomProgress(): void {
    // Validate the custom progress value
    if (this.customProgress < 0) {
      this.customProgress = 0;
    } else if (this.customProgress > 100) {
      this.customProgress = 100;
    }
    
    this.currentProgress = this.customProgress;
    this.customProgressMode = false;
  }
  
  // Update progress bar in real-time as user types
  onCustomProgressChange(): void {
    // Only update if the value is valid
    if (this.customProgress >= 0 && this.customProgress <= 100) {
      this.currentProgress = this.customProgress;
    }
  }

  isValid(): boolean {
    return this.currentProgress >= 0 && this.currentProgress <= 100;
  }

  save(): void {
    this.dialogRef.close(this.currentProgress);
  }

  close(): void {
    this.dialogRef.close(); // Closes the dialog
  }
}
