import { Component, Input, Output, EventEmitter } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { firstValueFrom } from 'rxjs';
import { Activity, Status, UpdateActivityInput } from 'src/app/graphql/generated';
import { ActivityService } from 'src/app/services/activity.service';
import { SetProgressDialogComponent } from '../set-progress-dialog/set-progress-dialog.component';
import { SetStatusDialogComponent } from '../set-status-dialog/set-status-dialog.component';

@Component({
  selector: 'app-bulk-actions',
  templateUrl: './bulk-actions.component.html',
  styleUrls: ['./bulk-actions.component.css']
})
export class BulkActionsComponent {
  @Input() selection: Activity[] = [];
  @Output() bulkActionCompleted = new EventEmitter<void>();

  constructor(private dialog: MatDialog, private activityService: ActivityService) {}

  async updateProgress(): Promise<void> {
    // Calculate average progress if multiple activities are selected
    let initialProgress = 0;
    if (this.selection.length > 0) {
      const totalProgress = this.selection.reduce((sum, activity) => {
        return sum + (activity.percentComplete ? activity.percentComplete * 100 : 0);
      }, 0);
      initialProgress = Math.round(totalProgress / this.selection.length);
    }

    const dialogRef = this.dialog.open(SetProgressDialogComponent, {
      width: '800px',
      height: '600px',
      data: { 
        selectedActivities: this.selection,
        initialProgress: initialProgress
      }
    });
    const result = await firstValueFrom(dialogRef.afterClosed());
    if (result === undefined) { return; }

    const input: UpdateActivityInput[] = this.selection.map(activity => ({
      id: activity.id,
      percentComplete: result / 100,
    }));

    console.log('Updating activities with progress:', result, '%');

    this.activityService.updateMultipleActivities(input).subscribe({
      next: () => {
        console.log('Activities updated successfully');
        this.bulkActionCompleted.emit();
      },
      error: (err) => {
        console.error('Error updating activities:', err);
      }
    });
  }

  async updateStatus(): Promise<void> {
    const dialogRef = this.dialog.open(SetStatusDialogComponent, {
      width: '800px',
      height: '600px',
      data: { 
        selectedActivities: this.selection,
        currentStatus: this.selection[0]?.status,
        currentReviewStatus: this.selection[0]?.reviewStatus
      }
    });
    const result = await firstValueFrom(dialogRef.afterClosed());
    if (result === undefined) { return; }

    const input: UpdateActivityInput[] = this.selection.map(activity => ({
      id: activity.id,
      status: result.status,
      reviewStatus: result.reviewStatus
    }));

    this.activityService.updateMultipleActivities(input).subscribe({
      next: () => {
        console.log('Activities updated successfully');
        this.bulkActionCompleted.emit();
      },
      error: (err) => {
        console.error('Error updating activities:', err);
      }
    });
  }
}
