<div class="activity-search">
  <mat-form-field>
    <mat-label>Search Activity by ID</mat-label>
    <input matInput [(ngModel)]="searchId" placeholder="Enter activity ID" (keyup.enter)="searchActivity()">
  </mat-form-field>
  <button mat-raised-button color="primary" [disabled]="searching" (click)="searchActivity()">
    <mat-icon>search</mat-icon>
    Search
  </button>
  <button mat-button color="accent" (click)="onClearSearch()" *ngIf="searchId">
    <mat-icon>clear</mat-icon>
    Clear
  </button>
</div>
