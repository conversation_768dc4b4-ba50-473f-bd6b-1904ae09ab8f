import { Component, EventEmitter, Output } from '@angular/core';
import { Activity } from 'src/app/graphql/generated';
import { ActivityService } from 'src/app/services/activity.service';

@Component({
  selector: 'app-activity-search',
  templateUrl: './activity-search.component.html',
  styleUrls: ['./activity-search.component.css']
})
export class ActivitySearchComponent {
  @Output() activityFound = new EventEmitter<Activity>();
  @Output() searchError = new EventEmitter<string>();
  @Output() clearSearch = new EventEmitter<void>();
  
  searchId: string = '';
  searching = false;
  
  constructor(private activityService: ActivityService) {}
  
  searchActivity() {
    if (!this.searchId.trim()) return;
    
    this.searching = true;
    this.activityService.getActivity(this.searchId)
      .subscribe({
        next: (activity) => {
          this.activityFound.emit(activity);
          this.searching = false;
        },
        error: (error) => {
          this.searchError.emit(error.message || 'Activity not found');
          this.searching = false;
        }
      });
  }

  onClearSearch() {
    this.searchId = '';
    this.clearSearch.emit();
  }
}
