/* Apply internal margins for the modal content */
mat-dialog-content {
  padding: 24px; /* Adds margin inside the modal */
}

/* Status buttons container */
.status-buttons, .review-status-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

/* Highlight the active button */
button.active {
  border: 2px solid #3f51b5; /* Primary color */
  background-color: rgba(63, 81, 181, 0.1); /* Light primary color */
}

/* Custom status input styling */
.custom-status {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

/* Dialog actions alignment */
mat-dialog-actions {
  padding: 16px 24px; /* Adds padding around action buttons */
  justify-content: flex-end; /* Aligns buttons to the right */
}

/* Review Status Button Styles */
.status-ok {
  color: #4caf50;
}

.status-ok.active {
  border-color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
}

.status-review {
  color: #ff9800;
}

.status-review.active {
  border-color: #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
}

.status-rejected {
  color: #f44336;
}

.status-rejected.active {
  border-color: #f44336;
  background-color: rgba(244, 67, 54, 0.1);
}

/* Button with icon styling */
button mat-icon {
  margin-right: 4px;
  vertical-align: middle;
}
