import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ReviewStatus, Status } from 'src/app/graphql/generated';

@Component({
  selector: 'app-set-status-dialog',
  templateUrl: './set-status-dialog.component.html',
  styleUrls: ['./set-status-dialog.component.css']
})
export class SetStatusDialogComponent {
  // Get all values from Status enum except "AUTO_CLOSED"
  // statusValues: string[] = Object.values(Status).filter(
  //   (value) => value !== Status.AutoClosed
  // );
  statusValues: string[] = Object.values(Status);
  reviewStatusValues: string[] = Object.values(ReviewStatus);
  
  currentStatus: string = '';
  currentReviewStatus: string = '';
  customStatusMode: boolean = false;
  customStatus: string = '';

  constructor(
    public dialogRef: MatDialogRef<SetStatusDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.currentStatus = data.currentStatus || this.statusValues[0];
    this.currentReviewStatus = data.currentReviewStatus || this.reviewStatusValues[0];
  }

  setStatus(status: string): void {
    this.currentStatus = status;
    this.customStatusMode = false;
  }

  setReviewStatus(reviewStatus: string): void {
    this.currentReviewStatus = reviewStatus;
  }

  saveCustomStatus(): void {
    if (this.customStatus) {
      this.currentStatus = this.customStatus;
      this.customStatusMode = false;
    }
  }

  setCustomStatusMode(): void {
    this.customStatusMode = true;
  }

  isValid(): boolean {
    return !!this.currentStatus && !!this.currentReviewStatus;
  }

  save(): void {
    this.dialogRef.close({
      status: this.currentStatus,
      reviewStatus: this.currentReviewStatus
    });
  }

  close(): void {
    this.dialogRef.close();
  }
}
