<h1 mat-dialog-title>{{ 'SET_STATUS.TITLE' | translate }}</h1>

<mat-dialog-content>
  <h3>Status</h3>
  <!-- Status Selection -->
  <div class="status-buttons">
    <button mat-stroked-button 
            *ngFor="let status of statusValues" 
            (click)="setStatus(status)"
            [class.active]="currentStatus === status">
      {{ status | translate }}
    </button>
  </div>

  <!-- Custom Status Input -->
  <div *ngIf="customStatusMode" class="custom-status">
    <mat-form-field appearance="outline">
      <mat-label>{{ 'SET_STATUS.CUSTOM_STATUS_LABEL' | translate }}</mat-label>
      <input matInput [(ngModel)]="customStatus" />
    </mat-form-field>
    <button mat-raised-button color="primary" (click)="saveCustomStatus()" [disabled]="!customStatus">
      {{ 'SET_STATUS.SAVE_CUSTOM' | translate }}
    </button>
  </div>

  <h3>Review Status</h3>
  <!-- Review Status Selection -->
  <div class="review-status-buttons">
    <button mat-stroked-button 
            *ngFor="let reviewStatus of reviewStatusValues" 
            (click)="setReviewStatus(reviewStatus)"
            [class.active]="currentReviewStatus === reviewStatus"
            [ngClass]="{
              'status-ok': reviewStatus === 'OK',
              'status-review': reviewStatus === 'FOR_REVIEW',
              'status-rejected': reviewStatus === 'REJECTED'
            }">
      <mat-icon *ngIf="reviewStatus === 'OK'" color="primary">check_circle</mat-icon>
      <mat-icon *ngIf="reviewStatus === 'FOR_REVIEW'" color="accent">pending</mat-icon>
      <mat-icon *ngIf="reviewStatus === 'REJECTED'" color="warn">cancel</mat-icon>
      {{ reviewStatus | translate }}
    </button>
  </div>
</mat-dialog-content>

<mat-dialog-actions>
  <button mat-button (click)="close()">{{ 'SET_STATUS.CANCEL' | translate }}</button>
  <button mat-raised-button color="primary" (click)="save()" [disabled]="!isValid()">
    {{ 'SET_STATUS.SAVE' | translate }}
  </button>
</mat-dialog-actions>
