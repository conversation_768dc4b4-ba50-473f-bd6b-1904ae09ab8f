<!-- Show a spinner, but keep table in DOM (just hidden) -->
<div [style.display]="loading ? 'block' : 'none'" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>{{ 'LOADING_MESSAGE' | translate }}</p>
</div>

<!-- Hide or show the table with CSS instead of *ngIf -->
<div class="manager-view" [style.display]="loading ? 'none' : 'block'">

    <!-- Title and Select Project -->
    <div class="header-container">
        <h1>{{ 'MANAGER_VIEW.TITLE' | translate }}</h1>
        <app-project-selector class="project-select" (projectSelected)="onProjectChange($event)"></app-project-selector>
    </div>

    <!-- Activity Search Component -->
    <app-activity-search (activityFound)="handleActivityFound($event)" (searchError)="handleSearchError($event)"
        (clearSearch)="clearSearch()">
    </app-activity-search>

    <!-- Search Mode Indicator -->
    <div *ngIf="isSearchMode" class="search-mode-indicator">
        <p>Showing search result. <button mat-button color="primary" (click)="clearSearch()">Return to all
                activities</button></p>
    </div>

    <!-- Show message when no activities exist -->
    <div *ngIf="!activities || activities.length === 0" class="no-activities-message">
        <p>No activities to display. Please select another project</p>
    </div>

    <!-- Filter Panel -->
    <div class="filter-panel">
        <mat-expansion-panel [(expanded)]="filterExpanded">
            <mat-expansion-panel-header>
                <mat-panel-title>
                    <mat-icon>filter_list</mat-icon>
                    Advanced Filters
                </mat-panel-title>
            </mat-expansion-panel-header>

            <div class="filter-form">
                <!-- Status Filter -->
                <mat-form-field>
                    <mat-label>Status</mat-label>
                    <mat-select [(ngModel)]="statusFilter">
                        <mat-option [value]="null">All</mat-option>
                        <mat-option [value]="'NOT_STARTED'">Not Started</mat-option>
                        <mat-option [value]="'IN_PROGRESS'">In Progress</mat-option>
                        <mat-option [value]="'COMPLETED'">Completed</mat-option>
                    </mat-select>
                </mat-form-field>

                <!-- Unable to Work Filter -->
                <mat-form-field>
                    <mat-label>Unable to Work</mat-label>
                    <mat-select [(ngModel)]="unableToWorkFilter">
                        <mat-option [value]="null">All</mat-option>
                        <mat-option [value]="true">Yes</mat-option>
                        <mat-option [value]="false">No</mat-option>
                    </mat-select>
                </mat-form-field>

                <!-- Work Order Filter -->
                <mat-form-field>
                    <mat-label>Work Order</mat-label>
                    <input matInput [(ngModel)]="workorderFilter" placeholder="Filter by work order">
                </mat-form-field>

                <!-- Planned Start Date Range -->
                <div class="date-range">
                    <mat-form-field>
                        <mat-label>Planned Start From</mat-label>
                        <input matInput [matDatepicker]="startFromPicker" [(ngModel)]="plannedStartDateRange.from">
                        <mat-datepicker-toggle matSuffix [for]="startFromPicker"></mat-datepicker-toggle>
                        <mat-datepicker #startFromPicker></mat-datepicker>
                    </mat-form-field>

                    <mat-form-field>
                        <mat-label>Planned Start To</mat-label>
                        <input matInput [matDatepicker]="startToPicker" [(ngModel)]="plannedStartDateRange.to">
                        <mat-datepicker-toggle matSuffix [for]="startToPicker"></mat-datepicker-toggle>
                        <mat-datepicker #startToPicker></mat-datepicker>
                    </mat-form-field>
                </div>

                <!-- Planned Finish Date Range -->
                <div class="date-range">
                    <mat-form-field>
                        <mat-label>Planned Finish From</mat-label>
                        <input matInput [matDatepicker]="finishFromPicker" [(ngModel)]="plannedFinishDateRange.from">
                        <mat-datepicker-toggle matSuffix [for]="finishFromPicker"></mat-datepicker-toggle>
                        <mat-datepicker #finishFromPicker></mat-datepicker>
                    </mat-form-field>

                    <mat-form-field>
                        <mat-label>Planned Finish To</mat-label>
                        <input matInput [matDatepicker]="finishToPicker" [(ngModel)]="plannedFinishDateRange.to">
                        <mat-datepicker-toggle matSuffix [for]="finishToPicker"></mat-datepicker-toggle>
                        <mat-datepicker #finishToPicker></mat-datepicker>
                    </mat-form-field>
                </div>

                <!-- Filter Actions -->
                <div class="filter-actions-buttons">
                    <button mat-raised-button color="primary" (click)="applyFilters()">Apply Filters</button>
                    <button mat-button (click)="resetFilters(); applyFilters()">Reset</button>
                </div>
            </div>
        </mat-expansion-panel>
    </div>

    <!-- Filter and Actions -->
    <div class="filter-actions">
        <!-- Filter Input -->
        <mat-form-field>
            <mat-label>{{ 'MANAGER_VIEW.FILTER_LABEL' | translate }}</mat-label>
            <input matInput (keyup)="applyFilter($event)"
                [placeholder]="'MANAGER_VIEW.FILTER_PLACEHOLDER' | translate" />
        </mat-form-field>

        <!-- Actions Dropdown -->
        <button mat-icon-button [matMenuTriggerFor]="actionsMenu" aria-label="Actions menu">
            <mat-icon>more_vert</mat-icon>
        </button>
        <mat-menu #actionsMenu="matMenu">
            <button mat-menu-item (click)="exportToExcel()">
                <mat-icon>download</mat-icon>
                <span>{{ 'MANAGER_VIEW.EXPORT_TO_EXCEL' | translate }}</span>
            </button>
            <button mat-menu-item (click)="openColumnSelector()">
                <mat-icon>view_column</mat-icon>
                <span>{{ 'MANAGER_VIEW.SELECT_COLUMNS' | translate }}</span>
            </button>
        </mat-menu>
    </div>


    <!-- Table and related UI elements -->
    <div class="table-container" [class.hidden]="!activities.length">
        <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">

            <!-- Checkbox Column (Only shown if isAuthorized is true) -->
            <ng-container *ngIf="isAuthorized" matColumnDef="select">
                <th mat-header-cell *matHeaderCellDef>
                    <mat-checkbox (change)="$event ? masterToggle() : null" [checked]="isAllSelected()"
                        [indeterminate]="isAnySelected() && !isAllSelected()">
                    </mat-checkbox>
                </th>
                <td mat-cell *matCellDef="let row">
                    <mat-checkbox (change)="$event ? toggleSelection(row) : null" [checked]="selection.includes(row)">
                    </mat-checkbox>
                </td>
            </ng-container>

            <!-- Data Columns -->
            <ng-container *ngFor="let column of displayedColumns" [matColumnDef]="column">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> {{ column | titlecase }} </th>
                <td mat-cell *matCellDef="let element">
                    <!-- For completion column -->
                    <ng-container *ngIf="column === 'completion'; else notPercentage">
                        <div class="completion-container">
                            <div>{{ element.percentComplete | percent }}</div>
                            <div class="progress-bar-container" *ngIf="element.lastPercentComplete !== null && element.lastPercentComplete !== undefined">
                                <div class="progress-bar" 
                                     [style.width]="(element.lastPercentComplete * 100) + '%'"
                                     [matTooltip]="'Previous Progress: ' + (element.lastPercentComplete | percent)">
                                </div>
                            </div>
                        </div>
                    </ng-container>

                    <!-- Handle other columns -->
                    <ng-template #notPercentage>
                        <ng-container *ngIf="column === 'unableToWork'; else checkStatus">
                            <mat-icon *ngIf="element[column] === true" color="warn">warning</mat-icon>
                            <!-- Nothing is rendered if unableToWork is false -->
                        </ng-container>
                        <ng-template #checkStatus>
                            <ng-container *ngIf="column === 'status'; else checkReviewStatus">
                                <div [matTooltip]="element[column]">
                                    <!-- COMPLETED status -->
                                    <mat-icon *ngIf="element[column] === 'COMPLETED'" class="status-completed">
                                        check_circle
                                    </mat-icon>

                                    <!-- IN_PROGRESS status -->
                                    <mat-icon *ngIf="element[column] === 'IN_PROGRESS'" class="status-in-progress">
                                        pending
                                    </mat-icon>

                                    <!-- NOT_STARTED status -->
                                    <mat-icon *ngIf="element[column] === 'NOT_STARTED'" class="status-not-started">
                                        schedule
                                    </mat-icon>

                                </div>
                            </ng-container>
                        </ng-template>
                        <ng-template #checkReviewStatus>
                            <ng-container *ngIf="column === 'reviewStatus'; else checkDates">
                                <span [ngClass]="{'review-status': true, 
                                                 'status-ok': element[column] === 'OK',
                                                 'status-review': element[column] === 'FOR_REVIEW',
                                                 'status-rejected': element[column] === 'REJECTED'}">
                                    <mat-icon *ngIf="element[column] === 'OK'" color="primary">check_circle</mat-icon>
                                    <mat-icon *ngIf="element[column] === 'FOR_REVIEW'" color="accent">pending</mat-icon>
                                    <mat-icon *ngIf="element[column] === 'REJECTED'" color="warn">cancel</mat-icon>
                                    {{ element[column] }}
                                </span>
                            </ng-container>
                        </ng-template>
                        <ng-template #checkDates>
                            <ng-container
                                *ngIf="column === 'actualStartDate' || column === 'actualFinishDate'; else checkWorkOrder">
                                {{ element[column] | date:'medium' }}
                            </ng-container>
                        </ng-template>
                        <ng-template #checkWorkOrder>
                            <ng-container *ngIf="column === 'WorkOrder'; else regularContent">
                                {{ element['workorderNo'] }}
                            </ng-container>
                        </ng-template>
                        <ng-template #regularContent>
                            {{ element[column] }}
                        </ng-template>
                    </ng-template>
                </td>
            </ng-container>

            <!-- Header Row -->
            <tr mat-header-row *matHeaderRowDef="headerColumns"></tr>

            <!-- Data Rows -->
            <tr mat-row *matRowDef="let row; columns: headerColumns" (click)="onRowClick(row)" class="clickable-row"
                [class.selected]="row === clickedRow">
            </tr>
        </table>
    </div>

    <!-- Paginator (only show if activities exist) -->
    <!-- <mat-paginator *ngIf="activities && activities.length > 0" [length]="totalActivities" [pageSize]="10"
        [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons (page)="onPageChange($event)">
    </mat-paginator> -->
    <mat-paginator [style.display]="activities.length ? 'flex' : 'none'" [length]="totalActivities" [pageSize]="limit"
        [pageSizeOptions]="[5,10,20,50]" showFirstLastButtons>
    </mat-paginator>

    <!-- Bulk Actions -->
    <!-- <div class="bulk-actions" *ngIf="selection.length > 0">
        <h3>Bulk Actions</h3>
        <button mat-raised-button color="primary" (click)="updateProgress()">Set Progress</button>
        <button mat-raised-button color="accent" (click)="updateStatus()">Set Status</button>
    </div> -->

    <!-- Bulk Actions Component -->
    <app-bulk-actions *ngIf="selection.length > 0" [selection]="selection" (bulkActionCompleted)="loadActivities()">
    </app-bulk-actions>

    <!-- Selected Activity -->
    <div *ngIf="clickedRow">
        <app-activity [id]="clickedRow.id"></app-activity>
    </div>
</div>
