import { Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Activity, Project, Status, UpdateActivityInput } from 'src/app/graphql/generated';
import { ProjectService } from 'src/app/services/project.service';
import { ActivityService } from 'src/app/services/activity.service';
import { firstValueFrom } from 'rxjs';
import { StorageService } from 'src/app/services/storage.service';
import { ColumnSelectorDialogComponent, ColumnSelectorData } from './column-selector-dialog/column-selector-dialog.component';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { AuthService } from 'src/app/core/services/auth.service';

@Component({
  selector: 'app-manager-view',
  templateUrl: './manager-view.component.html',
  styleUrls: ['./manager-view.component.css']
})
export class ManagerViewComponent implements OnInit {
  @ViewChild(MatSort) sort!: MatSort;
  @ViewChild(MatPaginator) paginator!: MatPaginator;

  projects: Project[] = [];
  loading = true;
  activities: Activity[] = [];
  error: string | null = null;

  availableColumns: string[] = [
    'id',
    'activityId',
    'name',
    'description',
    'floc',
    'WorkOrder',
    'jobOrderNumber',
    'discipline',
    'scopeNr',
    'scopeId',
    'completion',
    'status',
    'reviewStatus',
    'unableToWork',
    'actualStartDate',
    'actualFinishDate'
  ];
  
  // Columns that are not shown by default but can be selected
  hiddenByDefaultColumns: string[] = [
    'activityId',
    'floc',
    'reviewStatus'
  ];
  
  // Default columns to display (all available columns except those hidden by default)
  defaultColumns: string[] = this.availableColumns.filter(col => !this.hiddenByDefaultColumns.includes(col));
  
  // Flag to track if we're in search mode
  isSearchMode = false;
  displayedColumns: string[] = this.defaultColumns;
  headerColumns: string[] = [];
  selection: Activity[] = []; // Array to store selected rows

  // Authorization flag is calculated once per project change
  isAuthorized = false;

  // Pagination management (client-side)
  limit = 10;
  totalActivities = 0;
  currentPage = 0;

  // Filter state
  filterExpanded = false;
  statusFilter: Status | null = null;
  unableToWorkFilter: boolean | null = null;
  workorderFilter: string | null = null;
  plannedStartDateRange: { from: Date | null, to: Date | null } = { from: null, to: null };
  plannedFinishDateRange: { from: Date | null, to: Date | null } = { from: null, to: null };
  
  // Store unfiltered activities for client-side filtering
  unfilteredActivities: Activity[] = [];

  clickedRow?: Activity;
  dataSource = new MatTableDataSource<Activity>();
  selectedProject: Project | null = null; // Replace with project selection logic

  constructor(
    private projectService: ProjectService,
    private dialog: MatDialog,
    private storageService: StorageService,
    private authService: AuthService) { }

  ngOnInit(): void {
    
    // Retrieve column settings from storage
    let storedColumns = this.storageService.getSessionItem('managerViewColumns', this.defaultColumns);
    
    // Filter out any columns that are no longer in availableColumns
    this.displayedColumns = storedColumns.filter(column => this.availableColumns.includes(column));
    
    // If the filtered list is different from the stored list, update storage
    if (storedColumns.length !== this.displayedColumns.length) {
      this.storageService.setSessionItem('managerViewColumns', this.displayedColumns);
    }
    
    
    // Initially update header columns (authorization will be checked on project change)
    this.updateHeaderColumns();
  }

  ngAfterViewInit() {
    this.loading = false;
    
    // Configure sorting behavior
    this.dataSource.sortingDataAccessor = (item, property) => {
      switch(property) {
        case 'WorkOrder':
          return item.workorderNo ? item.workorderNo.toLowerCase() : '';
        case 'completion':
          return item.percentComplete || 0;
        case 'plannedStartDate':
        case 'plannedFinishDate':
        case 'actualStartDate':
        case 'actualFinishDate':
          return item[property] ? new Date(item[property]).getTime() : 0;
        default:
          return item[property] ? 
            (typeof item[property] === 'string' ? item[property].toLowerCase() : item[property]) 
            : '';
      }
    };
    
    // Configure filtering behavior
    this.dataSource.filterPredicate = (data: Activity, filter: string) => {
      const searchTerms = filter.toLowerCase().split(' ');
      
      // Check if all search terms are found in at least one of the searchable fields
      return searchTerms.every(term => {
        return this.displayedColumns.some(column => {
          let value: any;
          
          // Handle special cases for column mapping
          if (column === 'WorkOrder') {
            value = data.workorderNo;
          } else if (column === 'completion') {
            // For completion column, search in both percentComplete and lastPercentComplete
            const percentComplete = data.percentComplete ? String(data.percentComplete) : '';
            const lastPercentComplete = data.lastPercentComplete ? String(data.lastPercentComplete) : '';
            return percentComplete.includes(term) || lastPercentComplete.includes(term);
          } else {
            value = data[column as keyof Activity];
          }
          
          // Convert value to string if it exists
          if (value !== null && value !== undefined) {
            const stringValue = String(value).toLowerCase();
            return stringValue.includes(term);
          }
          
          return false;
        });
      });
    };
    
    // Connect paginator and sort after dataSource is initialized
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  // Helper method to reapply sort to the dataSource
  private reapplySort(): void {
    if (this.sort && this.dataSource) {
      // Store current sort state
      const activeSortHeader = this.sort.active;
      const direction = this.sort.direction;
      
      // Reassign sort to dataSource
      this.dataSource.sort = this.sort;
      
      // If there was an active sort, reapply it
      if (activeSortHeader && direction) {
        this.sort.sort({ id: activeSortHeader, start: direction, disableClear: false });
      }
    }
  }

  async onProjectChange(project: Project): Promise<void> {
    console.log(project);
    // Reset activities and selection when the project changes
    this.selectedProject = project;
    this.activities = [];
    this.unfilteredActivities = [];
    this.currentPage = 0;
    
    // Reset filters when project changes
    this.resetFilters();

    // Extract group names from Group objects for authorization check
    const groupNames = this.selectedProject.managerGroups?.map(group => group.name) || [];

    // Check and cache user authorization for the selected project
    this.isAuthorized = await this.authService.isUserAdminOrInGroups(groupNames);
  
    // Update header columns based on authorization
    this.updateHeaderColumns();
  
    this.loadActivities();
  }

  async openColumnSelector(): Promise<void> {
    const dialogRef = this.dialog.open(ColumnSelectorDialogComponent, {
      data: {
        availableColumns: this.availableColumns,
        selectedColumns: [...this.displayedColumns]
      } as ColumnSelectorData,
      width: '400px'
    });

    const result = await firstValueFrom(dialogRef.afterClosed());
    if (result) {
      this.displayedColumns = result;
      this.storageService.setSessionItem('managerViewColumns', result);
      this.updateHeaderColumns();
      // Reapply sort after columns change
      this.reapplySort();
    }
  }

  loadActivities(): void {
    // If we're in search mode, don't load activities from the project
    if (this.isSearchMode) {
      return;
    }
    
    // Check if a project is selected
    if (!this.selectedProject) {
      console.error('No project selected');
      return;
    }

    this.loading = true;

    // Fetch all activities for the project without pagination or server-side filtering
    this.projectService.getProjectWithActivities(
      this.selectedProject.id, 
      10000, // Use a large limit to get all activities at once
      null,
      {} // No server-side filters
    ).subscribe(
      (result: { items: Activity[], totalCount?: number }) => {
        // Store all unfiltered activities
        this.unfilteredActivities = result.items;
        console.log(result.items.length)
        
        // Apply client-side filtering
        this.applyFilters();
        
        this.loading = false;
        console.log('All activities loaded:', this.unfilteredActivities.length);
      },
      (error) => {
        this.error = 'Error fetching activities.';
        console.error('Error fetching activities:', error);
        this.loading = false;
      }
    );
  }
  
  // Handle activity found from search
  handleActivityFound(activity: Activity): void {
    if (activity) {
      this.isSearchMode = true;
      this.activities = [activity];
      this.dataSource.data = this.activities;
      this.totalActivities = 1;
      
      // Reset pagination if needed
      if (this.paginator) {
        this.paginator.firstPage();
      }
      
      // Ensure sort is applied to the new data
      this.reapplySort();
      
      this.error = null;
    }
  }

  // Handle search error
  handleSearchError(errorMessage: string): void {
    this.error = `Search error: ${errorMessage}`;
    console.error('Activity search error:', errorMessage);
  }

  // Clear search and return to normal view
  clearSearch(): void {
    this.isSearchMode = false;
    this.error = null;
    
    // Reload activities if a project is selected
    if (this.selectedProject) {
      this.loadActivities();
    }
  }

  applyFilters(): void {
    // Reset paginator to first page
    if (this.paginator) {
      this.paginator.firstPage();
    }
    
    // Apply all filters client-side
    let filteredActivities = [...this.unfilteredActivities];
    
    // Apply status filter
    if (this.statusFilter !== null) {
      filteredActivities = filteredActivities.filter(activity => 
        activity.status === this.statusFilter
      );
    }
    
    // Apply unableToWork filter
    if (this.unableToWorkFilter !== null) {
      filteredActivities = filteredActivities.filter(activity => 
        activity.unableToWork === this.unableToWorkFilter
      );
    }
    
    // Apply workorder filter
    if (this.workorderFilter) {
      filteredActivities = filteredActivities.filter(activity => 
        activity.workorderNo && 
        activity.workorderNo.toLowerCase().includes(this.workorderFilter.toLowerCase())
      );
    }
    
    // Apply plannedStartDate range filter
    if (this.plannedStartDateRange.from) {
      filteredActivities = filteredActivities.filter(activity => 
        activity.plannedStartDate && new Date(activity.plannedStartDate) >= this.plannedStartDateRange.from!
      );
    }
    
    if (this.plannedStartDateRange.to) {
      filteredActivities = filteredActivities.filter(activity => 
        activity.plannedStartDate && new Date(activity.plannedStartDate) <= this.plannedStartDateRange.to!
      );
    }
    
    // Apply plannedFinishDate range filter
    if (this.plannedFinishDateRange.from) {
      filteredActivities = filteredActivities.filter(activity => 
        activity.plannedFinishDate && new Date(activity.plannedFinishDate) >= this.plannedFinishDateRange.from!
      );
    }
    
    if (this.plannedFinishDateRange.to) {
      filteredActivities = filteredActivities.filter(activity => 
        activity.plannedFinishDate && new Date(activity.plannedFinishDate) <= this.plannedFinishDateRange.to!
      );
    }
    
    // Update activities and dataSource
    this.activities = filteredActivities;
    this.dataSource.data = this.activities;
    this.totalActivities = this.activities.length;
    
    // Ensure sort is applied to the filtered data
    this.reapplySort();
  }

  resetFilters(): void {
    this.statusFilter = null;
    this.unableToWorkFilter = null;
    this.workorderFilter = null;
    this.plannedStartDateRange = { from: null, to: null };
    this.plannedFinishDateRange = { from: null, to: null };
  }

  onPageChange(event: PageEvent): void {
    this.limit = event.pageSize;
    this.currentPage = event.pageIndex;
    // No need to reload data as pagination is handled client-side by MatTableDataSource
  }

  toggleSelection(row: Activity): void {
    if (this.isAuthorized) {
      const index = this.selection.indexOf(row);
      if (index >= 0) {
        this.selection.splice(index, 1); // Deselect
      } else {
        this.selection.push(row); // Select
      }
    } else {
      alert('You are not authorized to use multi-selection.');
    }
  }

  masterToggle(): void {
    if (this.isAuthorized) {
      if (this.isAllSelected()) {
        this.selection = [];
      } else {
        // Only select items on the current page
        const currentPageData = this.getCurrentPageData();
        this.selection = [...currentPageData];
      }
    } else {
      alert('You are not authorized to use multi-selection.');
    }
  }
  
  // Helper method to get data on the current page
  private getCurrentPageData(): Activity[] {
    if (!this.paginator) {
      return this.dataSource.data;
    }
    
    const startIndex = this.paginator.pageIndex * this.paginator.pageSize;
    const endIndex = startIndex + this.paginator.pageSize;
    return this.dataSource.data.slice(startIndex, endIndex);
  }

  isAllSelected(): boolean {
    const currentPageData = this.getCurrentPageData();
    return this.selection.length > 0 && 
           currentPageData.every(item => this.selection.includes(item));
  }

  isAnySelected(): boolean {
    return this.selection.length > 0;
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value.trim().toLowerCase();
    this.dataSource.filter = filterValue;
    
    // Reset paginator when filtering
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
    
    // Ensure sort is maintained after filtering
    this.reapplySort();
  }

  exportToExcel(): void {
    console.log('Exporting selected rows to Excel:', this.selection);

    const rowsToExport = this.selection;

    if (!rowsToExport || rowsToExport.length === 0) {
      alert('No rows selected to export!');
      return;
    }

    const worksheet = XLSX.utils.json_to_sheet(rowsToExport);

    const workbook = {
      Sheets: { Data: worksheet },
      SheetNames: ['Data']
    };

    const excelBuffer: any = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array'
    });

    const data: Blob = new Blob([excelBuffer], {
      type:
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
    });

    const fileName = `activities_export_${new Date().getTime()}.xlsx`;
    saveAs(data, fileName);
  }

  onRowClick(row: Activity): void {
    this.clickedRow = row;
  }

  updateHeaderColumns(): void {
    // If authorized, prepend a 'select' column to allow selection controls
    this.headerColumns = this.isAuthorized ? ['select', ...this.displayedColumns] : [...this.displayedColumns];
  }
}
