<div class="project-selector-container">
    <mat-form-field class="project-select">
      <mat-label>{{ 'MANAGER_VIEW.SELECT_PROJECT' | translate }}</mat-label>
      <mat-select [(ngModel)]="selectedProject" (ngModelChange)="onProjectChange($event)" [disabled]="loading">
        <mat-option *ngFor="let project of projects" [value]="project">
          {{ project.name }} | {{ project.id }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  
    <div *ngIf="error" class="error-message">
      <p>{{ error }}</p>
    </div>
  </div>
