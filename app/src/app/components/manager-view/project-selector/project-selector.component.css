.project-selector-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.project-select {
  width: 100%;
}

/* Make text in dropdown not overflow */
::ng-deep .mat-option-text {
  white-space: normal;
  line-height: 1.2;
  overflow-wrap: break-word;
}

/* Error message styling */
.error-message {
  color: #f44336;
  margin-top: 8px;
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
  .project-selector-container {
    max-width: 100%;
    padding: 0 10px;
  }
  
  /* Increase touch target size for mobile */
  ::ng-deep .mat-select-trigger {
    min-height: 48px;
    display: flex;
    align-items: center;
  }
}
