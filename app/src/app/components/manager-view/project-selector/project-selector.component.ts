import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { Project } from 'src/app/graphql/generated';
import { ProjectService } from 'src/app/services/project.service';

@Component({
  selector: 'app-project-selector',
  templateUrl: './project-selector.component.html',
  styleUrls: ['./project-selector.component.css']
})
export class ProjectSelectorComponent implements OnInit {
  projects: Project[] = [];
  selectedProject: Project | null = null;
  @Output() projectSelected = new EventEmitter<Project>();
  loading = true;
  error: string | null = null;

  constructor(private projectService: ProjectService) {}

  ngOnInit(): void {
    this.fetchProjects();
  }

  fetchProjects(): void {
    this.projectService.listProjectsShort(100).subscribe({
      next: (data) => {
        this.projects = data.items;
        if (this.projects.length > 0) {
          this.selectedProject = this.projects[0];
          this.projectSelected.emit(this.selectedProject);
        }
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Failed to load projects';
        this.loading = false;
        console.error(err);
      }
    });
  }

  onProjectChange(project: Project): void {
    this.selectedProject = project;
    this.projectSelected.emit(project);
  }
}
