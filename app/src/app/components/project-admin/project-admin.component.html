<!-- project-admin.component.html -->
<h1>{{ 'PROJECT_ADMIN.TITLE' | translate }}</h1>

<mat-form-field>
  <input matInput (keyup)="applyFilter($event)" [placeholder]="'PROJECT_ADMIN.SEARCH' | translate">
</mat-form-field>

<div class="mat-elevation-z8">
  <table mat-table [dataSource]="dataSource" matSort>
    <ng-container matColumnDef="id">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'PROJECT_ADMIN.COLUMNS.ID' | translate }}</th>
      <td mat-cell *matCellDef="let project">{{ project.id }}</td>
    </ng-container>

    <ng-container matColumnDef="name">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'PROJECT_ADMIN.COLUMNS.NAME' | translate }}</th>
      <td mat-cell *matCellDef="let project">{{ project.name }}</td>
    </ng-container>

    <ng-container matColumnDef="status">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'PROJECT_ADMIN.COLUMNS.STATUS' | translate }}</th>
      <td mat-cell *matCellDef="let project">{{ project.status }}</td>
    </ng-container>

    <ng-container matColumnDef="isSyncing">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'PROJECT_ADMIN.COLUMNS.SYNCING' | translate }}</th>
      <td mat-cell *matCellDef="let project">{{ project.isSyncing ? ('PROJECT_ADMIN.YES' | translate) : ('PROJECT_ADMIN.NO' | translate) }}</td>
    </ng-container>

    <ng-container matColumnDef="isVisible">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'PROJECT_ADMIN.COLUMNS.VISIBLE' | translate }}</th>
      <td mat-cell *matCellDef="let project">{{ project.isVisible ? ('PROJECT_ADMIN.YES' | translate) : ('PROJECT_ADMIN.NO' | translate) }}</td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;" 
        (click)="selectProject(row)" 
        [class.selected]="row === selectedRow"></tr>
  </table>

  <!-- Updated paginator with server-side pagination properties -->
  <mat-paginator 
    [length]="totalCount"
    [pageSize]="pageSize" 
    [pageSizeOptions]="[5, 10, 20]"
    (page)="handlePageEvent($event)"
    showFirstLastButtons
    aria-label="Select page of projects">
  </mat-paginator>
  
</div>

<app-project-details 
  [project]="selectedProject"
  (projectChanged)="onProjectChanged($event)">
</app-project-details>
