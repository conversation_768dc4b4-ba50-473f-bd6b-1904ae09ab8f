// project-admin.component.ts
import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { Project, ProjectConnection } from 'src/app/graphql/generated';
import { ProjectService } from 'src/app/services/project.service';
import { MessageService } from 'src/app/core/services/message.service';
import { TranslateService } from 'src/app/core/services/translate.service';

@Component({
  selector: 'app-project-admin',
  templateUrl: './project-admin.component.html',
  styleUrls: ['./project-admin.component.css']
})
export class ProjectAdminComponent implements OnInit, AfterViewInit {
  selectedRow: Project | null = null;
  displayedColumns: string[] = ['id', 'name', 'status', 'isSyncing', 'isVisible'];
  dataSource = new MatTableDataSource<Project>([]);
  selectedProject: Project | null = null;
  
  // Client-side pagination properties
  pageSize: number = 10;
  
  // Server-side pagination properties
  allProjects: Project[] = []; // Store all fetched projects
  nextToken: string | null = null;
  totalCount: number = 0;
  isLoading: boolean = false;
  batchSize: number = 100; // Number of projects to fetch at once
  
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  constructor(
    private projectService: ProjectService, 
    private messageService: MessageService,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.fetchProjects();
  }

  ngAfterViewInit(): void {
    // Set up the paginator after view init
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  fetchProjects(): void {
    this.isLoading = true;
    this.projectService.listProjects(this.batchSize).subscribe((projectConnection: ProjectConnection) => {
      if (projectConnection) {
        // Store all projects for client-side pagination
        this.allProjects = projectConnection.items || [];
        
        // Store pagination info for potential server-side pagination
        this.nextToken = projectConnection.nextToken;
        this.totalCount = projectConnection.totalCount || this.allProjects.length;
        
        // Apply initial client-side pagination
        this.updateDisplayedProjects();
        
        // Configure sorting
        this.dataSource.sort = this.sort;
        this.isLoading = false;
      }
    }, error => {
      this.messageService.showError('Failed to load projects');
      this.isLoading = false;
      console.error(error);
    });
  }

  // Update the displayed projects based on current page
  updateDisplayedProjects(): void {
    // Let the MatTableDataSource handle pagination with the full dataset
    this.dataSource.data = this.allProjects;
  }

  // Handle pagination events
  handlePageEvent(event: PageEvent): void {
    this.pageSize = event.pageSize;
    
    // If we're approaching the end of our cached data and there's more data available
    const currentPage = event.pageIndex;
    const itemsPerPage = event.pageSize;
    const totalItemsNeeded = (currentPage + 1) * itemsPerPage;
    
    if (totalItemsNeeded > this.allProjects.length && this.nextToken) {
      // Fetch more projects from the server
      this.loadMoreProjects();
    }
  }

  // Load more projects when needed
  loadMoreProjects(): void {
    if (!this.nextToken || this.isLoading) return;
    
    this.isLoading = true;
    this.projectService.listProjects(this.batchSize, this.nextToken).subscribe((projectConnection: ProjectConnection) => {
      if (projectConnection) {
        // Add new projects to our cache
        this.allProjects = [...this.allProjects, ...(projectConnection.items || [])];
        
        // Update pagination info
        this.nextToken = projectConnection.nextToken;
        this.totalCount = projectConnection.totalCount || this.allProjects.length;
        
        // Update the displayed projects
        this.updateDisplayedProjects();
        this.isLoading = false;
      }
    }, error => {
      this.messageService.showError('Failed to load more projects');
      this.isLoading = false;
      console.error(error);
    });
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    
    // When filtering, go back to the first page
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  selectProject(project: Project): void {
    this.selectedProject = project;
    this.selectedRow = project;
  }

  // Handle project changes from the project-details component
  onProjectChanged(project: Project): void {
    this.updateProject(project);
  }

  updateProject(project: Project = this.selectedProject): void {
    if (project) {
      this.projectService.updateProject(project.id, {
        isSyncing: project.isSyncing,
        isVisible: project.isVisible !== undefined ? project.isVisible : false,
        managerGroups: project.managerGroups?.map(group => group.id) || [],
        operatorGroups: project.operatorGroups?.map(group => group.id) || [],
        workerGroups: project.workerGroups?.map(group => group.id) || []
      }).subscribe(updatedProject => {
        this.selectedProject = updatedProject;
        this.messageService.showSuccess('Project updated successfully');
        // Refresh projects to ensure we have the latest data
        this.fetchProjects();
      });
    }
  }
}
