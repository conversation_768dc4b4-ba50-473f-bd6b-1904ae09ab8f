.mobile-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.activity-list {
  padding: 16px;
  max-width: 100%;
  box-sizing: border-box;
}

.activity-items-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Flex container for heading + menus side by side */
.header-container {
  display: flex;
  align-items: center;       /* Vertically center items */
  justify-content: space-between; /* Spread out: H1 on the left, menus on the right */
  margin-bottom: 1rem;
  flex-wrap: wrap; /* Allow wrapping on small screens */
  gap: 0.75rem; /* Space between wrapped items */
}

/* Wrap your filter + sort buttons together */
.menus-container {
  display: flex;
  gap: 0.5rem; /* Space between buttons */
}

.activity-list mat-form-field {
  width: 100%;
  margin-bottom: 16px;
}

.filters {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
  width: 100%;
}

.paginator-filter-container {
  margin-bottom: 16px;
}

.activity-count {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
}

.filters-row {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.search-field {
  flex: 1;
  min-width: 200px;
}

.sort-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.3rem;
}

.sort-label {
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.87);
  font-weight: 500;
}

.sort-group {
  display: flex;
}

.mat-button-toggle {
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.87);
}

.mat-button-toggle-checked {
  background-color: #e0e0e0;
}

.mat-button-toggle mat-icon {
  margin-right: 8px;
  font-size: 18px;
  width: 18px;
  height: 18px;
  line-height: 18px;
}

.toggle-group {
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-bottom: 1.3rem;
}

.activity-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  margin-bottom: 12px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.activity-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.activity-info {
  flex: 1;
}

.activity-title {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
  margin-bottom: 8px;
}

.dates {
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
}

.dates span {
  display: block;
  margin-bottom: 4px;
}

.activity-status {
  margin-left: 16px;
}

.spinner-wrapper {
  position: relative;
  width: 40px;
  height: 40px;
}

.spinner-wrapper mat-progress-spinner ::ng-deep circle {
  stroke: #1976d2;
}

.progress-label {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: 500;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: rgba(0, 0, 0, 0.54);
}

.no-results {
  text-align: center;
  padding: 32px;
  color: rgba(0, 0, 0, 0.54);
  background: white;
  border-radius: 8px;
  font-size: 16px;
}

.predecessor-status {
  background-color: green;
  border-radius: 10px;
  padding: 10px 20px;
  width: fit-content;
  color: white;
}

.activity-contractor {
  background-color: #1976d2;
  border-radius: 10px;
  padding: 5px 10px;
  width: fit-content;
  color: white;
  margin-top: 8px;
  margin-bottom: 8px;
  font-size: 14px;
}

.unable-to-work-status {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.unable-to-work-status mat-icon {
  font-size: 20px;
  height: 20px;
  width: 20px;
}

.mat-paginator {
  font-size: 14px;
  background: transparent;
  margin-top: 16px;
  width: 100%;
  overflow-x: auto; /* Allow horizontal scrolling if needed */
}

.mat-paginator-navigation-button {
  min-width: 36px;
  height: 36px;
}

/* Improve touch targets for buttons */
button[mat-icon-button] {
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 1200px) {
  .filters-row {
    flex-direction: column;
    align-items: stretch;
  }

  .search-field,
  .sort-container,
  .toggle-group,
  .sort-group {
    width: 100%;
  }

  .toggle-group {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: 900px) {
  .header-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-container h2 {
    margin-bottom: 0.5rem;
    width: 100%;
  }
  
  .project-select {
    width: 100%;
  }
  
  .menus-container {
    margin-top: 0.5rem;
    width: 100%;
    justify-content: flex-end;
  }
  
  /* Ensure search field is properly sized */
  .search-field {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .sort-container {
    flex-direction: column;
    align-items: flex-start;
  }

  .sort-label {
    margin-bottom: 0.5rem;
  }

  .mat-button-toggle {
    flex: 1;
  }
  
  .activity-list {
    padding: 12px;
  }
  
  .paginator-filter-container {
    display: flex;
    flex-direction: column;
  }
}

@media screen and (max-width: 600px) {
  .mat-paginator,
  .mat-paginator-page-size-label,
  .mat-paginator-range-label,
  .mat-paginator-page-size-select,
  .mat-select-value-text,
  .mat-option {
    font-size: 12px;
  }

  .mat-paginator-navigation-previous,
  .mat-paginator-navigation-next,
  .mat-paginator-navigation-first,
  .mat-paginator-navigation-last {
    font-size: 18px;
  }

  .mat-paginator-navigation-button {
    min-width: 32px;
    height: 32px;
  }

  .mat-paginator-page-size-label {
    display: none;
  }

  .mat-button-toggle-label-content span:not(.mat-icon) {
    display: none;
  }

  .mat-button-toggle mat-icon {
    margin-right: 0;
  }
  
  .activity-list {
    padding: 8px;
  }
  
  /* Improve spacing for the header on small screens */
  .header-container h2 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }
  
  /* Make menu buttons more touch-friendly */
  button[mat-icon-button] {
    margin: 0 4px;
  }
  
  /* Adjust search field appearance */
  .search-field {
    margin-bottom: 8px;
  }
  
  .search-field .mat-form-field-wrapper {
    padding-bottom: 0;
  }
  
  /* Adjust activity items container */
  .activity-items-container {
    gap: 8px;
  }
  
  /* Adjust no results message */
  .no-results {
    padding: 24px 16px;
  }
}

@media screen and (max-width: 400px) {
  .mat-paginator {
    font-size: 11px;
  }

  .mat-paginator-range-label {
    margin: 0 4px;
  }

  .mat-paginator-navigation-previous,
  .mat-paginator-navigation-next,
  .mat-paginator-navigation-first,
  .mat-paginator-navigation-last {
    font-size: 16px;
  }

  .mat-paginator-navigation-button {
    min-width: 28px;
    height: 28px;
  }
  
  .activity-list {
    padding: 6px;
  }
  
  /* Stack menus vertically if needed */
  .menus-container {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  /* Ensure no-results message is properly sized */
  .no-results {
    padding: 16px;
    font-size: 14px;
  }
  
  /* Reduce heading size further */
  .header-container h2 {
    font-size: 1.1rem;
  }
  
  /* Optimize paginator for very small screens */
  .mat-paginator-range-actions {
    display: flex;
    justify-content: center;
  }
  
  .mat-paginator-range-label {
    margin: 0 2px;
  }
}

@media (hover: none) and (pointer: coarse) {
  .mat-button-toggle {
    min-height: 44px;
  }
  
  /* Improve touch targets for all interactive elements */
  button, 
  .mat-button-toggle, 
  .mat-menu-item {
    min-height: 44px;
  }
  
  /* Remove hover effects that don't make sense on touch devices */
  .activity-item:hover {
    transform: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  /* Add active state for touch feedback instead */
  .activity-item:active {
    background-color: rgba(0, 0, 0, 0.05);
  }
  
  /* Increase spacing between interactive elements */
  .menus-container {
    gap: 0.75rem;
  }
  
  /* Add tap highlight color for better touch feedback */
  button[mat-icon-button],
  button[mat-menu-item] {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }
  
  /* Ensure form fields are touch-friendly */
  .mat-form-field {
    margin-bottom: 16px;
  }
  
  input, select, textarea {
    font-size: 16px; /* Prevents iOS zoom on focus */
  }
}
