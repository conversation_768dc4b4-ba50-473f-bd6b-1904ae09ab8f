<div class="activity-item" (click)="onClick()">
  <!-- Middle section with activity details -->
  <div class="activity-details">
    <!-- Title row with warning icon -->
    <div class="title-row">
      <div class="activity-title">{{ activity.name }}</div>
      <div class="warning-icon" *ngIf="hasWarning">
        <mat-icon color="warn">warning</mat-icon>
      </div>
    </div>
    
    <div class="activity-id" *ngIf="activity.activityId">#{{ activity.activityId }} <span *ngIf="activity.workorderNo">| WO: {{ activity.workorderNo }}</span></div>
    <div class="activity-time" *ngIf="activity.startDate || activity.plannedStartDate">
      <mat-icon>schedule</mat-icon>
      {{ (activity.startDate || activity.plannedStartDate) | date:'MM/dd h:mm a' }} - 
      {{ (activity.finishDate || activity.plannedFinishDate) | date:'MM/dd h:mm a' }}
    </div>
  </div>
  
  <!-- Right section with status and progress -->
  <div class="activity-status-container">
    <div class="status-badge" [ngClass]="statusClass">{{ 'STATUS_VALUES.' + activity.status | translate }}</div>
    
    <!-- Only show progress spinner if status is not NOT_STARTED -->
    <div class="progress-indicator" *ngIf="activity.status !== 'NOT_STARTED'">
      <div class="spinner-wrapper">
        <mat-progress-spinner mode="determinate" [value]="activity.percentComplete * 100" diameter="40" strokeWidth="5">
        </mat-progress-spinner>
        <div class="progress-label">{{ activity.percentComplete * 100 | number:'1.0-0' }}%</div>
      </div>
    </div>
  </div>
</div>
