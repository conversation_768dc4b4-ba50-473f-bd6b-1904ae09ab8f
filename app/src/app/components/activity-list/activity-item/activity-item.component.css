.activity-item {
  display: flex;
  align-items: center;
  padding: 16px;
  margin-bottom: 12px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
}

.activity-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

/* Title row with warning icon */
.title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.warning-icon {
  margin-left: 8px;
}

.warning-icon mat-icon {
  font-size: 20px;
  height: 20px;
  width: 20px;
}

.activity-details {
  flex: 1;
}

.activity-title {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
  margin-bottom: 4px;
}

.activity-id {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 8px;
}

.activity-time {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
}

.activity-time mat-icon {
  font-size: 16px;
  height: 16px;
  width: 16px;
  margin-right: 4px;
}

.activity-status-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 16px;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  margin-bottom: 8px;
  color: white;
}

.status-in-progress {
  background-color: #2196f3;
}

.status-completed {
  background-color: #4caf50;
}

.status-not-started {
  background-color: #ff9800;
}

.status-default {
  background-color: #9e9e9e;
}

/* Spinner styles */
.spinner-wrapper {
  position: relative;
  width: 40px;
  height: 40px;
}

.spinner-wrapper mat-progress-spinner ::ng-deep circle {
  stroke: #1976d2;
}

.progress-label {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: 500;
}

/* Responsive styles */
@media (max-width: 600px) {
  .activity-item {
    flex-direction: column;
    align-items: flex-start;
    padding: 12px;
  }
  
  .activity-status-container {
    flex-direction: row;
    margin-left: 0;
    margin-top: 12px;
    width: 100%;
    justify-content: space-between;
    align-items: center;
  }
  
  .status-badge {
    margin-bottom: 0;
  }
  
  .activity-title {
    font-size: 15px;
    margin-right: 8px;
  }
  
  .activity-time {
    font-size: 13px;
  }
}

@media (max-width: 400px) {
  .activity-item {
    padding: 10px;
    margin-bottom: 8px;
  }
  
  .activity-title {
    font-size: 14px;
  }
  
  .activity-id {
    font-size: 12px;
    margin-bottom: 4px;
  }
  
  .activity-time {
    font-size: 12px;
  }
  
  .activity-time mat-icon {
    font-size: 14px;
    height: 14px;
    width: 14px;
  }
  
  .status-badge {
    padding: 3px 8px;
    font-size: 11px;
  }
  
  .spinner-wrapper {
    width: 32px;
    height: 32px;
  }
  
  .progress-label {
    font-size: 10px;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .activity-item {
    /* Add tap highlight for better touch feedback */
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }
  
  /* Increase touch target size for better accessibility */
  .title-row, .activity-id, .activity-time {
    padding: 2px 0;
  }
}
