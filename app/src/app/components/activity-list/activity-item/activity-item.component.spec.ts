import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { ActivityItemComponent } from './activity-item.component';
import { Status } from 'src/app/graphql/generated';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { of } from 'rxjs';

// Mock translate loader
export class MockTranslateLoader implements TranslateLoader {
  getTranslation() {
    return of({
      'STATUS_VALUES': {
        'IN_PROGRESS': 'In Progress',
        'COMPLETED': 'Completed',
        'NOT_STARTED': 'Not Started'
      }
    });
  }
}

describe('ActivityItemComponent', () => {
  let component: ActivityItemComponent;
  let fixture: ComponentFixture<ActivityItemComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ActivityItemComponent],
      imports: [
        MatIconModule, 
        MatProgressSpinnerModule,
        HttpClientTestingModule,
        TranslateModule.forRoot({
          loader: {
            provide: TranslateLoader,
            useClass: MockTranslateLoader
          }
        })
      ]
    });
    fixture = TestBed.createComponent(ActivityItemComponent);
    component = fixture.componentInstance;
    
    // Mock activity data
    component.activity = {
      id: '1',
      name: 'Test Activity',
      activityId: '1230012',
      workorderNo: '8129393393',
      percentComplete: 0.25,
      status: Status.InProgress,
      plannedStartDate: new Date().toISOString(),
      plannedFinishDate: new Date().toISOString()
    };
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display activity name', () => {
    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('.activity-title').textContent).toContain('Test Activity');
  });

  it('should display activity ID', () => {
    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('.activity-id').textContent).toContain('#1230012');
  });

  it('should display status badge with correct class', () => {
    const compiled = fixture.nativeElement;
    const statusBadge = compiled.querySelector('.status-badge');
    expect(statusBadge).toBeTruthy();
    expect(statusBadge.classList).toContain('status-in-progress');
  });

  it('should not show progress spinner for NOT_STARTED status', () => {
    component.activity.status = Status.NotStarted;
    fixture.detectChanges();
    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('.progress-indicator')).toBeNull();
  });

  it('should show progress spinner for IN_PROGRESS status', () => {
    component.activity.status = Status.InProgress;
    fixture.detectChanges();
    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('.progress-indicator')).not.toBeNull();
  });

  it('should show warning icon when activity has unableToWork flag', () => {
    component.activity.unableToWork = true;
    fixture.detectChanges();
    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('.warning-icon')).not.toBeNull();
  });

  it('should not show warning icon when activity does not have unableToWork flag', () => {
    component.activity.unableToWork = false;
    fixture.detectChanges();
    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('.warning-icon')).toBeNull();
  });

  it('should emit activityClick event when clicked', () => {
    spyOn(component.activityClick, 'emit');
    const compiled = fixture.nativeElement;
    compiled.querySelector('.activity-item').click();
    expect(component.activityClick.emit).toHaveBeenCalledWith(component.activity);
  });
});
