import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Activity, Status } from 'src/app/graphql/generated';

@Component({
  selector: 'app-activity-item',
  templateUrl: './activity-item.component.html',
  styleUrls: ['./activity-item.component.css']
})
export class ActivityItemComponent {
  @Input() activity!: Activity;
  @Output() activityClick = new EventEmitter<Activity>();

  get hasWarning(): boolean {
    return this.activity.unableToWork || false;
  }

  get statusClass(): string {
    switch (this.activity.status) {
      case Status.InProgress:
        return 'status-in-progress';
      case Status.Completed:
        return 'status-completed';
      case Status.NotStarted:
        return 'status-not-started';
      default:
        return 'status-default';
    }
  }

  onClick(): void {
    this.activityClick.emit(this.activity);
  }
}
