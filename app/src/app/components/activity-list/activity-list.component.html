<div class="mobile-container">
  <!-- Container for the heading + project selector + menus side by side -->
  <div class="header-container">
    <h2>{{ 'MY_ACTIVITIES' | translate }}</h2>

    <!-- Project Selector -->
    <app-project-selector class="project-select" (projectSelected)="onProjectSelected($event)"></app-project-selector>
  </div>

  <!-- Loading indicator -->
  <div class="loading-container" *ngIf="loading">
    <mat-spinner diameter="40"></mat-spinner>
    <p>{{ 'LOADING_MESSAGE' | translate }}</p>
  </div>

  <div class="content-container" *ngIf="!loading && !error">

    <!-- Container for menus -->
    <div class="menus-container">
      <!-- Sort Menu Trigger -->
      <button mat-icon-button [matMenuTriggerFor]="sortMenu" matTooltip="{{ 'COMMON.SORT' | translate }}"
        aria-label="Sort activities">
        <mat-icon>move_up</mat-icon>
      </button>
      <mat-menu #sortMenu="matMenu">
        <button mat-menu-item (click)="toggleSortDirection(true)">
          {{ 'COMMON.OLDEST_FIRST' | translate }}
        </button>
        <button mat-menu-item (click)="toggleSortDirection(false)">
          {{ 'COMMON.NEWEST_FIRST' | translate }}
        </button>
      </mat-menu>

      <!-- Filter Menu Trigger -->
      <button mat-icon-button [matMenuTriggerFor]="filterMenu" matTooltip="{{ 'COMMON.FILTER' | translate }}"
        aria-label="Filter activities">
        <mat-icon>filter_list</mat-icon>
      </button>
      <mat-menu #filterMenu="matMenu">
        <!-- Possible dynamic creation of menu items, or keep them static as below -->
        <button mat-menu-item (click)="setFilter('all')">
          {{ 'COMMON.ALL_ACTIVITIES' | translate }}
        </button>
        <button mat-menu-item (click)="setFilter('today')">
          {{ 'COMMON.SHOW_TODAY_ONLY' | translate }}
        </button>
        <button mat-menu-item (click)="setFilter('todayAndUnfinished')">
          {{ 'COMMON.SHOW_TODAY_AND_UNFINISHED' | translate }}
        </button>

        <mat-divider></mat-divider>

        <!-- Toggle Search Bar -->
        <button mat-menu-item (click)="toggleSearch()">
          <mat-icon>search</mat-icon>
          {{ 'SEARCH' | translate }}
        </button>
      </mat-menu>
    </div>

    <!-- Main content: project selector, filters, activities -->
    <div class="activity-list" *ngIf="!loading && !error">

      <!-- Search Bar -->
      <div class="filters" *ngIf="showSearchBar">
        <mat-form-field class="search-field">
          <mat-label>{{ 'SEARCH' | translate }}</mat-label>
          <input matInput placeholder="{{ 'SEARCH_PLACEHOLDER' | translate }}" [(ngModel)]="searchText"
            (ngModelChange)="filterActivities()" />
          <button mat-icon-button matSuffix aria-label="Clear" *ngIf="searchText"
            (click)="searchText=''; filterActivities()">
            <mat-icon>close</mat-icon>
          </button>
          <mat-icon matSuffix *ngIf="!searchText">search</mat-icon>
        </mat-form-field>
      </div>

      <!-- Activities found -->
      <div *ngIf="filteredActivities.length > 0">


        <!-- Activity List -->
        <div class="activity-items-container">
          <app-activity-item *ngFor="let activity of filteredActivities; trackBy: trackByActivityId"
            [activity]="activity" (activityClick)="onActivityClick($event)">
          </app-activity-item>
        </div>
      </div>

      <!-- Projects selected but no activities found -->
      <div class="no-results" *ngIf="selectedProjects.length > 0 && filteredActivities.length === 0">
        <p>{{ 'NO_ACTIVITIES_FOUND' | translate }}</p>
      </div>

      <div class="paginator-filter-container">
        <mat-paginator [length]="activities.length" [pageSize]="pageSize" [pageSizeOptions]="pageSizeOptions"
          (page)="onPageChange($event)" showFirstLastButtons aria-label="Select page of activities">
        </mat-paginator>
      </div>


    </div>


  </div>