import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MessageService } from 'src/app/core/services/message.service';
import { LoadingService } from 'src/app/core/services/loading.service';
import { Activity, Project, Status } from 'src/app/graphql/generated';
import { ProjectService } from 'src/app/services/project.service';

@Component({
  selector: 'app-activity-list',
  templateUrl: './activity-list.component.html',
  styleUrls: ['./activity-list.component.css']
})
export class ActivityListComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;

  projects: Project[] = [];
  selectedProjects: Project[] = [];
  allSelected = false;

  activities: Activity[] = [];
  filteredActivities: Activity[] = [];

  loading = true;
  error: string | null = null;

  // For pagination
  pageSize = 5;
  pageSizeOptions = [5, 10, 25];
  currentPage = 0;

  // For searching, filtering, sorting
  searchText = '';
  showSearchBar = false;
  filterOption: 'all' | 'today' | 'todayAndUnfinished' = 'all';
  sortAscending = false;

  constructor(
    private router: Router,
    private messageService: MessageService,
    private projectService: ProjectService,
    private loadingService: LoadingService
  ) {}

  ngOnInit(): void {
    
  }

  /**
   * This is your main filter function that calls other helper methods
   * to filter, sort, and paginate the activities.
   */
  filterActivities(): void {
    // 1) Merge all activities from selected projects
    let filtered = this.activities.slice();

    // 2) Apply date sort first (or last - up to you)
    filtered = this.sortActivitiesByDate(filtered, this.sortAscending);

    // 3) Apply filter for 'today' or 'todayAndUnfinished'
    switch (this.filterOption) {
      case 'today':
        filtered = filtered.filter((act) => this.isTodayActivity(act));
        break;
      case 'todayAndUnfinished':
        filtered = filtered.filter((act) => {
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          const activityDate = new Date(act.plannedStartDate || 0);
          activityDate.setHours(0, 0, 0, 0);
          const isBeforeToday = activityDate.getTime() < today.getTime();

          return (
            this.isTodayActivity(act) ||
            (isBeforeToday && this.isUnfinishedActivity(act))
          );
        });
        break;
      default:
        // 'all' -> no specific date filter
        break;
    }

    // 4) Apply text search
    if (this.searchText.trim()) {
      const searchTerm = this.searchText.toLowerCase();
      filtered = filtered.filter((activity) =>
        this.matchesSearchTerm(activity, searchTerm)
      );
    }

    // 5) Apply pagination
    const startIndex = this.currentPage * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.filteredActivities = filtered.slice(startIndex, endIndex);
  }

  /**
   * Sort activities by start date in ascending or descending order.
   */
  private sortActivitiesByDate(activities: Activity[], ascending: boolean): Activity[] {
    return activities.slice().sort((a, b) => {
      const dateA = new Date(a.plannedStartDate || '').getTime();
      const dateB = new Date(b.plannedStartDate || '').getTime();

      if (isNaN(dateA) && isNaN(dateB)) return 0;
      if (isNaN(dateA)) return 1;
      if (isNaN(dateB)) return -1;

      return ascending ? dateA - dateB : dateB - dateA;
    });
  }

  /**
   * Helper to check if an activity is happening 'today'.
   */
  private isTodayActivity(activity: Activity): boolean {
    if (!activity.plannedStartDate) return false;
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const activityDate = new Date(activity.plannedStartDate);
    activityDate.setHours(0, 0, 0, 0);

    return activityDate.getTime() === today.getTime();
  }

  /**
   * Helper to check if an activity is unfinished.
   */
  private isUnfinishedActivity(activity: Activity): boolean {
    return (
      activity.status !== Status.Completed
      // activity.status !== Status.ReviewedClosed &&
      // activity.status !== Status.AutoClosed
    );
  }

  /**
   * Check if an activity matches the user's search term.
   */
  private matchesSearchTerm(activity: Activity, term: string): boolean {
    // You can expand this to include any fields you'd like
    const nameMatch = activity.name.toLowerCase().includes(term);
    const startMatch = (activity.plannedStartDate || '').toString().toLowerCase().includes(term);
    const endMatch = (activity.plannedFinishDate || '').toString().toLowerCase().includes(term);
    const progressMatch = (activity.progress || 0).toString().includes(term);
    
    // Search in contractor name if available
    const contractorMatch = activity.contractor?.name?.toLowerCase().includes(term) || false;

    return nameMatch || startMatch || endMatch || progressMatch || contractorMatch;
  }

  /**
   * Toggle the search bar display.
   */
  toggleSearch(): void {
    this.showSearchBar = !this.showSearchBar;
  }

  /**
   * Filter selection from menu.
   */
  setFilter(filter: 'all' | 'today' | 'todayAndUnfinished'): void {
    this.filterOption = filter;
    this.currentPage = 0; // reset pagination when changing filter
    this.filterActivities();
  }

  /**
   * Toggle sorting direction from menu:
   * @param ascending boolean whether oldest-first (true) or newest-first (false)
   */
  toggleSortDirection(ascending: boolean): void {
    this.sortAscending = ascending;
    this.filterActivities();
  }

  /**
   * Handle page change event from the paginator.
   */
  onPageChange(event: PageEvent): void {
    this.pageSize = event.pageSize;
    this.currentPage = event.pageIndex;
    this.filterActivities();
  }

  /**
   * Navigate to the activity detail page (adjust route as necessary).
   */
  onActivityClick(activity: Activity): void {
    this.router.navigate(['/activity', activity.id]);
  }

  /**
   * Handle project selection from the project-selector component
   */
  onProjectSelected(project: Project): void {
    // Set the selected project
    this.selectedProjects = [project];

    console.log('Selected project:', project);
    
    // Fetch activities for the selected project using listProjectWithActivities
    this.loading = true;
    this.loadingService.show('activity-list');
    
    this.projectService.getProjectWithActivities(project.id, 20, null).subscribe({
      next: (data) => {
        this.activities = data.items || [];

        // Reset pagination and refilter
        this.currentPage = 0;
        this.filterActivities();
        this.loading = false;
        this.loadingService.hide();
      },
      error: (err) => {
        this.messageService.showError('Failed to load project activities');
        this.loading = false;
        this.loadingService.hide();
        console.error(err);
      }
    });
  }

  /**
   * trackBy function to improve *ngFor performance
   */
  trackByActivityId(index: number, activity: Activity): string {
    return activity.id;
  }
}
