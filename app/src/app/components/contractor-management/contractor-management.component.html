<div class="container">
  <h1>Contractor Management</h1>

  <div class="loading-overlay" *ngIf="isLoading">
    <mat-spinner></mat-spinner>
  </div>

  <div class="section">
    <h2>{{ selectedContractor ? 'Edit' : 'Create' }} Contractor</h2>
    <form [formGroup]="contractorForm" (ngSubmit)="selectedContractor ? updateContractor(selectedContractor.id) : createContractor()">
      <div class="form-row">
        <mat-form-field appearance="outline">
          <mat-label>Contractor Number</mat-label>
          <input matInput formControlName="contractorNumber" required>
          <mat-error *ngIf="contractorForm.get('contractorNumber')?.hasError('required')">Contractor Number is required</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Name</mat-label>
          <input matInput formControlName="name" required>
          <mat-error *ngIf="contractorForm.get('name')?.hasError('required')">Name is required</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Description</mat-label>
          <textarea matInput formControlName="description"></textarea>
        </mat-form-field>

        <div class="button-group">
          <button mat-raised-button color="primary" type="submit" [disabled]="contractorForm.invalid">
            {{ selectedContractor ? 'Update' : 'Create' }} Contractor
          </button>
          <button mat-button type="button" *ngIf="selectedContractor" (click)="cancelEdit()">Cancel</button>
        </div>
      </div>
    </form>
  </div>

  <div class="section">
    <h2>Contractor List</h2>
    <div class="table-container">
      <table mat-table [dataSource]="contractors" class="mat-elevation-z8">
        <!-- Contractor Number Column -->
        <ng-container matColumnDef="contractorNumber">
          <th mat-header-cell *matHeaderCellDef>Contractor Number</th>
          <td mat-cell *matCellDef="let contractor">{{contractor.contractorNumber}}</td>
        </ng-container>

        <!-- Name Column -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef>Name</th>
          <td mat-cell *matCellDef="let contractor">{{contractor.name}}</td>
        </ng-container>

        <!-- Description Column -->
        <ng-container matColumnDef="description">
          <th mat-header-cell *matHeaderCellDef>Description</th>
          <td mat-cell *matCellDef="let contractor">{{contractor.description}}</td>
        </ng-container>

        <!-- Users Column -->
        <ng-container matColumnDef="users">
          <th mat-header-cell *matHeaderCellDef>Users</th>
          <td mat-cell *matCellDef="let contractor">
            <div class="users-list">
              <div *ngFor="let user of contractor.users" class="user-chip">
                <span>{{user.username}}</span>
                <button mat-icon-button color="warn" (click)="removeUserFromContractor(user.username, contractor.id)">
                  <mat-icon>close</mat-icon>
                </button>
              </div>
            </div>
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let contractor">
            <button mat-icon-button color="primary" (click)="editContractor(contractor)" matTooltip="Edit Contractor">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button color="warn" (click)="deleteContractor(contractor.id, contractor.name)" matTooltip="Delete Contractor">
              <mat-icon>delete</mat-icon>
            </button>
            <button mat-icon-button [matMenuTriggerFor]="userMenu" matTooltip="Assign User">
              <mat-icon>person_add</mat-icon>
            </button>
            <mat-menu #userMenu="matMenu">
              <button mat-menu-item *ngFor="let user of users" 
                      (click)="assignUserToContractor(user.username, contractor.id)"
                      [disabled]="isUserInContractor(user, contractor.id)">
                {{user.username}}
              </button>
            </mat-menu>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedContractorColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedContractorColumns;"></tr>
      </table>
    </div>
  </div>
</div>
