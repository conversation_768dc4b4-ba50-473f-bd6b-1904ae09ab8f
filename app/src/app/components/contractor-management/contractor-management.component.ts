import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ContractorService } from '../../services/contractor.service';
import { UserService } from '../../services/user.service';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Contractor, ContractorInput, User } from 'src/app/graphql/generated';

@Component({
  selector: 'app-contractor-management',
  templateUrl: './contractor-management.component.html',
  styleUrls: ['./contractor-management.component.css']
})
export class ContractorManagementComponent implements OnInit {
  contractors: Contractor[] = [];
  users: User[] = [];
  selectedContractor: Contractor | null = null;
  selectedUser: User | null = null;
  contractorForm: FormGroup;
  displayedContractorColumns: string[] = ['contractorNumber', 'name', 'description', 'users', 'actions'];
  isLoading = false;

  constructor(
    private contractorService: ContractorService,
    private userService: UserService,
    private fb: FormBuilder,
    private snackBar: MatSnackBar
  ) {
    this.contractorForm = this.fb.group({
      contractorNumber: ['', [Validators.required]],
      name: ['', [Validators.required]],
      description: ['']
    });
  }

  ngOnInit(): void {
    this.loadContractors();
    this.loadUsers();
  }

  loadContractors(): void {
    this.isLoading = true;
    this.contractorService.listContractors().subscribe({
      next: (result) => {
        this.contractors = result.items;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading contractors', error);
        this.snackBar.open('Error loading contractors', 'Close', { duration: 3000 });
        this.isLoading = false;
      }
    });
  }

  loadUsers(): void {
    this.isLoading = true;
    this.userService.listUsers().subscribe({
      next: (result) => {
        this.users = result.items;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading users', error);
        this.snackBar.open('Error loading users', 'Close', { duration: 3000 });
        this.isLoading = false;
      }
    });
  }

  createContractor(): void {
    if (this.contractorForm.valid) {
      this.isLoading = true;
      const contractorInput: ContractorInput = this.contractorForm.value;
      
      this.contractorService.createContractor(contractorInput).subscribe({
        next: (contractor) => {
          this.contractors.push(contractor);
          this.contractorForm.reset();
          this.snackBar.open('Contractor created successfully', 'Close', { duration: 3000 });
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error creating contractor', error);
          this.snackBar.open('Error creating contractor', 'Close', { duration: 3000 });
          this.isLoading = false;
        }
      });
    }
  }

  updateContractor(id: string): void {
    if (this.contractorForm.valid) {
      this.isLoading = true;
      const contractorInput: ContractorInput = this.contractorForm.value;
      
      this.contractorService.updateContractor(id, contractorInput).subscribe({
        next: (updatedContractor) => {
          const index = this.contractors.findIndex(contractor => contractor.id === id);
          if (index !== -1) {
            this.contractors[index] = updatedContractor;
          }
          this.selectedContractor = null;
          this.contractorForm.reset();
          this.snackBar.open('Contractor updated successfully', 'Close', { duration: 3000 });
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error updating contractor', error);
          this.snackBar.open('Error updating contractor', 'Close', { duration: 3000 });
          this.isLoading = false;
        }
      });
    }
  }

  deleteContractor(id: string, name: string): void {
    if (confirm(`Are you sure you want to delete contractor ${name}?`)) {
      this.isLoading = true;
      this.contractorService.deleteContractor(id).subscribe({
        next: () => {
          this.contractors = this.contractors.filter(contractor => contractor.id !== id);
          this.snackBar.open('Contractor deleted successfully', 'Close', { duration: 3000 });
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error deleting contractor', error);
          this.snackBar.open('Error deleting contractor', 'Close', { duration: 3000 });
          this.isLoading = false;
        }
      });
    }
  }

  editContractor(contractor: Contractor): void {
    this.selectedContractor = contractor;
    this.contractorForm.patchValue({
      contractorNumber: contractor.contractorNumber,
      name: contractor.name,
      description: contractor.description
    });
  }

  cancelEdit(): void {
    this.selectedContractor = null;
    this.contractorForm.reset();
  }

  assignUserToContractor(username: string, contractorId: string): void {
    this.isLoading = true;
    this.contractorService.addUserToContractor(username, contractorId).subscribe({
      next: (updatedUser) => {
        // Refresh contractors to show updated user assignments
        this.loadContractors();
        this.snackBar.open('User assigned to contractor successfully', 'Close', { duration: 3000 });
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error assigning user to contractor', error);
        this.snackBar.open('Error assigning user to contractor', 'Close', { duration: 3000 });
        this.isLoading = false;
      }
    });
  }

  removeUserFromContractor(username: string, contractorId: string): void {
    this.isLoading = true;
    this.contractorService.removeUserFromContractor(username, contractorId).subscribe({
      next: (updatedUser) => {
        // Refresh contractors to show updated user assignments
        this.loadContractors();
        this.snackBar.open('User removed from contractor successfully', 'Close', { duration: 3000 });
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error removing user from contractor', error);
        this.snackBar.open('Error removing user from contractor', 'Close', { duration: 3000 });
        this.isLoading = false;
      }
    });
  }

  isUserInContractor(user: User, contractorId: string): boolean {
    return user.contractors?.some(contractor => contractor?.id === contractorId) || false;
  }
}
