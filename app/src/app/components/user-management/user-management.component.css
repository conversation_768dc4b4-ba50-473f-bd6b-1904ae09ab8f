.container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.tab-content {
  padding: 20px 0;
}

.section {
  margin-bottom: 30px;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: flex-start;
}

mat-form-field {
  flex: 1;
  min-width: 200px;
}

.table-container {
  overflow-x: auto;
  margin-top: 20px;
}

table {
  width: 100%;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.groups-list {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.group-chip {
  display: flex;
  align-items: center;
  background-color: #e0e0e0;
  border-radius: 16px;
  padding: 0 8px;
  height: 32px;
  font-size: 14px;
}

.group-chip button {
  width: 24px;
  height: 24px;
  line-height: 24px;
  margin-left: 4px;
}

.group-chip button mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  line-height: 16px;
}

/* User details section */
.mt-3 {
  margin-top: 1rem;
}

mat-card {
  margin-bottom: 1rem;
}

.chip-list {
  width: 100%;
}

mat-card-content h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.2rem;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }
  
  mat-form-field {
    width: 100%;
  }
}
