import { Component, OnInit, ViewChild } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { UserService } from '../../services/user.service';
import { GroupService } from '../../services/group.service';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Group, GroupInput, GroupType, User, UserInput } from 'src/app/graphql/generated';
import { MatChipInputEvent, MatChipGrid } from '@angular/material/chips';
import { COMMA, ENTER } from '@angular/cdk/keycodes';

@Component({
  selector: 'app-user-management',
  templateUrl: './user-management.component.html',
  styleUrls: ['./user-management.component.css']
})
export class UserManagementComponent implements OnInit {
  users: User[] = [];
  groups: Group[] = [];
  selectedUser: User | null = null;
  selectedGroup: Group | null = null;
  userForm: FormGroup;
  groupForm: FormGroup;
  disciplineForm: FormGroup;
  equipmentForm: FormGroup;
  displayedUserColumns: string[] = ['username', 'email', 'groups', 'actions'];
  displayedGroupColumns: string[] = ['name', 'type', 'description', 'actions'];
  groupTypes = Object.values(GroupType);
  isLoading = false;
  
  // For mat-chip-grid
  readonly separatorKeysCodes = [ENTER, COMMA] as const;
  disciplines: string[] = [];
  equipments: string[] = [];
  
  @ViewChild('disciplineChipList') disciplineChipList!: MatChipGrid;
  @ViewChild('equipmentChipList') equipmentChipList!: MatChipGrid;

  constructor(
    private userService: UserService,
    private groupService: GroupService,
    private fb: FormBuilder,
    private snackBar: MatSnackBar
  ) {
    this.userForm = this.fb.group({
      username: ['', [Validators.required]],
      email: ['', [Validators.email]]
    });

    this.groupForm = this.fb.group({
      name: ['', [Validators.required]],
      groupType: [GroupType.Worker, [Validators.required]],
      description: ['']
    });

    this.disciplineForm = this.fb.group({
      discipline: ['']
    });

    this.equipmentForm = this.fb.group({
      equipment: ['']
    });
  }

  ngOnInit(): void {
    this.loadUsers();
    this.loadGroups();
  }

  loadUsers(): void {
    this.isLoading = true;
    this.userService.listUsers().subscribe({
      next: (result) => {
        this.users = result.items;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading users', error);
        this.snackBar.open('Error loading users', 'Close', { duration: 3000 });
        this.isLoading = false;
      }
    });
  }

  loadGroups(): void {
    this.isLoading = true;
    this.groupService.listGroups().subscribe({
      next: (result) => {
        this.groups = result.items;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading groups', error);
        this.snackBar.open('Error loading groups', 'Close', { duration: 3000 });
        this.isLoading = false;
      }
    });
  }

  createUser(): void {
    if (this.userForm.valid) {
      this.isLoading = true;
      const userInput: UserInput = this.userForm.value;
      
      this.userService.createUser(userInput).subscribe({
        next: (user) => {
          this.users.push(user);
          this.userForm.reset();
          this.snackBar.open('User created successfully', 'Close', { duration: 3000 });
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error creating user', error);
          this.snackBar.open('Error creating user', 'Close', { duration: 3000 });
          this.isLoading = false;
        }
      });
    }
  }

  createGroup(): void {
    if (this.groupForm.valid) {
      this.isLoading = true;
      const groupInput: GroupInput = this.groupForm.value;
      
      this.groupService.createGroup(groupInput).subscribe({
        next: (group) => {
          this.groups.push(group);
          this.groupForm.reset({ groupType: GroupType.Worker });
          this.snackBar.open('Group created successfully', 'Close', { duration: 3000 });
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error creating group', error);
          this.snackBar.open('Error creating group', 'Close', { duration: 3000 });
          this.isLoading = false;
        }
      });
    }
  }

  deleteUser(username: string): void {
    if (confirm(`Are you sure you want to delete user ${username}?`)) {
      this.isLoading = true;
      this.userService.deleteUser(username).subscribe({
        next: () => {
          this.users = this.users.filter(user => user.username !== username);
          this.snackBar.open('User deleted successfully', 'Close', { duration: 3000 });
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error deleting user', error);
          this.snackBar.open('Error deleting user', 'Close', { duration: 3000 });
          this.isLoading = false;
        }
      });
    }
  }

  deleteGroup(id: string, name: string): void {
    if (confirm(`Are you sure you want to delete group ${name}?`)) {
      this.isLoading = true;
      this.groupService.deleteGroup(id).subscribe({
        next: () => {
          this.groups = this.groups.filter(group => group.id !== id);
          this.snackBar.open('Group deleted successfully', 'Close', { duration: 3000 });
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error deleting group', error);
          this.snackBar.open('Error deleting group', 'Close', { duration: 3000 });
          this.isLoading = false;
        }
      });
    }
  }

  assignUserToGroup(username: string, groupId: string): void {
    this.isLoading = true;
    this.userService.addUserToGroup(username, groupId).subscribe({
      next: (updatedUser) => {
        const index = this.users.findIndex(user => user.username === username);
        if (index !== -1) {
          this.users[index] = updatedUser;
        }
        this.snackBar.open('User assigned to group successfully', 'Close', { duration: 3000 });
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error assigning user to group', error);
        this.snackBar.open('Error assigning user to group', 'Close', { duration: 3000 });
        this.isLoading = false;
      }
    });
  }

  removeUserFromGroup(username: string, groupId: string): void {
    this.isLoading = true;
    this.userService.removeUserFromGroup(username, groupId).subscribe({
      next: (updatedUser) => {
        const index = this.users.findIndex(user => user.username === username);
        if (index !== -1) {
          this.users[index] = updatedUser;
        }
        this.snackBar.open('User removed from group successfully', 'Close', { duration: 3000 });
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error removing user from group', error);
        this.snackBar.open('Error removing user from group', 'Close', { duration: 3000 });
        this.isLoading = false;
      }
    });
  }

  selectUser(user: User): void {
    this.selectedUser = user;
    this.disciplines = user.disciplines?.filter(d => d !== null) as string[] || [];
    this.equipments = user.equipments?.filter(e => e !== null) as string[] || [];
  }

  getUserGroups(user: User): Group[] {
    const groups: Group[] = [];
    if (user.managerGroups) groups.push(...user.managerGroups);
    if (user.operatorGroups) groups.push(...user.operatorGroups);
    if (user.workerGroups) groups.push(...user.workerGroups);
    return groups;
  }

  isUserInGroup(user: User, groupId: string): boolean {
    const userGroups = this.getUserGroups(user);
    return userGroups.some(group => group.id === groupId);
  }

  getGroupTypeLabel(type: GroupType): string {
    return type.charAt(0) + type.slice(1).toLowerCase();
  }

  // Discipline management
  addDiscipline(event: MatChipInputEvent): void {
    const value = (event.value || '').trim();
    if (value && this.selectedUser) {
      this.disciplines.push(value);
      this.updateUserDisciplines();
      event.chipInput!.clear();
    }
  }

  removeDiscipline(discipline: string): void {
    const index = this.disciplines.indexOf(discipline);
    if (index >= 0 && this.selectedUser) {
      this.disciplines.splice(index, 1);
      this.updateUserDisciplines();
    }
  }

  updateUserDisciplines(): void {
    if (this.selectedUser) {
      this.isLoading = true;
      this.userService.updateUser(this.selectedUser.username, { disciplines: this.disciplines })
        .subscribe({
          next: (updatedUser) => {
            const index = this.users.findIndex(u => u.username === this.selectedUser!.username);
            if (index !== -1) {
              this.users[index] = { ...this.users[index], disciplines: updatedUser.disciplines };
              this.selectedUser = this.users[index];
            }
            this.snackBar.open('Disciplines updated successfully', 'Close', { duration: 3000 });
            this.isLoading = false;
          },
          error: (error) => {
            console.error('Error updating disciplines', error);
            this.snackBar.open('Error updating disciplines', 'Close', { duration: 3000 });
            this.isLoading = false;
          }
        });
    }
  }

  // Equipment management
  addEquipment(event: MatChipInputEvent): void {
    const value = (event.value || '').trim();
    if (value && this.selectedUser) {
      this.equipments.push(value);
      this.updateUserEquipments();
      event.chipInput!.clear();
    }
  }

  removeEquipment(equipment: string): void {
    const index = this.equipments.indexOf(equipment);
    if (index >= 0 && this.selectedUser) {
      this.equipments.splice(index, 1);
      this.updateUserEquipments();
    }
  }

  updateUserEquipments(): void {
    if (this.selectedUser) {
      this.isLoading = true;
      this.userService.updateUser(this.selectedUser.username, { equipments: this.equipments })
        .subscribe({
          next: (updatedUser) => {
            const index = this.users.findIndex(u => u.username === this.selectedUser!.username);
            if (index !== -1) {
              this.users[index] = { ...this.users[index], equipments: updatedUser.equipments };
              this.selectedUser = this.users[index];
            }
            this.snackBar.open('Equipment updated successfully', 'Close', { duration: 3000 });
            this.isLoading = false;
          },
          error: (error) => {
            console.error('Error updating equipment', error);
            this.snackBar.open('Error updating equipment', 'Close', { duration: 3000 });
            this.isLoading = false;
          }
        });
    }
  }
}
