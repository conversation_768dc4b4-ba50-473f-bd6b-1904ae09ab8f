<div class="container">
  <h1>User Management</h1>

  <div class="loading-overlay" *ngIf="isLoading">
    <mat-spinner></mat-spinner>
  </div>

  <mat-tab-group>
    <!-- Users Tab -->
    <mat-tab label="Users">
      <div class="tab-content">
        <div class="section">
          <h2>Create User</h2>
          <form [formGroup]="userForm" (ngSubmit)="createUser()">
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Username</mat-label>
                <input matInput formControlName="username" required>
                <mat-error *ngIf="userForm.get('username')?.hasError('required')">Username is required</mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Email</mat-label>
                <input matInput formControlName="email" type="email">
                <mat-error *ngIf="userForm.get('email')?.hasError('email')">Please enter a valid email</mat-error>
              </mat-form-field>

              <button mat-raised-button color="primary" type="submit" [disabled]="userForm.invalid">Create User</button>
            </div>
          </form>
        </div>

        <div class="section">
          <h2>User List</h2>
          <div class="table-container">
            <table mat-table [dataSource]="users" class="mat-elevation-z8">
              <!-- Username Column -->
              <ng-container matColumnDef="username">
                <th mat-header-cell *matHeaderCellDef>Username</th>
                <td mat-cell *matCellDef="let user" (click)="selectUser(user)" style="cursor: pointer;">{{user.username}}</td>
              </ng-container>

              <!-- Email Column -->
              <ng-container matColumnDef="email">
                <th mat-header-cell *matHeaderCellDef>Email</th>
                <td mat-cell *matCellDef="let user">{{user.email}}</td>
              </ng-container>

              <!-- Groups Column -->
              <ng-container matColumnDef="groups">
                <th mat-header-cell *matHeaderCellDef>Groups</th>
                <td mat-cell *matCellDef="let user">
                  <div class="groups-list">
                    <div *ngFor="let group of getUserGroups(user)" class="group-chip">
                      <!-- <span>{{group.name}} ({{getGroupTypeLabel(group.groupType)}})</span> -->
                      <span>{{group.name}}</span>
                      <button mat-icon-button color="warn" (click)="removeUserFromGroup(user.username, group.id)">
                        <mat-icon>close</mat-icon>
                      </button>
                    </div>
                  </div>
                </td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let user">
                  <button mat-icon-button color="warn" (click)="deleteUser(user.username)" matTooltip="Delete User">
                    <mat-icon>delete</mat-icon>
                  </button>
                  <button mat-icon-button [matMenuTriggerFor]="groupMenu" matTooltip="Assign to Group">
                    <mat-icon>group_add</mat-icon>
                  </button>
                  <mat-menu #groupMenu="matMenu">
                    <button mat-menu-item *ngFor="let group of groups" 
                            (click)="assignUserToGroup(user.username, group.id)"
                            [disabled]="isUserInGroup(user, group.id)">
                      {{group.name}} ({{getGroupTypeLabel(group.groupType)}})
                    </button>
                  </mat-menu>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedUserColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedUserColumns;"></tr>
            </table>
          </div>
        </div>

        <!-- User Detail Section -->
        <div class="section" *ngIf="selectedUser">
          <h2>User Details: {{selectedUser.username}}</h2>
          
          <mat-card>
            <mat-card-content>
              <h3>Basic Information</h3>
              <p><strong>Username:</strong> {{selectedUser.username}}</p>
              <p><strong>Email:</strong> {{selectedUser.email || 'Not provided'}}</p>
              <p><strong>Created:</strong> {{selectedUser.createdAt | date:'medium'}}</p>
              <p><strong>Last Updated:</strong> {{selectedUser.updatedAt | date:'medium'}}</p>
            </mat-card-content>
          </mat-card>

          <mat-card class="mt-3">
            <mat-card-content>
              <h3>Disciplines</h3>
              <mat-form-field class="chip-list">
                <mat-label>Add Disciplines</mat-label>
                <mat-chip-grid #disciplineChipList aria-label="Discipline selection">
                  <mat-chip-row
                    *ngFor="let discipline of disciplines"
                    (removed)="removeDiscipline(discipline)">
                    {{discipline}}
                    <button matChipRemove aria-label="remove discipline">
                      <mat-icon>cancel</mat-icon>
                    </button>
                  </mat-chip-row>
                  <input
                    placeholder="New discipline..."
                    [matChipInputFor]="disciplineChipList"
                    [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
                    (matChipInputTokenEnd)="addDiscipline($event)">
                </mat-chip-grid>
              </mat-form-field>
            </mat-card-content>
          </mat-card>

          <mat-card class="mt-3">
            <mat-card-content>
              <h3>Equipment</h3>
              <mat-form-field class="chip-list">
                <mat-label>Add Equipment</mat-label>
                <mat-chip-grid #equipmentChipList aria-label="Equipment selection">
                  <mat-chip-row
                    *ngFor="let equipment of equipments"
                    (removed)="removeEquipment(equipment)">
                    {{equipment}}
                    <button matChipRemove aria-label="remove equipment">
                      <mat-icon>cancel</mat-icon>
                    </button>
                  </mat-chip-row>
                  <input
                    placeholder="New equipment..."
                    [matChipInputFor]="equipmentChipList"
                    [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
                    (matChipInputTokenEnd)="addEquipment($event)">
                </mat-chip-grid>
              </mat-form-field>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </mat-tab>

    <!-- Groups Tab -->
    <mat-tab label="Groups">
      <div class="tab-content">
        <div class="section">
          <h2>Create Group</h2>
          <form [formGroup]="groupForm" (ngSubmit)="createGroup()">
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Name</mat-label>
                <input matInput formControlName="name" required>
                <mat-error *ngIf="groupForm.get('name')?.hasError('required')">Name is required</mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Type</mat-label>
                <mat-select formControlName="groupType" required>
                  <mat-option *ngFor="let type of groupTypes" [value]="type">
                    {{getGroupTypeLabel(type)}}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="groupForm.get('type')?.hasError('required')">Type is required</mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Description</mat-label>
                <textarea matInput formControlName="description"></textarea>
              </mat-form-field>

              <button mat-raised-button color="primary" type="submit" [disabled]="groupForm.invalid">Create Group</button>
            </div>
          </form>
        </div>

        <div class="section">
          <h2>Group List</h2>
          <div class="table-container">
            <table mat-table [dataSource]="groups" class="mat-elevation-z8">
              <!-- Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>Name</th>
                <td mat-cell *matCellDef="let group">{{group.name}}</td>
              </ng-container>

              <!-- Type Column -->
              <ng-container matColumnDef="type">
                <th mat-header-cell *matHeaderCellDef>Type</th>
                <td mat-cell *matCellDef="let group">{{getGroupTypeLabel(group.groupType)}}</td>
              </ng-container>

              <!-- Description Column -->
              <ng-container matColumnDef="description">
                <th mat-header-cell *matHeaderCellDef>Description</th>
                <td mat-cell *matCellDef="let group">{{group.description}}</td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let group">
                  <button mat-icon-button color="warn" (click)="deleteGroup(group.id, group.name)" matTooltip="Delete Group">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedGroupColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedGroupColumns;"></tr>
            </table>
          </div>
        </div>
      </div>
    </mat-tab>
  </mat-tab-group>
</div>
