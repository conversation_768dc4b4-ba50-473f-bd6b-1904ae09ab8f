<h1>{{ 'SYNC_JOBS.TITLE' | translate }}</h1>

<div class="filters-container">
  <mat-form-field class="search-field">
    <mat-label>{{ 'SYNC_JOBS.FILTERS.SEARCH' | translate }}</mat-label>
    <input matInput [formControl]="searchFilter" placeholder="{{ 'SYNC_JOBS.FILTERS.SEARCH_PLACEHOLDER' | translate }}">
    <mat-icon matSuffix>search</mat-icon>
  </mat-form-field>
</div>

<div class="loading-indicator" *ngIf="isLoading">
  <mat-spinner diameter="40"></mat-spinner>
</div>

<div class="mat-elevation-z8">
  <table mat-table [dataSource]="dataSource" matSort>
    <!-- Sync Type Column -->
    <ng-container matColumnDef="syncType">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'SYNC_JOBS.COLUMNS.SYNC_TYPE' | translate }}</th>
      <td mat-cell *matCellDef="let syncJob">{{ syncJob.syncType }}</td>
    </ng-container>

    <!-- Project ID Column -->
    <ng-container matColumnDef="projectId">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'SYNC_JOBS.COLUMNS.PROJECT_ID' | translate }}</th>
      <td mat-cell *matCellDef="let syncJob">{{ syncJob.projectId || 'N/A' }}</td>
    </ng-container>

    <!-- Timestamp Column -->
    <ng-container matColumnDef="timestamp">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'SYNC_JOBS.COLUMNS.TIMESTAMP' | translate }}</th>
      <td mat-cell *matCellDef="let syncJob">{{ syncJob.timestamp | date:'medium' }}</td>
    </ng-container>

    <!-- Status Column -->
    <ng-container matColumnDef="status">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'SYNC_JOBS.COLUMNS.STATUS' | translate }}</th>
      <td mat-cell *matCellDef="let syncJob" [style.color]="getStatusColor(syncJob.status)">
        {{ 'SYNC_JOBS.STATUS.' + syncJob.status | translate }}
      </td>
    </ng-container>

    <!-- Execution Time Column -->
    <ng-container matColumnDef="executionTime">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'SYNC_JOBS.COLUMNS.EXECUTION_TIME' | translate }}</th>
      <td mat-cell *matCellDef="let syncJob">{{ syncJob.executionTime ? (syncJob.executionTime | number:'1.2-2') + 's' : 'N/A' }}</td>
    </ng-container>

    <!-- Items Processed Column -->
    <ng-container matColumnDef="itemsProcessed">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'SYNC_JOBS.COLUMNS.ITEMS_PROCESSED' | translate }}</th>
      <td mat-cell *matCellDef="let syncJob">{{ getItemsProcessed(syncJob) }}</td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
  </table>

  <mat-paginator 
    [length]="totalCount"
    [pageSize]="pageSize" 
    [pageSizeOptions]="[5, 10, 20, 50]"
    (page)="handlePageEvent($event)"
    showFirstLastButtons
    aria-label="Select page of sync jobs">
  </mat-paginator>
</div>
