import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { FormControl } from '@angular/forms';
import { debounceTime } from 'rxjs/operators';
import { SyncJob, SyncJobConnection, SyncJobStatus } from 'src/app/graphql/generated';
import { SyncJobService } from 'src/app/services/sync-job.service';
import { MessageService } from 'src/app/core/services/message.service';
import { TranslateService } from 'src/app/core/services/translate.service';

@Component({
  selector: 'app-sync-jobs',
  templateUrl: './sync-jobs.component.html',
  styleUrls: ['./sync-jobs.component.css']
})
export class SyncJobsComponent implements OnInit, AfterViewInit {
  displayedColumns: string[] = ['syncType', 'projectId', 'timestamp', 'status', 'executionTime', 'itemsProcessed'];
  dataSource = new MatTableDataSource<SyncJob>([]);
  
  // Search filter
  searchFilter = new FormControl('');
  
  // Pagination properties
  allSyncJobs: SyncJob[] = [];
  nextToken: string | null = null;
  totalCount: number = 0;
  isLoading: boolean = false;
  batchSize: number = 100;
  pageSize: number = 10;
  
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  constructor(
    private syncJobService: SyncJobService,
    private messageService: MessageService,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.fetchSyncJobs();
    
    // Set up search filter
    this.searchFilter.valueChanges
      .pipe(debounceTime(300))
      .subscribe(value => {
        this.dataSource.filter = value.trim().toLowerCase();
      });
      
    // Set custom filter predicate
    this.dataSource.filterPredicate = (data: SyncJob, filter: string) => {
      if (!filter) return true;
      
      const searchStr = filter.toLowerCase();
      return (data.syncType?.toLowerCase().includes(searchStr) || 
             (data.projectId?.toString().toLowerCase().includes(searchStr)) || 
             data.status?.toString().toLowerCase().includes(searchStr));
    };
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    
    // Set default sorting by timestamp in descending order (newest first)
    this.sort.active = 'timestamp';
    this.sort.direction = 'desc';
    this.sort.sortChange.emit({active: 'timestamp', direction: 'desc'});
  }

  fetchSyncJobs(): void {
    this.isLoading = true;
    
    this.syncJobService.listSyncJobs(this.batchSize, null).subscribe(
      (syncJobConnection: SyncJobConnection) => {
        if (syncJobConnection) {
          this.allSyncJobs = syncJobConnection.items || [];
          this.nextToken = syncJobConnection.nextToken;
          this.totalCount = syncJobConnection.totalCount || this.allSyncJobs.length;
          this.updateDisplayedSyncJobs();
          this.dataSource.sort = this.sort;
          this.isLoading = false;
        }
      },
      error => {
        this.messageService.showError('Failed to load sync jobs');
        this.isLoading = false;
        console.error(error);
      }
    );
  }

  updateDisplayedSyncJobs(): void {
    this.dataSource.data = this.allSyncJobs;
  }

  handlePageEvent(event: PageEvent): void {
    this.pageSize = event.pageSize;
    
    const currentPage = event.pageIndex;
    const itemsPerPage = event.pageSize;
    const totalItemsNeeded = (currentPage + 1) * itemsPerPage;
    
    if (totalItemsNeeded > this.allSyncJobs.length && this.nextToken) {
      this.loadMoreSyncJobs();
    }
  }

  loadMoreSyncJobs(): void {
    if (!this.nextToken || this.isLoading) return;
    
    this.isLoading = true;
    
    this.syncJobService.listSyncJobs(this.batchSize, this.nextToken).subscribe(
      (syncJobConnection: SyncJobConnection) => {
        if (syncJobConnection) {
          this.allSyncJobs = [...this.allSyncJobs, ...(syncJobConnection.items || [])];
          this.nextToken = syncJobConnection.nextToken;
          this.totalCount = syncJobConnection.totalCount || this.allSyncJobs.length;
          this.updateDisplayedSyncJobs();
          this.isLoading = false;
        }
      },
      error => {
        this.messageService.showError('Failed to load more sync jobs');
        this.isLoading = false;
        console.error(error);
      }
    );
  }


  // Helper method to format the items processed count
  getItemsProcessed(syncJob: SyncJob): number {
    let total = 0;
    if (syncJob.projectsProcessed) total += syncJob.projectsProcessed;
    if (syncJob.activitiesProcessed) total += syncJob.activitiesProcessed;
    if (syncJob.activityCodesProcessed) total += syncJob.activityCodesProcessed;
    if (syncJob.relationshipsProcessed) total += syncJob.relationshipsProcessed;
    return total;
  }

  // Helper method to get status color
  getStatusColor(status: SyncJobStatus): string {
    switch (status) {
      case SyncJobStatus.Succeeded:
        return 'green';
      case SyncJobStatus.Failed:
        return 'red';
      case SyncJobStatus.InProgress:
        return 'orange';
      default:
        return 'inherit';
    }
  }
}
