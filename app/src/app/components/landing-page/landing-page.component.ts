import { Component } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-landing-page',
  templateUrl: './landing-page.component.html',
  styleUrl: './landing-page.component.css'
})
export class LandingPageComponent {
  
  constructor(private router: Router) {}
  
  navigateToActivities(): void {
    this.router.navigate(['/activity-list']);
  }
  
  navigateToUserManual(): void {
    this.router.navigate(['/docs/overview_en']);
  }
  
  navigateToHelp(): void {
    this.router.navigate(['/about']);
  }
}
