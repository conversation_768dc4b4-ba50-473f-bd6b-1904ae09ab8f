.landing-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.hero-section {
  height: 100vh;
  display: grid;
  grid-template-columns: 1fr 1fr;
  color: white;
  position: relative;
}

.hero-section::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 50%;
  height: 100%;
  background-image: url('../../../assets/pictures/landing_background.png');
  background-size: cover;
  background-position: center;
  z-index: -1;
}

.hero-content {
  grid-column: 2;
  max-width: 500px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  height: 100%;
  justify-content: center;
  padding: 80px 40px;
}

.title-container {
  grid-column: 1;
  position: absolute;
  left: 40px;
  top: 80px;
  max-width: 500px;
  z-index: 1;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
}

h1 {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: white;
}

.hero-subtitle {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  line-height: 1.4;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 20px;
  margin-top: 40px;
}

.action-button {
  width: 100%;
  padding: 15px;
  background-color: #00b0ff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
}

.action-button:hover {
  background-color: #0091ea;
}

.action-button mat-icon {
  margin-right: 10px;
}

@media (max-width: 768px) {
  .hero-section {
    grid-template-columns: 1fr;
    background-image: url('../../../assets/pictures/landing_background.png');
    background-size: cover;
    background-position: center;
    padding: 60px 20px 40px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  .hero-section::before {
    display: none;
  }
  
  .title-container {
    position: static;
    margin-bottom: 20px;
    text-shadow: none;
  }
  
  .hero-content {
    grid-column: 1;
    padding: 0;
    height: 100%;
    justify-content: space-between;
  }
  
  h1 {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .action-button {
    padding: 12px;
  }
  
  .action-buttons {
    margin-top: auto;
    gap: 10px;
  }
}
