{"name": "app", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "codegen": "graphql-codegen --config codegen.yaml"}, "private": true, "dependencies": {"@angular/animations": "^18.2.13", "@angular/cdk": "^18.2.14", "@angular/common": "^18.2.13", "@angular/compiler": "^18.2.13", "@angular/core": "^18.2.13", "@angular/forms": "^18.2.13", "@angular/material": "^18.2.14", "@angular/platform-browser": "^18.2.13", "@angular/platform-browser-dynamic": "^18.2.13", "@angular/router": "^18.2.13", "@apollo/client": "^3.11.10", "@ngx-translate/core": "^16.0.3", "@ngx-translate/http-loader": "^16.0.0", "@types/file-saver": "^2.0.7", "@types/xlsx": "^0.0.36", "apollo-angular": "^8.0.0", "aws-amplify": "^6.6.2", "aws-rum-web": "^1.19.0", "chart.js": "^4.4.7", "chartjs-adapter-date-fns": "^3.0.0", "file-saver": "^2.0.5", "graphql": "^16.9.0", "marked": "^12.0.2", "ng2-charts": "^7.0.0", "ngx-markdown": "^18.1.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.12", "@angular/cli": "~18.2.12", "@angular/compiler-cli": "^18.2.13", "@graphql-codegen/cli": "5.0.3", "@graphql-codegen/typescript-apollo-angular": "4.0.0", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.4.5"}}