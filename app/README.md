# App

This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 16.2.2.

## Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The application will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory.

## Running unit tests

Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Running end-to-end tests

Run `ng e2e` to execute the end-to-end tests via a platform of your choice. To use this command, you need to first add a package that implements end-to-end testing capabilities.

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI Overview and Command Reference](https://angular.io/cli) page.

### 4. Generate GraphQL Statements
Run the following command to generate the necessary GraphQL code:
```bash
npx graphql-codegen
```

## Deployment

### Change environments to meet qa or prod environment

### Build and deploy
```
$Env:NODE_TLS_REJECT_UNAUTHORIZED=0
ng build --configuration qa
aws s3 cp dist/app s3://frontendstack-intranetstaticwebsitebucketc0729b19-qenc12zjylaq --recursive --profile taex-qa
aws cloudfront create-invalidation --distribution-id E1IRV9FWFBS2R5 --paths "/*" --profile taex-qa
```

### Create testing user in cognito
aws cognito-idp admin-create-user --user-pool-id eu-central-1_pf5IB2K4Y --username julian
aws cognito-idp admin-set-user-password --user-pool-id eu-central-1_pf5IB2K4Y --username julian --password <PASSWORD> --permanent
