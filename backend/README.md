# Welcome to the backend

## Useful commands

* `cdk deploy --all -c stage=qa`  deploy this stack to your default AWS account/region
* `cdk diff -c stage=qa`    compare deployed stack with current state
* `cdk synth`   emits the synthesized CloudFormation template

## Deployment

### Configure application
Adopt stage configuration in `cdk.json`.

### Boostrap account
QA
```
cdk boostrap -c stage=qa --profile taex-qa
cdk bootstrap aws://************/us-east-1 -c stage=qa --profile taex-qa

PROD
```
cdk bootstrap -c stage=prod --profile taex-prod
cdk bootstrap aws://************/us-east-1 -c stage=prod --profile taex-prod
```

### Prepare usage of service-catalogues in account
The backend is using Covestro Service Catalogue products. For this the cdk-role needs to be added to the respective Catalogue portfolios in the respective regions.

### Add cdk role to portfolios in eu-central-1
```
aws servicecatalog associate-principal-with-portfolio --portfolio-id port-qrr2yu4vqrtpm --principal-arn arn:aws:iam::************:role/cdk-hnb659fds-cfn-exec-role-************-eu-central-1 --principal-type IAM --profile taex-qa

aws servicecatalog associate-principal-with-portfolio --portfolio-id port-vjzqee3rg3x2c --principal-arn arn:aws:iam::************:role/cdk-hnb659fds-cfn-exec-role-************-eu-central-1 --principal-type IAM --profile taex-qa
```

PROD
```
aws servicecatalog associate-principal-with-portfolio --portfolio-id port-qrr2yu4vqrtpm --principal-arn arn:aws:iam::************:role/cdk-hnb659fds-cfn-exec-role-************-eu-central-1 --principal-type IAM --profile taex-prod

aws servicecatalog associate-principal-with-portfolio --portfolio-id port-vjzqee3rg3x2c --principal-arn arn:aws:iam::************:role/cdk-hnb659fds-cfn-exec-role-************-eu-central-1 --principal-type IAM --profile taex-prod
```

### Add cdk role to portfolios in us-east-1

QA
```
aws servicecatalog associate-principal-with-portfolio --portfolio-id port-2kcwf4efeq2yk --principal-arn arn:aws:iam::************:role/cdk-hnb659fds-cfn-exec-role-************-us-east-1 --region us-east-1 --principal-type IAM --profile taex-qa

aws servicecatalog associate-principal-with-portfolio --portfolio-id port-jr7min7sastgq --principal-arn arn:aws:iam::************:role/cdk-hnb659fds-cfn-exec-role-************-us-east-1 --region us-east-1 --principal-type IAM --profile taex-qa
```

PROD
```
aws servicecatalog associate-principal-with-portfolio --portfolio-id port-2kcwf4efeq2yk --principal-arn arn:aws:iam::************:role/cdk-hnb659fds-cfn-exec-role-************-us-east-1 --region us-east-1 --principal-type IAM --profile taex-prod

aws servicecatalog associate-principal-with-portfolio --portfolio-id port-jr7min7sastgq --principal-arn arn:aws:iam::************:role/cdk-hnb659fds-cfn-exec-role-************-us-east-1 --region us-east-1 --principal-type IAM --profile taex-prod
```

### Install dependencies
```
aws codeartifact login --tool npm --repository cov-cdk --domain covestro --domain-owner 618253301100 --region eu-central-1 --namespace @cov-cdk --profile <your profile>
npm install
```

### Initial Deploy
This deployment only needs to done initially, as afterwards deployments are handled by the CI/CD pipeline.

QA
```
cdk diff -c stage=qa --profile taex-qa
cdk deploy --all -c stage=qa --profile taex-qa
```

PROD
```
cdk diff -c stage=prod --profile taex-prod
cdk deploy --all -c stage=prod --profile taex-prod
```

### Add cloudfront endpoint to Adore
- create adore dns entry go/adore
- set adore entry to Route53 entry e.g. taex-qa.<accountId>.aws.glpoly.net.

### Update Azure Configuration
- go to azure go/azure
- search for your `application` (e.g. taex-qa.covestro.net)
- change the `redirect URL` > add a platform 'WEB' and add 'https://taex-qa-covestro-net.auth.eu-central-1.amazoncognito.com/saml2/idpresponse' as url
- change `Application ID URI` > add 'Application ID URI' > set to cognito UserPoolId e.g. urn:amazon:cognito:sp:eu-central-1_pf5IB2K4Y

### Add Users
- go to azure go/azure
- serach for you `Enterprise Application` (e.g. taex-qa.covestro.net)
- Assign Users and Groups

### Deploy frontend
- Deploy the frontend to CloudFront (see Frontend Readme.md)

## Configure CICD
* CiCd Stack deploys a role that can be used for "Git lab runners"
* After deployment lookup 'RunnerRoleArn' in Cicd-Stack
* Update (Role-Arn, Cloudfront Distrubtion and S3 Bucket) .gitlab-ci.yml file in root-directory of project

* Add gitlab runner role as trustedentity to cdk-deploy role

## Synthetic test-data
TODO

## Testing
TODO