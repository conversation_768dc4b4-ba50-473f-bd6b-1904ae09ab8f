#!/usr/bin/env node
import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import { GraphQlApiStack } from '../lib/GraphQlApiStack';
import { FrontendStack } from '../lib/FrontendStack';
import { CicdStack } from '../lib/CicdStack';
import { MonitoringStack } from '../lib/MonitoringStack';
import { WorkflowStack } from '../lib/WorkflowStack';
import { P6IntegrationStack } from '../lib/P6IntegrationStack';

const app = new cdk.App();
// Retrieve the deployment stage from context
const stage = app.node.tryGetContext('stage');
if (!stage) {
  throw new Error('Error: Stage must be specified using the context parameter: -c stage=<stage>');
}

// Retrieve environment-specific configuration
const envConfig = app.node.tryGetContext(stage);
if (!envConfig) {
  throw new Error(`Error: Configuration for stage "${stage}" not found in cdk.json`);
}

// Extract environment settings from the configuration
const { accountId, region, envVariables } = envConfig;
const { appName } = envVariables;

const env = {
  account: accountId,
  region,
};

// Deploy Frontend stack with environment-specific parameters
const frontendStack = new FrontendStack(app, 'FrontendStack', {
  appName,
  env,
  stage: envConfig.envVariables.stage,
  domainName: envConfig.envVariables.domainName,
  azureApplicationOwnerEmailAddress: envConfig.envVariables.azureApplicationOwnerEmailAddress,
});

// GraphQL API Stack
const apiStack = new GraphQlApiStack(app, `${appName}GraphQlApiStack`, {
  appName,
  userPool: frontendStack.userPool,
  userVerificationQueue: frontendStack.userVerificationQueue,
  env,
});

// Workflow Stack
new WorkflowStack(app, 'WorkflowStack', {
  table: apiStack.dynamoTable,
  env: env,
})

// P6 Integration Stack
const p6IntegrationStack = new P6IntegrationStack(app, 'P6IntegrationStack-new', {
  table: apiStack.dynamoTable,
  primaveraEndpoint: envConfig.envVariables.primaveraEndpoint,
  primaveraVpcEndpointServiceName: envConfig.envVariables.primaveraVpcEndpointServiceName,
  apiUserName: envConfig.envVariables.primaveraApiUserName,
  activityUpdateQueue: apiStack.activityUpdateQueue,
  env: env,
});

// Add a dependency to ensure the GraphQL API stack is created first
p6IntegrationStack.addDependency(apiStack);

// Stacks not relevant for dev stage
if (stage == 'qa' || stage == 'prod') {

  // Cicd Stack for Gitlab runners
  new CicdStack(app, 'CicdStack', {
    appName
  })

  // Monitoring Stack for Usage Monitoring
  new MonitoringStack(app, 'MonitoringStack', {
    env,
    domainName: envConfig.envVariables.domainName,
  });


}
