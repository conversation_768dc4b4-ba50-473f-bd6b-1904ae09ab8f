<#
.SYNOPSIS
  Create and validate Cognito users via AWS CLI, using a specified profile.

.PARAMETER UserPoolId
  Your Cognito User Pool ID (e.g. us-east-1_XXXXXXXXX).

.PARAMETER TemporaryPassword
  Initial temp password for new users.

.PARAMETER PermanentPassword
  Password to set as permanent.

.PARAMETER Profile
  AWS CLI profile to use (defaults to "default").
#>
param(
    [string]$UserPoolId        = "us-east-1_XXXXXXXXX",
    [string]$TemporaryPassword = "TempP@ssw0rd!",
    [string]$PermanentPassword = "Str0ngP@ssw0rd!",
    [string]$Profile           = "default"
)

# List of users to create & validate
$users = @("peter","adam","tom","paul")

foreach ($user in $users) {
    Write-Host "=== Processing user: $user ===" -ForegroundColor Cyan

    # 1) Create the user (suppressing email)
    Write-Host "Creating user..."
    aws cognito-idp admin-create-user `
        --profile $Profile `
        --user-pool-id $UserPoolId `
        --username $user `
        --temporary-password $TemporaryPassword `
        --message-action SUPPRESS
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to create $user; skipping."
        continue
    }

    # 2) Set permanent password
    Write-Host "Setting permanent password..."
    aws cognito-idp admin-set-user-password `
        --profile $Profile `
        --user-pool-id $UserPoolId `
        --username $user `
        --password $PermanentPassword `
        --permanent
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to set password for $user; skipping."
        continue
    }

    # 3) Validate by fetching the user’s status
    Write-Host "Validating user record..."
    $userJson = aws cognito-idp admin-get-user `
        --profile $Profile `
        --user-pool-id $UserPoolId `
        --username $user `
        --output json

    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to retrieve $user; skipping."
        continue
    }

    $userRecord = $userJson | ConvertFrom-Json

    # Print out some key details
    Write-Host ("Username:        {0}" -f $userRecord.Username)
    Write-Host ("UserStatus:      {0}" -f $userRecord.UserStatus)
    Write-Host ("Enabled:         {0}" -f $userRecord.Enabled)
    Write-Host ("CreatedDate:     {0}" -f $userRecord.UserCreateDate)
    Write-Host ("LastModifiedDate:{0}" -f $userRecord.UserLastModifiedDate)
    Write-Host "`n"
}
