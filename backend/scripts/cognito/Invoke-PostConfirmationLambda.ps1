<#
.SYNOPSIS
Triggers an AWS Lambda function with a hard-coded Cognito PostConfirmation event.

.PARAMETER FunctionName
The name (or ARN) of the Lambda function to invoke. Default is "my-lambda-function".

.EXAMPLE
.\trigger-lambda.ps1 -FunctionName ConfirmSignUpHandler
#>

param(
    [Parameter(Mandatory = $false)]
    [string]$FunctionName = "FrontendStack-PostConfirmationLambdaC4F60401-GFucOUnqmrai"
)

# 1. Build the Cognito PostConfirmation event payload
$event = @{
    version       = "1"
    triggerSource = "PostConfirmation_ConfirmSignUp"
    region        = "eu-central-1"
    userPoolId    = "eu-central-1_2qC9UKHu4"
    userName      = "sally"
    callerContext = @{
        awsSdkVersion = "aws-sdk-js-2.0"
        clientId      = "exampleclientid"
    }
    request = @{
        userAttributes = @{
            email          = "<EMAIL>"
            email_verified = "true"
            sub            = "abcd1234-5678-90ef-ghij-klmnopqrstuv"
        }
    }
    response = @{}
}

# 2. Convert to JSON and save to a temp file WITHOUT BOM
$tempFile = [IO.Path]::Combine($env:TEMP, "cognito-event-$(Get-Random).json")
$json = $event | ConvertTo-Json -Depth 10

# If you're on PowerShell Core (6+) you can do:
# $json | Out-File -FilePath $tempFile -Encoding utf8NoBOM

# On Windows PowerShell 5.1, use .NET directly:
[System.IO.File]::WriteAllText(
    $tempFile,
    $json,
    (New-Object System.Text.UTF8Encoding($false))  # $false = no BOM
)

Write-Host "Event JSON written to $tempFile (UTF8 without BOM)"

# 3. Invoke the Lambda
$responseFile = [IO.Path]::Combine($env:TEMP, "lambda-response-$(Get-Random).json")
Write-Host "Invoking Lambda function '$FunctionName' in eu-central-1..."
aws lambda invoke `
    --function-name $FunctionName `
    --region eu-central-1 `
    --payload "fileb://$tempFile" `
    --cli-binary-format raw-in-base64-out `
    --profile taggo `
    $responseFile | Out-Null

# 4. Output the result
if (Test-Path $responseFile) {
    Write-Host "Lambda invocation complete. Response saved to $responseFile"
    Get-Content $responseFile | Write-Host
} else {
    Write-Error "Lambda invocation failed or no response file created."
}

# 5. Cleanup (optional)
# Remove-Item $tempFile, $responseFile -Force
