{"projects": [{"name": "Refinery Shutdown 2024", "description": "Comprehensive maintenance and upgrades for the refinery plant.", "plantId": "plant-001", "status": "PENDING", "autoClose": true, "allowedGroups": ["contractora"], "timeline": {"start": "2024-03-05T00:00:00Z", "end": "2024-03-06T00:00:00Z"}, "workPackages": [{"name": "Replace Heat Exchanger HX-120", "description": "Replace the damaged heat exchanger with a new unit.", "dependencies": [], "stopPointNo": "SP-001", "stopPointDescription": "Heat exchanger isolation point", "plannerName": "<PERSON>", "stoScopeRequestNo": "81283923", "sapWorkOrderNo": "91203aaaa", "sapNotification": "notificationa", "equipmentNr": "akdfj09a9sd", "equipmentType": "Piping", "activities": [{"name": "Isolate HX-120", "activityId": "Syn-301", "description": "Isolate the heat exchanger from the system for maintenance.", "assignedTo": "HeatTech Services Ltd.", "status": "IN_PROGRESS", "progress": 50, "sequenceNo": 1, "plannedEffort": 4, "plannedTimeline": {"start": "2024-03-05T00:00:00Z", "end": "2024-03-06T00:00:00Z"}, "actualTimeline": {"start": "2024-03-05T00:00:00Z", "end": "2024-03-06T00:00:00Z"}, "attachments": [{"name": "Bedienungsanleitung.pdf", "key": "12345678"}], "resources": [{"resourceName": "Engineer", "resourceCode": "ENG-011", "discipline": "Mechanical"}], "comments": [{"author": "<PERSON>", "text": "The heat exchanger is in need of maintenance. Can you do it today?", "createdAt": "2024-03-05T00:00:00Z"}], "allowedGroups": ["contractora"]}, {"name": "Install New HX-120", "activityId": "Syn-302", "description": "Install the new heat exchanger and connect piping.", "assignedTo": "MechSolutions Inc.", "status": "COMPLETED", "progress": 0, "sequenceNo": 2, "plannedEffort": 8, "plannedTimeline": {"start": "2024-03-05T00:00:00Z", "end": "2024-03-06T00:00:00Z"}, "actualTimeline": {"start": "2024-03-05T00:00:00Z", "end": "2024-03-06T00:00:00Z"}, "resources": [{"resourceName": "Engineer", "resourceCode": "ENG-011", "discipline": "Mechanical"}], "allowedGroups": []}, {"name": "Install New HX-120", "activityId": "Syn-303", "description": "Install the new heat exchanger and connect piping.", "assignedTo": "MechSolutions Inc.", "status": "COMPLETED", "progress": 0, "sequenceNo": 2, "plannedEffort": 8, "plannedTimeline": {"start": "2024-03-05T00:00:00Z", "end": "2024-03-06T00:00:00Z"}, "actualTimeline": {"start": "2024-03-05T00:00:00Z", "end": "2024-03-06T00:00:00Z"}, "resources": [{"resourceName": "Engineer", "resourceCode": "ENG-011", "discipline": "Mechanical"}], "allowedGroups": []}, {"name": "Install New HX-120", "activityId": "Syn-302", "description": "Install the new heat exchanger and connect piping.", "assignedTo": "MechSolutions Inc.", "status": "COMPLETED", "progress": 0, "sequenceNo": 2, "plannedEffort": 8, "plannedTimeline": {"start": "2024-03-05T00:00:00Z", "end": "2024-03-06T00:00:00Z"}, "actualTimeline": {"start": "2024-03-05T00:00:00Z", "end": "2024-03-08T00:00:00Z"}, "resources": [{"resourceName": "Engineer", "resourceCode": "ENG-011", "discipline": "Mechanical"}], "allowedGroups": []}, {"name": "Install New HX-120", "activityId": "Syn-302", "description": "Install the new heat exchanger and connect piping.", "assignedTo": "MechSolutions Inc.", "status": "COMPLETED", "progress": 0, "sequenceNo": 2, "plannedEffort": 8, "plannedTimeline": {"start": "2024-03-05T00:00:00Z", "end": "2024-03-06T00:00:00Z"}, "actualTimeline": {"start": "2024-03-05T00:00:00Z", "end": "2024-03-08T00:00:00Z"}, "resources": [{"resourceName": "Engineer", "resourceCode": "ENG-011", "discipline": "Mechanical"}], "allowedGroups": []}, {"name": "Install New HX-120", "activityId": "Syn-302", "description": "Install the new heat exchanger and connect piping.", "assignedTo": "MechSolutions Inc.", "status": "COMPLETED", "progress": 0, "sequenceNo": 2, "plannedEffort": 12, "plannedTimeline": {"start": "2024-03-05T00:00:00Z", "end": "2024-03-06T00:00:00Z"}, "actualTimeline": {"start": "2024-03-05T00:00:00Z", "end": "2024-03-10T00:00:00Z"}, "resources": [{"resourceName": "Engineer", "resourceCode": "ENG-011", "discipline": "Mechanical"}], "allowedGroups": []}, {"name": "Test New HX-120", "activityId": "Syn-303", "description": "Perform pressure and performance tests on the new heat exchanger.", "assignedTo": "HeatTech Services Ltd.", "status": "COMPLETED", "progress": 0, "plannedEffort": 6, "sequenceNo": 3, "unableToWork": true, "plannedTimeline": {"start": "2024-03-06T00:00:00Z", "end": "2024-03-06T12:00:00Z"}, "resources": [{"resourceName": "Engineer", "resourceCode": "ENG-011", "discipline": "Mechanical"}], "allowedGroups": []}, {"name": "Update Documentation", "activityId": "Syn-304", "description": "Update the technical documentation with details of the new heat exchanger.", "assignedTo": "Admin Team", "status": "COMPLETED", "sequenceNo": 4, "progress": 0, "plannedTimeline": {"start": "2024-03-07T00:00:00Z", "end": "2024-03-07T12:00:00Z"}, "actualTimeline": {"start": "2024-03-07T00:00:00Z", "end": "2024-03-07T12:00:00Z"}, "resources": [{"resourceName": "Admin", "resourceCode": "ENG-011", "discipline": "Documentation"}], "allowedGroups": []}]}, {"name": "Inspect Reactor R-200", "description": "Perform a detailed inspection of Reactor R-200.", "dependencies": [], "stopPointNo": "SP-002", "stopPointDescription": "Reactor isolation point", "plannerName": "<PERSON>", "activities": [{"activityId": "Syn-001", "name": "Prepare R-200 for Inspection", "description": "Drain and clean Reactor R-200.", "assignedTo": "ProcessTech Solutions", "status": "PENDING", "sequenceNo": 5, "progress": 0, "plannedEffort": 10, "plannedTimeline": {"start": "2024-03-02T00:00:00Z", "end": "2024-03-02T00:00:00Z"}, "resources": [{"resourceName": "Engineer", "resourceCode": "ENG-011", "discipline": "Mechanical"}], "allowedGroups": ["contractora"]}, {"name": "Conduct Non-Destructive Testing", "activityId": "Syn-002", "description": "Perform NDT to identify material weaknesses in Reactor R-200.", "assignedTo": "InspectPro Ltd.", "status": "PENDING", "progress": 0, "plannedTimeline": {"start": "2024-03-03T00:00:00Z", "end": "2024-03-03T12:00:00Z"}, "resources": [{"resourceName": "Engineer", "resourceCode": "ENG-011", "discipline": "Mechanical"}], "allowedGroups": []}]}]}, {"name": "Utility Unit Overhaul 2024", "description": "Overhaul of Utility Unit including boilers and compressors.", "plantId": "plant-002", "status": "COMPLETED", "allowedGroups": ["contractora"], "timeline": {"start": "2024-03-05T00:00:00Z", "end": "2024-03-06T00:00:00Z"}, "workPackages": [{"name": "Boiler B-101 Overhaul", "description": "Complete overhaul of Boiler B-101.", "dependencies": [], "stopPointNo": "SP-003", "stopPointDescription": "Boiler isolation point", "plannerName": "<PERSON>", "stoScopeRequestNo": "81283923", "sapWorkOrderNo": "91203aaaa", "sapNotification": "notificationa", "equipmentNr": "akdfj09a9sd", "equipmentType": "Pump", "activities": [{"name": "Inspect Boiler B-101", "activityId": "Syn-101", "description": "Perform a detailed inspection of Boiler B-101.", "assignedTo": "BoilerInspect Co.", "status": "PENDING", "progress": 0, "plannedEffort": 12, "sequenceNo": 1, "plannedTimeline": {"start": "2024-03-05T00:00:00Z", "end": "2024-03-06T00:00:00Z"}, "resources": [{"resourceName": "Inspector", "resourceCode": "INS-011", "discipline": "Mechanical"}], "allowedGroups": ["contractora"]}, {"name": "Replace <PERSON><PERSON>", "activityId": "Syn-201", "description": "Replace damaged tubes in Boiler B-101.", "assignedTo": "TubeFixers Inc.", "status": "PENDING", "progress": 0, "plannedEffort": 30, "plannedTimeline": {"start": "2024-03-06T00:00:00Z", "end": "2024-03-06T12:00:00Z"}, "resources": [{"resourceName": "Inspector", "resourceCode": "INS-011", "discipline": "Mechanical"}], "allowedGroups": []}]}]}]}