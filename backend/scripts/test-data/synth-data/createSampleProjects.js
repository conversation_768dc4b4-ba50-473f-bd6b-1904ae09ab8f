// Import necessary libraries
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const AmazonCognitoIdentity = require('amazon-cognito-identity-js');
const fetch = require('node-fetch');

// AppSync endpoint and Cognito details
const APPSYNC_URL = process.env.APPSYNC_URL;
const COGNITO_USER_POOL_ID = process.env.COGNITO_USER_POOL_ID;
const COGNITO_CLIENT_ID = process.env.COGNITO_CLIENT_ID;
const COGNITO_USERNAME = process.env.COGNITO_USERNAME;
const COGNITO_PASSWORD = process.env.COGNITO_PASSWORD;

// GraphQL mutation to add a project
const ADD_PROJECT_MUTATION = `
  mutation AddProject($input: ProjectInput!) {
    addProject(input: $input) {
      id
      name
    }
  }
`;

// Load projects data from test-projects.json
const loadProjects = () => {
  try {
    const data = fs.readFileSync('./test-projects.json', 'utf8');
    const parsedData = JSON.parse(data);
    return parsedData.projects;
  } catch (err) {
    console.error('Error reading test-projects.json:', err);
    return [];
  }
};

const projects = loadProjects();

// Function to authenticate with Cognito and get a token
const authenticateUser = () => {
  return new Promise((resolve, reject) => {
    const authenticationDetails = new AmazonCognitoIdentity.AuthenticationDetails({
      Username: COGNITO_USERNAME,
      Password: COGNITO_PASSWORD,
    });

    const poolData = {
      UserPoolId: COGNITO_USER_POOL_ID,
      ClientId: COGNITO_CLIENT_ID,
    };
    const userPool = new AmazonCognitoIdentity.CognitoUserPool(poolData);
    const userData = {
      Username: COGNITO_USERNAME,
      Pool: userPool,
    };
    const cognitoUser = new AmazonCognitoIdentity.CognitoUser(userData);

    cognitoUser.authenticateUser(authenticationDetails, {
      onSuccess: (result) => {
        const accessToken = result.getAccessToken().getJwtToken();
        resolve(accessToken);
      },
      onFailure: (err) => {
        reject(err);
      },
    });
  });
};

// Function to create projects
const createProject = async (project, token) => {
  try {
    const response = await axios.post(
      APPSYNC_URL,
      {
        query: ADD_PROJECT_MUTATION,
        variables: { input: project }
      },
      {
        headers: {
          Authorization: token,
          'Content-Type': 'application/json'
        }
      }
    );

    if (response.data.errors) {
      console.error('GraphQL errors:', response.data.errors);
    } else {
      console.log(`Project created: ${response.data.data.addProject.name} (ID: ${response.data.data.addProject.id})`);
    }
  } catch (error) {
    console.error('Error creating project:', error.response ? error.response.data : error.message);
  }
};

// Main function to create sample projects
const main = async () => {
  try {
    const token = await authenticateUser();
    for (const project of projects) {
      await createProject(project, token);
    }
  } catch (error) {
    console.error('Error during authentication or project creation:', error);
  }
};

main();
