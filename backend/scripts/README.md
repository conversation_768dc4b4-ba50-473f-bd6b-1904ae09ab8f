# Setup Guide for AppSync Sample Projects Script

## Prerequisites

- Node.js (version 12 or higher)
- AWS credentials for accessing the Cognito user pool
- Powershell for setting environment variables

## Installation

1. Clone the repository:
   ```powershell
   git clone https://github.com/your-repository/appsync-sample-projects.git
   cd appsync-sample-projects
   ```

2. Install the required dependencies:
   ```powershell
   npm install
   ```


---- Create Test Data ----

## Setting Environment Variables

Before running the script, you need to set the required environment variables. You can do this via PowerShell by running the following commands:

DEV
```powershell
$env:APPSYNC_URL = "https://eipkzxeqmja4jdrs56thxa4jtq.appsync-api.eu-central-1.amazonaws.com/graphql"
$env:COGNITO_USER_POOL_ID = "eu-central-1_2qC9UKHu4"
$env:COGNITO_CLIENT_ID = "69j26hfquq6lncd6esj2nagn2o"
$env:COGNITO_USERNAME = "your_username"
$env:COGNITO_PASSWORD = "your_password"
```

QA
```powershell
$env:APPSYNC_URL = "https://lsuz6iuv7fednellcrriccguxe.appsync-api.eu-central-1.amazonaws.com/graphql"
$env:COGNITO_USER_POOL_ID = "eu-central-1_pf5IB2K4Y"
$env:COGNITO_CLIENT_ID = "49p5k7097mco5ujce6bl2f98ab"
$env:COGNITO_USERNAME = "your_username"
$env:COGNITO_PASSWORD = "your_password"
```


Replace each placeholder value (`your_user_pool_id`, `your_client_id`, etc.) with the appropriate value for your AWS setup.

## Running the Script

To execute the script after setting the environment variables, run:

```powershell
node createSampleProjects.js
```

This will create the sample projects by interacting with the AppSync API using Cognito authentication.

## Troubleshooting

- Make sure all environment variables are correctly set.
- Check the `test-projects.json` file for proper formatting.
- Ensure you have the correct permissions for the Cognito user to access the AppSync endpoint.

## License

This project is licensed under the MIT License.


---- CDK Scripts ----

# Setting Environment Variables

Before running the script, you need to set the required environment variables. You can do this via PowerShell by running the following commands:

DEV
```powershell
$env:PROFILE_NAME = "dev"
$env:COGNITO_USER_POOL_ID = "eu-central-1_pf5IB2K4Y"
$env:REGION = "eu-central-1"
```

QA
```powershell
$env:PROFILE_NAME = "qa"
$env:COGNITO_USER_POOL_ID = "eu-central-1_pf5IB2K4Y"
$env:REGION = "eu-central-1"
```