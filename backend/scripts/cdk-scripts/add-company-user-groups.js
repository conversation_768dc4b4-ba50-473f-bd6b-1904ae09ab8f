import { CognitoIdentityProviderClient, CreateGroupCommand } from "@aws-sdk/client-cognito-identity-provider";
import { fromIni } from "@aws-sdk/credential-provider-ini";

// Define the AWS CLI profile and region
const PROFILE_NAME = process.env.PROFILE_NAME;  // Change to your desired AWS CLI profile
const REGION = process.env.REGION; // Change to your AWS region

// Initialize the Cognito client with the specified profile
const client = new CognitoIdentityProviderClient({
    region: REGION,
    credentials: fromIni({ profile: PROFILE_NAME }),
});

// Define the Cognito User Pool ID
const USER_POOL_ID = process.env.COGNITO_USER_POOL_ID; // Replace with your actual User Pool ID

// List of companies for which groups should be created
const companies = ["Covestro", "FPT"]; // Add more companies if needed

// Function to create a user pool group
const createGroup = async (groupName) => {
    try {
        const command = new CreateGroupCommand({
            GroupName: groupName,
            UserPoolId: USER_POOL_ID,
            Description: `Group for ${groupName}`,
        });

        const response = await client.send(command);
        console.log(`✅ Successfully created group: ${groupName}`);
        return response;
    } catch (error) {
        console.error(`❌ Failed to create group: ${groupName} -`, error.message);
    }
};

// Main function to create all groups
const createAllGroups = async () => {
    console.log("🚀 Starting group creation...");
    
    for (const company of companies) {
        const groupName = `Company_${company}`;
        await createGroup(groupName);
    }

    console.log("✅ All groups have been processed.");
};

// Execute the function
createAllGroups();