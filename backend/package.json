{"name": "backend", "version": "0.1.0", "bin": {"backend": "bin/backend.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest --verbose", "cdk": "cdk"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "22.7.9", "aws-cdk": "2.166.0", "axios": "^1.7.7", "jest": "^29.7.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "~5.6.3", "uuid": "^11.0.3"}, "dependencies": {"@cov-cdk/alpha-app-patterns": "^0.12.4", "aws-cdk-lib": "2.166.0", "constructs": "^10.0.0", "source-map-support": "^0.5.21"}}