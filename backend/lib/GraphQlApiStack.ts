import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as appsync from 'aws-cdk-lib/aws-appsync';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as cognito from 'aws-cdk-lib/aws-cognito';
import * as sqs from 'aws-cdk-lib/aws-sqs';
import { AttachmentHandling } from './constructs/AttachmentHandling';
import { DynamoEventSource, SqsEventSource } from 'aws-cdk-lib/aws-lambda-event-sources';

interface GraphQlApiStackProps extends cdk.StackProps {
  appName: string;
  userPool: cognito.IUserPool;
  userVerificationQueue: sqs.Queue;
}

export class GraphQlApiStack extends cdk.Stack {
  public readonly dynamoTable: dynamodb.Table;
  public readonly activityUpdateQueue: sqs.Queue;

  constructor(scope: Construct, id: string, props: GraphQlApiStackProps) {
    super(scope, id, props);

    const { appName, userPool } = props;

    // Create AppSync API
    const graphqlApi = new appsync.GraphqlApi(this, `${appName}GraphqlApi`, {
      name: `${appName}-GraphqlApi`,
      schema: appsync.SchemaFile.fromAsset('../schema/schema.graphql'), // Path to your schema file
      authorizationConfig: {
        defaultAuthorization: {
          authorizationType: appsync.AuthorizationType.USER_POOL,
          userPoolConfig: {
            userPool,
          },
        },
      },
    });

    // Create DynamoDB Table
    this.dynamoTable = new dynamodb.Table(this, `${appName}Table`, {
      partitionKey: { name: 'PK', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'SK', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      stream: dynamodb.StreamViewType.NEW_AND_OLD_IMAGES,
    });

    // Construct add S3 Bucket, Signing Url and API resolvers to handle file attachments
    const { uploadBucket } = AttachmentHandling(this, {
      appName,
      graphqlApi,
      dynamoTable: this.dynamoTable,
    });

    // Add GSI - Global Secondary Indices
    const gsiDefinitions = [
      { name: 'type-index', key: 'type' },
      { name: 'project-contract-index', key: 'PROJECT#CONTRACTOR' },
      { name: 'projectObjectId-index', key: 'projectObjectId' },
      { name: 'predecessorActivityObjectId-index', key: 'predecessorActivityObjectId' },
      { name: 'successorActivityObjectId-index', key: 'successorActivityObjectId' },
    ];

    gsiDefinitions.forEach(({ name, key }) =>
      this.dynamoTable.addGlobalSecondaryIndex({
        indexName: name,
        partitionKey: { name: key, type: dynamodb.AttributeType.STRING },
      })
    );

    // Create Lambda Function
    const lambdaFunction = new lambda.Function(this, `${appName}ResolverFunction`, {
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('lambda/graphql-api', {
        exclude: ['node_modules', '**/__tests__/*']
      }),
      environment: {
        TABLE_NAME: this.dynamoTable.tableName,
        S3_BUCKET_NAME: uploadBucket.bucketName,
      },
      timeout: cdk.Duration.seconds(15),
      memorySize: 512,
    });

    // Grant Lambda permission to access DynamoDB
    this.dynamoTable.grantReadWriteData(lambdaFunction);

    // Grant Lambda permission to access bucket
    uploadBucket.grantReadWrite(lambdaFunction);

    // Attach Lambda to AppSync as a data source
    const lambdaDataSource = graphqlApi.addLambdaDataSource(`${appName}LambdaDataSource`, lambdaFunction);

    // Resolvers for SignedUrls
    lambdaDataSource.createResolver('generateSignedUrls', { typeName: 'Mutation', fieldName: 'getUploadUrl' });
    lambdaDataSource.createResolver('generateSignedUrlForDownload', { typeName: 'Mutation', fieldName: 'getDownloadUrl' });
    lambdaDataSource.createResolver('generateSignedUrlForDelete', { typeName: 'Mutation', fieldName: 'getDeleteUrl' });

    // Resolvers for Queries
    lambdaDataSource.createResolver('GetProjectResolver', {
      typeName: 'Query',
      fieldName: 'getProject',
    });

    lambdaDataSource.createResolver('ListProjectsResolver', {
      typeName: 'Query',
      fieldName: 'listProjects',
    });


    lambdaDataSource.createResolver('GetActivityResolver', {
      typeName: 'Query',
      fieldName: 'getActivity',
    });

    lambdaDataSource.createResolver('ListActivitiesResolver', {
      typeName: 'Query',
      fieldName: 'listActivities',
    });


    // Resolvers for Mutations
    lambdaDataSource.createResolver('AddProjectResolver', {
      typeName: 'Mutation',
      fieldName: 'addProject',
    });

    lambdaDataSource.createResolver('UpdateProjectResolver', {
      typeName: 'Mutation',
      fieldName: 'updateProject',
    });

    lambdaDataSource.createResolver('DeleteProjectResolver', {
      typeName: 'Mutation',
      fieldName: 'deleteProject',
    });


    lambdaDataSource.createResolver('AddActivityResolver', {
      typeName: 'Mutation',
      fieldName: 'addActivity',
    });

    lambdaDataSource.createResolver('UpdateActivityResolver', {
      typeName: 'Mutation',
      fieldName: 'updateActivity',
    });

    lambdaDataSource.createResolver('UpdateActivitiesResolver', {
      typeName: 'Mutation',
      fieldName: 'updateActivities',
    });

    lambdaDataSource.createResolver('DeleteActivityResolver', {
      typeName: 'Mutation',
      fieldName: 'deleteActivity',
    });

    // Resolvers for Field-Level Resolvers


    lambdaDataSource.createResolver('ProjectActivityResolver', {
      typeName: 'Project',
      fieldName: 'activities',
    });

    lambdaDataSource.createResolver('ProjectManagerGroupsResolver', {
      typeName: 'Project',
      fieldName: 'managerGroups',
    });

    lambdaDataSource.createResolver('ProjectWorkerGroupsResolver', {
      typeName: 'Project',
      fieldName: 'workerGroups',
    });

    lambdaDataSource.createResolver('ProjectOperatorGroupsResolver', {
      typeName: 'Project',
      fieldName: 'operatorGroups',
    });

    lambdaDataSource.createResolver('GetCountByStatus', {
      typeName: 'Query',
      fieldName: 'getCountByStatus',
    });

    lambdaDataSource.createResolver('GetCompletedActivitiesByDay', {
      typeName: 'Query',
      fieldName: 'getCompletedActivitiesByDay',
    });

    lambdaDataSource.createResolver('GetPlannedActivitiesByDay', {
      typeName: 'Query',
      fieldName: 'getPlannedActivitiesByDay',
    });

    lambdaDataSource.createResolver('GetCountOfUnableToWorkActivities', {
      typeName: 'Query',
      fieldName: 'getCountOfUnableToWorkActivities',
    });

    lambdaDataSource.createResolver('GetActivityCountGroupedByStatusAndDiscipline', {
      typeName: 'Query',
      fieldName: 'getActivityCountGroupedByStatusAndDiscipline',
    });

    // SyncJob Resolvers
    lambdaDataSource.createResolver('ListSyncJobsResolver', {
      typeName: 'Query',
      fieldName: 'listSyncJobs',
    });

    // Group Resolvers
    lambdaDataSource.createResolver('GetGroupResolver', {
      typeName: 'Query',
      fieldName: 'getGroup',
    });

    lambdaDataSource.createResolver('ListGroupsResolver', {
      typeName: 'Query',
      fieldName: 'listGroups',
    });

    lambdaDataSource.createResolver('CreateGroupResolver', {
      typeName: 'Mutation',
      fieldName: 'createGroup',
    });

    lambdaDataSource.createResolver('UpdateGroupResolver', {
      typeName: 'Mutation',
      fieldName: 'updateGroup',
    });

    lambdaDataSource.createResolver('DeleteGroupResolver', {
      typeName: 'Mutation',
      fieldName: 'deleteGroup',
    });

    // User Resolvers
    lambdaDataSource.createResolver('GetUserResolver', {
      typeName: 'Query',
      fieldName: 'getUser',
    });

    lambdaDataSource.createResolver('ListUsersResolver', {
      typeName: 'Query',
      fieldName: 'listUsers',
    });

    lambdaDataSource.createResolver('CreateUserResolver', {
      typeName: 'Mutation',
      fieldName: 'createUser',
    });

    lambdaDataSource.createResolver('DeleteUserResolver', {
      typeName: 'Mutation',
      fieldName: 'deleteUser',
    });

    lambdaDataSource.createResolver('UpdateUserResolver', {
      typeName: 'Mutation',
      fieldName: 'updateUser',
    });

    lambdaDataSource.createResolver('AddUserToGroupResolver', {
      typeName: 'Mutation',
      fieldName: 'addUserToGroup',
    });

    lambdaDataSource.createResolver('RemoveUserFromGroupResolver', {
      typeName: 'Mutation',
      fieldName: 'removeUserFromGroup',
    });

    // Contractor Resolvers
    lambdaDataSource.createResolver('GetContractorResolver', {
      typeName: 'Query',
      fieldName: 'getContractor',
    });

    lambdaDataSource.createResolver('ListContractorsResolver', {
      typeName: 'Query',
      fieldName: 'listContractors',
    });

    lambdaDataSource.createResolver('CreateContractorResolver', {
      typeName: 'Mutation',
      fieldName: 'createContractor',
    });

    lambdaDataSource.createResolver('UpdateContractorResolver', {
      typeName: 'Mutation',
      fieldName: 'updateContractor',
    });

    lambdaDataSource.createResolver('DeleteContractorResolver', {
      typeName: 'Mutation',
      fieldName: 'deleteContractor',
    });

    lambdaDataSource.createResolver('AddUserToContractorResolver', {
      typeName: 'Mutation',
      fieldName: 'addUserToContractor',
    });

    lambdaDataSource.createResolver('RemoveUserFromContractorResolver', {
      typeName: 'Mutation',
      fieldName: 'removeUserFromContractor',
    });

    // Field resolvers for User and Contractor types
    lambdaDataSource.createResolver('UserContractorsResolver', {
      typeName: 'User',
      fieldName: 'contractors',
    });

    lambdaDataSource.createResolver('ContractorUsersResolver', {
      typeName: 'Contractor',
      fieldName: 'users',
    });

    lambdaDataSource.createResolver('ActivityContractorResolver', {
      typeName: 'Activity',
      fieldName: 'contractor',
    });

    lambdaDataSource.createResolver('ActivityPredecessorActivitiesResolver', {
      typeName: 'Activity',
      fieldName: 'predecessorActivities',
    });

    lambdaDataSource.createResolver('ActivitySuccessorActivitiesResolver', {
      typeName: 'Activity',
      fieldName: 'successorActivities',
    });

    lambdaDataSource.createResolver('ActivitySyncStatusResolver', {
      typeName: 'Activity',
      fieldName: 'syncStatus',
    });

    // Create an SQS Queue for activity updates
    this.activityUpdateQueue = new sqs.Queue(this, `${appName}ActivityUpdateQueue`, {
      visibilityTimeout: cdk.Duration.seconds(180), // Match Lambda timeout
      retentionPeriod: cdk.Duration.days(4),
    });

    // Grant the Lambda function permission to send messages to the queue
    this.activityUpdateQueue.grantSendMessages(lambdaFunction);

    // Update Lambda environment variables to include the queue URL
    lambdaFunction.addEnvironment('ACTIVITY_UPDATE_QUEUE_URL', this.activityUpdateQueue.queueUrl);

    // Create a Lambda function to process user verification messages
    const userProcessorLambda = new lambda.Function(this, `${appName}UserProcessorFunction`, {
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('lambda/user-processor'),
      environment: {
        TABLE_NAME: this.dynamoTable.tableName,
      },
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
    });

    // Grant Lambda permission to access DynamoDB
    this.dynamoTable.grantWriteData(userProcessorLambda);

    // Create event source mapping to connect SQS to Lambda
    userProcessorLambda.addEventSource(
      new SqsEventSource(props.userVerificationQueue, {
        batchSize: 10,
      })
    );

    // Output the API URL and resource identifiers
    new cdk.CfnOutput(this, 'GraphqlApiUrl', {
      value: graphqlApi.graphqlUrl,
    });

    new cdk.CfnOutput(this, 'TableName', {
      value: this.dynamoTable.tableName,
    });

    new cdk.CfnOutput(this, 'LambdaFunctionName', {
      value: lambdaFunction.functionName,
    });

    new cdk.CfnOutput(this, 'ActivityUpdateQueueUrl', {
      value: this.activityUpdateQueue.queueUrl,
    });

    new cdk.CfnOutput(this, 'UserProcessorLambdaName', {
      value: userProcessorLambda.functionName,
    });
  }
}
