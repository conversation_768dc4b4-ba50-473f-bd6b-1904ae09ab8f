import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as sfn from 'aws-cdk-lib/aws-stepfunctions';
import * as tasks from 'aws-cdk-lib/aws-stepfunctions-tasks';
import { ITable } from 'aws-cdk-lib/aws-dynamodb';
import * as sqs from 'aws-cdk-lib/aws-sqs';
import { DynamoEventSource } from 'aws-cdk-lib/aws-lambda-event-sources';

interface WorkflowStackProps extends cdk.StackProps {
    table: ITable;
}

export class WorkflowStack extends cdk.Stack {
    public readonly activityUpdateQueue: sqs.Queue;

    constructor(scope: Construct, id: string, props: WorkflowStackProps) {
        super(scope, id, props);

        const { table } = props;

        // Create an SQS queue to store activity updates
        this.activityUpdateQueue = new sqs.Queue(this, 'ActivityUpdateQueue', {
            visibilityTimeout: cdk.Duration.seconds(30),
            retentionPeriod: cdk.Duration.days(1),
        });

        // Define Lambda functions for each task
        const handleProgress100Lambda = new lambda.Function(this, 'HandleProgress100Lambda', {
            runtime: lambda.Runtime.NODEJS_LATEST,
            handler: 'index.handler',
            code: lambda.Code.fromAsset('lambda/workflow/handleProgress100'),
            environment: {
                TABLE_NAME: table.tableName,
                ACTIVITY_UPDATE_QUEUE: this.activityUpdateQueue.queueUrl
            },
        });

        table.grantReadWriteData(handleProgress100Lambda)
        this.activityUpdateQueue.grantSendMessages(handleProgress100Lambda)

        const handleProgressFromZeroLambda = new lambda.Function(this, 'HandleProgressFromZeroLambda', {
            runtime: lambda.Runtime.NODEJS_LATEST,
            handler: 'index.handler',
            code: lambda.Code.fromAsset('lambda/workflow/handleProgressFromZeroTask'),
            environment: {
                TABLE_NAME: table.tableName,
                ACTIVITY_UPDATE_QUEUE: this.activityUpdateQueue.queueUrl
            },
        });

        table.grantReadWriteData(handleProgressFromZeroLambda)
        this.activityUpdateQueue.grantSendMessages(handleProgressFromZeroLambda)

        // Create Step Functions tasks wrapping the Lambda functions
        const handleProgress100Task = new tasks.LambdaInvoke(this, 'Handle Progress 100', {
            lambdaFunction: handleProgress100Lambda,
            outputPath: '$.Payload',
        });

        const handleProgressFromZeroTask = new tasks.LambdaInvoke(this, 'Handle Progress From Zero', {
            lambdaFunction: handleProgressFromZeroLambda,
            outputPath: '$.Payload',
        });

        // Define Choice states after conversion
        const checkProgress = new sfn.Choice(this, 'Check Progress')
            .when(sfn.Condition.numberEquals('$.newImage.percentComplete', 1), handleProgress100Task)
            .when(
                sfn.Condition.and(
                    sfn.Condition.numberEquals('$.oldImage.percentComplete', 0),
                    sfn.Condition.numberGreaterThan('$.newImage.percentComplete', 0)
                ),
                handleProgressFromZeroTask
            )
            .otherwise(new sfn.Pass(this, 'Skip Progress'));

        // Workflow Chain with Pass state for conversion
        const workflowChain = checkProgress.afterwards()
            .next(new sfn.Succeed(this, 'Workflow Succeeded'));

        // Create the state machine
        const stateMachine = new sfn.StateMachine(this, 'ActivityWorkflowStateMachine', {
            definition: workflowChain,
            timeout: cdk.Duration.minutes(5),
        });


        // Create Lambda Function for DynamoDB Stream Events
        const streamHandler = new lambda.Function(this, 'StreamHandler', {
            runtime: lambda.Runtime.NODEJS_LATEST,
            handler: 'index.handler',
            code: lambda.Code.fromAsset('lambda/workflow/handleDynamoStream'),
            environment: {
                STATE_MACHINE_ARN: stateMachine.stateMachineArn,
            }
        });

        // Grant Permissions for Lambda to Start the State Machine
        stateMachine.grantStartExecution(streamHandler);

        // Configure DynamoDB Stream as Event Source for Lambda
        streamHandler.addEventSource(new DynamoEventSource(table, {
            startingPosition: lambda.StartingPosition.LATEST,
            batchSize: 5, // adjust as needed
        }));
    }
}
