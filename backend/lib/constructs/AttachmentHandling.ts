import * as cdk from 'aws-cdk-lib';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as appsync from 'aws-cdk-lib/aws-appsync';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import { Construct } from 'constructs';

interface AttachmentHandlingProps {
  appName: string;
  graphqlApi: appsync.GraphqlApi;
  dynamoTable: dynamodb.Table;
}

export function AttachmentHandling(scope: Construct, props: AttachmentHandlingProps) {
  const { appName } = props;

  // Create S3 bucket for uploads
  const uploadBucket = new s3.Bucket(scope, `${appName}-UploadBucket`, {
    cors: [
      {
        allowedHeaders: ['*'],
        allowedMethods: [
          s3.HttpMethods.GET,
          s3.HttpMethods.PUT,
          s3.HttpMethods.POST,
          s3.HttpMethods.DELETE,
          s3.HttpMethods.HEAD,
        ],
        allowedOrigins: [
          'http://localhost:4200',
          `https://${appName}-qa.covestro.net`,
          `https://${appName}.covestro.net`,
        ],
        exposedHeaders: [
          'x-amz-server-side-encryption',
          'x-amz-request-id',
          'x-amz-id-2',
          'ETag',
        ],
        maxAge: 3000,
      },
    ],
  });

  return { uploadBucket };
}
