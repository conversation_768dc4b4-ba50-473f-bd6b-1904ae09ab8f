import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as cognito from 'aws-cdk-lib/aws-cognito';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as sqs from 'aws-cdk-lib/aws-sqs';
import * as iam from 'aws-cdk-lib/aws-iam';
import { IntranetStaticWebsite } from '@cov-cdk/alpha-app-patterns';

interface FrontendStackProps extends cdk.StackProps {
  appName: string;
  domainName: string;
  azureApplicationOwnerEmailAddress: string;
  stage: string
}

export class FrontendStack extends cdk.Stack {
  public readonly userPool: cognito.IUserPool;
  public readonly userVerificationQueue: sqs.Queue;

  constructor(scope: Construct, id: string, props: FrontendStackProps) {
    super(scope, id, props);

    const { appName, domainName, azureApplicationOwnerEmailAddress, stage } = props;

    if (stage == 'qa' || stage == 'prod') {
      const website = new IntranetStaticWebsite(this, 'IntranetStaticWebsite', {
        domainName: domainName,
        azureApplicationOwnerEmailAddress: azureApplicationOwnerEmailAddress,
        errorResponses: [
          {
            httpStatus: 403,
            responseHttpStatus: 200,
            responsePagePath: '/index.html'
          }
        ]
       });
       this.userPool = website.userPool;
    } else {
      // if stage == development we are only deploying a user-pool without frontend delivery
      this.userPool = new cognito.UserPool(this, 'UserPool', {
        userPoolName: `${appName}-user-pool`,
        removalPolicy: cdk.RemovalPolicy.DESTROY,
      });

      // add app client to userpool
      new cognito.UserPoolClient(this, 'UserPoolClient', {
        userPool: this.userPool,
        userPoolClientName: `${appName}-app-client`,
      });

    }
    
    const cfnUserPoolGroup = new cognito.CfnUserPoolGroup(this, 'MyCfnUserPoolGroup', {
      userPoolId: this.userPool.userPoolId,
      description: 'Group for admins',
      groupName: 'admin',
    });

    // Create SQS queue for user verification events
    this.userVerificationQueue = new sqs.Queue(this, 'UserVerificationQueue', {
      visibilityTimeout: cdk.Duration.seconds(300),
      retentionPeriod: cdk.Duration.days(4),
    });

    // Create post confirmation Lambda
    const postConfirmationLambda = new lambda.Function(this, 'PostConfirmationLambda', {
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('lambda/post-confirmation'),
      environment: {
        QUEUE_URL: this.userVerificationQueue.queueUrl,
      },
    });

    // Grant Lambda permission to send messages to the queue
    this.userVerificationQueue.grantSendMessages(postConfirmationLambda);

    // Add Lambda as a post confirmation trigger to the user pool
    // Get the underlying CfnUserPool
    const cfnUserPool = this.userPool.node.defaultChild as cognito.CfnUserPool;
    
    // Add the Lambda trigger
    cfnUserPool.lambdaConfig = {
      postConfirmation: postConfirmationLambda.functionArn
    };
    
    // Add permissions for Cognito to invoke the Lambda
    postConfirmationLambda.addPermission('AllowCognitoInvoke', {
      principal: new iam.ServicePrincipal('cognito-idp.amazonaws.com'),
      sourceArn: this.userPool.userPoolArn
    });
  }
}
