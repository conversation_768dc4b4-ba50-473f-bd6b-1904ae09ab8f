import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { ITable } from 'aws-cdk-lib/aws-dynamodb';
import { Rule, Schedule } from 'aws-cdk-lib/aws-events';
import { LambdaFunction } from 'aws-cdk-lib/aws-events-targets';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { Secret } from 'aws-cdk-lib/aws-secretsmanager';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import { RuleTargetInput } from 'aws-cdk-lib/aws-events';
import { ConnectedAndSharedVpc } from '@cov-cdk/service-catalog/cov-network';
import * as servicecatalog from 'aws-cdk-lib/aws-servicecatalog';
import { Duration } from 'aws-cdk-lib';
import * as sqs from 'aws-cdk-lib/aws-sqs';
import * as lambdaEventSources from 'aws-cdk-lib/aws-lambda-event-sources';

interface P6IntegrationStackProps extends cdk.StackProps {
    /**
     * An existing DynamoDB table reference.
     */
    table: ITable;

    /**
     * The endpoint (URL) to the Primavera P6 SOAP API.
     */
    primaveraEndpoint: string;
    primaveraVpcEndpointServiceName: string;
    apiUserName: string;
    
    /**
     * SQS Queue for activity updates from GraphQL API
     */
    activityUpdateQueue: sqs.IQueue;
}

export class P6IntegrationStack extends cdk.Stack {
    constructor(scope: Construct, id: string, props: P6IntegrationStackProps) {
        super(scope, id, props);

        const { table, primaveraEndpoint, apiUserName, activityUpdateQueue } = props;


        // Create a new secret in Secrets Manager for Primavera P6 credentials.
        const primaveraSecret = new Secret(this, 'PrimaveraCredentials', {
            secretName: 'PrimaveraCredentials',
            generateSecretString: {
                secretStringTemplate: JSON.stringify({ username: apiUserName }),
                generateStringKey: 'password',
                excludePunctuation: false, // Set to true if some systems have issues with special characters
            },
            description: 'Credentials for Primavera P6 SOAP API',
        });

        // Create an SQS Queue for Project Sync Requests
        const projectQueue = new sqs.Queue(this, 'ProjectSyncQueue', {
            visibilityTimeout: cdk.Duration.seconds(180), // Match Lambda timeout
        });

        // Create a VPC connected Lambda function that fetches data from the Primavera SOAP API.
        const minimalVpc = new ConnectedAndSharedVpc(this, "TAEX-Vpc");

        // TODO: Manage dependency between Provisioned Product and VPC
        new servicecatalog.CfnCloudFormationProvisionedProduct(this, 'MyProvisionedProduct', {
            productId: 'prod-gynstbliifbas',
            provisioningArtifactId: 'pa-sfv7xkehqz2r6',
            provisionedProductName: 'CovestroSharedService-V2',
            provisioningParameters: [
                {
                    key: 'ServiceName',
                    value: 'p6ta-nlb-taex.749506386854.aws.glpoly.net',
                },
                {
                    key: 'UniqueVpcName',
                    value: minimalVpc.vpcName
                },
            ],
        });

        // Define security group for Lambda
        const lambdaSecurityGroup = new ec2.SecurityGroup(this, 'LambdaSecurityGroup', {
            vpc: minimalVpc,
            description: 'Security group for Lambda accessing intranet',
            allowAllOutbound: true, // Allow outbound access
        });


        const dependencyLayer = new lambda.LayerVersion(this, 'MyLayer', {
            code: lambda.Code.fromAsset('lambda/p6-integration-layer'),
            compatibleRuntimes: [lambda.Runtime.NODEJS_18_X, lambda.Runtime.NODEJS_20_X],
            description: 'Lambda Layer with axios dependency',
        });


        const fetchProjectsLambda = new lambda.Function(this, 'FetchProjectsFromP6', {
            code: lambda.Code.fromAsset('lambda/p6-integration', {
                    exclude: ['node_modules', '**/__tests__/*']
                  }),
            handler: 'index.handler',
            runtime: lambda.Runtime.NODEJS_18_X,
            timeout: Duration.seconds(180),
            environment: {
                P6_WS_URL: `${primaveraEndpoint}`,
                SECRET_NAME: primaveraSecret.secretName,
                PROJECTS_TABLE: table.tableName,
                SYNC_QUEUE_URL: projectQueue.queueUrl
            },
            vpc: minimalVpc, // Attach to the VPC
            vpcSubnets: { subnets: minimalVpc.intranetSubnets },
            securityGroups: [lambdaSecurityGroup], // Assign security group
            layers: [dependencyLayer],
            memorySize: 1024,
        });

        // Grant Lambda permission to read the secret and read/write data in DynamoDB
        primaveraSecret.grantRead(fetchProjectsLambda);
        table.grantReadWriteData(fetchProjectsLambda);

        projectQueue.grantSendMessages(fetchProjectsLambda);

        /**
         * 3. Set up an EventBridge rule to trigger the Lambda every x minutes.
         */
        new Rule(this, 'FetchProjectsScheduleRule', {
            schedule: Schedule.cron({
                minute: '0/30', // Every 30 minutes
                hour: '*',
            }),
            targets: [new LambdaFunction(fetchProjectsLambda, {
                event: RuleTargetInput.fromObject({
                    action: "fetchProjects"
                })
            })],
        });

        new Rule(this, 'TriggerProjectSyncScheduleRule', {
            schedule: Schedule.cron({
                minute: '0/30', // Every 30 minutes
                hour: '*',
            }),
            targets: [new LambdaFunction(fetchProjectsLambda, {
                event: RuleTargetInput.fromObject({
                    action: "triggerProjectSync"
                })
            })],
        });

        // Whenever a Sync message with fetchActivities arrives in the queue it will be consumed
        fetchProjectsLambda.addEventSource(new lambdaEventSources.SqsEventSource(projectQueue, {
            batchSize: 1,  // Only one message processed at a time
            maxConcurrency: 2,  // Only two concurrent executions
        }));

        // Add the activity update queue as an event source for the Lambda
        fetchProjectsLambda.addEventSource(new lambdaEventSources.SqsEventSource(activityUpdateQueue, {
            batchSize: 1,  // Process one message at a time
            maxConcurrency: 5,  // Allow more concurrent executions for activity updates
        }));


    }
}
