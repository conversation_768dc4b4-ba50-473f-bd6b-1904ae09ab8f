import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { GitlabOpenIdConnectCondition, GitlabSharedRunnerIntegration } from '@cov-cdk/alpha-app-patterns';

export interface CicdStackProps extends cdk.StackProps {
    readonly appName: string;
}

export class CicdStack extends cdk.Stack {

    constructor(scope: Construct, id: string, props: CicdStackProps) {
        super(scope, id);

        const { appName } = props;

        const sample1 = new GitlabSharedRunnerIntegration(this, `${appName}-CicdIntegration`, {
            runnerRoleCondition: GitlabOpenIdConnectCondition.forGroup('PlantEngineeringApps'),
        });

        // attach to sample1.runnterRole policy that allows s3 list
        sample1.runnerRole.addToPrincipalPolicy(new cdk.aws_iam.PolicyStatement({
            actions: ['s3:*'],
            resources: ['*'],
        }));

        sample1.runnerRole.addToPrincipalPolicy(new cdk.aws_iam.PolicyStatement({
            actions: ['cloudfront:CreateInvalidation'], // Allow creating invalidations
            resources: ['*'], // Replace with specific CloudFront ARN
        }));

        sample1.runnerRole.addToPrincipalPolicy(new cdk.aws_iam.PolicyStatement({
            actions: [
                "cloudformation:*",
                "s3:*",
                "cloudfront:CreateInvalidation",
                "iam:CreateRole",
                "iam:DeleteRole",
                "iam:AttachRolePolicy",
                "iam:DetachRolePolicy",
                "iam:PassRole",
                "logs:CreateLogGroup",
                "logs:CreateLogStream",
                "logs:PutLogEvents",
                "ssm:GetParameter"
            ],
            resources: ["*"] // Scope as much as possible to specific ARNs
        }));


        sample1.runnerRole.addToPrincipalPolicy(new cdk.aws_iam.PolicyStatement({
            actions: [
                "codeartifact:GetAuthorizationToken",
                "codeartifact:ReadFromRepository",
                "codeartifact:GetRepositoryEndpoint",
                "sts:GetServiceBearerToken"
            ],
            resources: ["*"]
        }));

        // Output runner role ARN
        new cdk.CfnOutput(this, 'RunnerRoleArn', { value: sample1.runnerRole.roleArn });
    }
}
