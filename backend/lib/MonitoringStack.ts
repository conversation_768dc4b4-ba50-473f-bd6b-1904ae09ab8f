import { IntranetStaticWebsite } from '@cov-cdk/alpha-app-patterns';
import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as cognito from 'aws-cdk-lib/aws-cognito';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as rum from 'aws-cdk-lib/aws-rum';

interface MonitoringStackProps extends cdk.StackProps {
    domainName: string;
}

export class MonitoringStack extends cdk.Stack {
    public readonly distribution: cdk.aws_cloudfront.IDistribution;

    constructor(scope: Construct, id: string, props: MonitoringStackProps) {
        super(scope, id, props);

        const { domainName } = props;

        // Cognito Identity Pool for unauthenticated user access
        const identityPool = new cognito.CfnIdentityPool(this, 'CognitoIdentityPool', {
            allowUnauthenticatedIdentities: true,
        });

        // IAM Role for unauthenticated users to allow RUM access
        const unauthenticatedRole = new iam.Role(this, 'RUMUnauthenticatedRole', {
            assumedBy: new iam.FederatedPrincipal(
                'cognito-identity.amazonaws.com',
                {
                    "StringEquals": {
                        "cognito-identity.amazonaws.com:aud": identityPool.ref,
                    },
                    "ForAnyValue:StringLike": {
                        "cognito-identity.amazonaws.com:amr": "unauthenticated",
                    },
                },
                "sts:AssumeRoleWithWebIdentity"
            ),
        });

        // Permissions for the unauthenticated role to send RUM data
        unauthenticatedRole.addToPolicy(new iam.PolicyStatement({
            effect: iam.Effect.ALLOW,
            actions: ["rum:PutRumEvents"],
            resources: [
                `arn:aws:rum:${cdk.Aws.REGION}:${cdk.Aws.ACCOUNT_ID}:appmonitor/*`,
            ],
        }));

        // Attach the role to the Cognito Identity Pool
        new cognito.CfnIdentityPoolRoleAttachment(this, 'IdentityPoolRoleAttachment', {
            identityPoolId: identityPool.ref,
            roles: { unauthenticated: unauthenticatedRole.roleArn },
        });

        // Configure CloudWatch RUM app monitor
        new rum.CfnAppMonitor(this, 'AngularAppRUMMonitor', {
            name: 'AngularAppRUMMonitor',
            domain: domainName,
            appMonitorConfiguration: {
                allowCookies: true,
                enableXRay: true, // Enable AWS X-Ray for tracing
                guestRoleArn: unauthenticatedRole.roleArn,
                identityPoolId: identityPool.ref,
                telemetries: ['errors', 'performance', 'http'], // RUM metrics to collect
                sessionSampleRate: 1.0, // Capture 100% of sessions; adjust as needed
            },
            customEvents: {
                status: 'ENABLED',
            },
            cwLogEnabled: true, // Enable CloudWatch Logs for RUM
        });
    }
}
