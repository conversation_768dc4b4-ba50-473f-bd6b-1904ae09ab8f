/**
 * Post Confirmation Lambda Function
 * 
 * This Lambda function is triggered when a user confirms their account in Cognito.
 * It sends a message to an SQS queue with the user's details.
 */

const { SQSClient, SendMessageCommand } = require('@aws-sdk/client-sqs');

const sqs = new SQSClient({ region: process.env.AWS_REGION });

/**
 * <PERSON>ler function for the post confirmation trigger
 * 
 * @param {Object} event - The event from Cognito
 * @param {Object} context - The Lambda context
 * @returns {Object} - The event object to be returned to Cognito
 */
exports.handler = async (event, context) => {
  console.log('Post Confirmation event:', JSON.stringify(event, null, 2));
  
  try {
    // Only process if this is a sign-up event
    if (event.triggerSource === 'PostConfirmation_ConfirmSignUp') {
      const { userName, request } = event;
      const { userAttributes } = request;
      
      // Extract user details from the event
      const userData = {
        username: userName,
        email: userAttributes.email,
        sub: userAttributes.sub,
        emailVerified: userAttributes.email_verified === 'true',
        createdAt: new Date().toISOString()
      };
      
      console.log('User data to be sent to SQS:', JSON.stringify(userData, null, 2));
      
      // Send message to SQS queue
      const params = {
        QueueUrl: process.env.QUEUE_URL,
        MessageBody: JSON.stringify(userData),
        MessageAttributes: {
          'event_type': {
            DataType: 'String',
            StringValue: 'USER_VERIFIED'
          }
        }
      };
      
      const command = new SendMessageCommand(params);
      const response = await sqs.send(command);
      
      console.log('Message sent to SQS:', response);
    }
    
    // Return the event object to Cognito
    return event;
  } catch (error) {
    console.error('Error processing post confirmation event:', error);
    // Still return the event to Cognito to allow the user to be confirmed
    // even if there was an error sending to SQS
    return event;
  }
};
