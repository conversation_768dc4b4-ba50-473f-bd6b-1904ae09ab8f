// local-run-delete-relationships.js
const { fromIni } = require('@aws-sdk/credential-providers');

// Configuration - Replace these values with your own
const AWS_PROFILE = 'taggo'; // Your AWS CLI profile name
const AWS_REGION = 'eu-central-1';
const PROJECTS_TABLE = 'TAExGraphQlApiStack-TAExTable948F8D9B-PNHWWR1O612L'; // Your DynamoDB table name

// Configure AWS SDK to use the specified profile
process.env.AWS_SDK_LOAD_CONFIG = 1;
process.env.AWS_PROFILE = AWS_PROFILE;

// Set environment variables
process.env.PROJECTS_TABLE = PROJECTS_TABLE;

// Import AWS SDK
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, QueryCommand, BatchWriteCommand } = require('@aws-sdk/lib-dynamodb');

// Configure credentials
const credentials = fromIni({ profile: AWS_PROFILE });
const client = new DynamoDBClient({
  credentials,
  region: AWS_REGION
});
const docClient = DynamoDBDocumentClient.from(client);

/**
 * Deletes all items with type: RELATIONSHIP from the DynamoDB table
 */
async function deleteAllRelationships() {
  try {
    console.log('Starting deletion of all RELATIONSHIP items...');
    console.log(`Using AWS Profile: ${AWS_PROFILE}`);
    console.log(`Using DynamoDB Table: ${PROJECTS_TABLE}`);
    
    let lastEvaluatedKey = null;
    let totalDeleted = 0;
    let batchCount = 0;
    
    const startTime = new Date();
    console.log(`[${startTime.toISOString()}] Starting deletion operation`);
    
    do {
      // Query to get only RELATIONSHIP items using the type-index
      // Using ExpressionAttributeNames to handle the reserved keyword 'type'
      const queryParams = {
        TableName: PROJECTS_TABLE,
        IndexName: 'type-index',
        KeyConditionExpression: "#itemType = :typeValue",
        ExpressionAttributeNames: {
          "#itemType": "type"  // This creates an alias for the reserved keyword
        },
        ExpressionAttributeValues: {
          ":typeValue": "RELATIONSHIP"
        },
        // ExclusiveStartKey: lastEvaluatedKey
      };
      
      const queryResult = await docClient.send(new QueryCommand(queryParams));
      
      // Add more robust null checking
      if (!queryResult || !queryResult.Items || queryResult.Items.length === 0) {
        console.log('No more RELATIONSHIP items found.');
        break;
      }
      
      console.log(`Found ${queryResult.Items.length} RELATIONSHIP items to delete`);
      
      // Convert items to delete requests with additional null checks
      const deleteRequests = queryResult.Items.map((item) => {
        if (!item || !item.PK || !item.SK) {
          console.log('Warning: Found item with missing PK or SK, skipping:', item);
          return null;
        }
        return {
          DeleteRequest: {
            Key: {
              PK: item.PK,
              SK: item.SK
            }
          }
        };
      }).filter(request => request !== null); // Filter out any null requests
      
      // Only proceed if we have items to delete
      if (deleteRequests.length > 0) {
        // Process in chunks of 25
        for (let i = 0; i < deleteRequests.length; i += 25) {
          batchCount++;
          const batch = deleteRequests.slice(i, i + 25);
          const batchSize = batch.length;
          
          console.log(`Processing batch ${batchCount} with ${batchSize} items`);
          
          await docClient.send(new BatchWriteCommand({
            RequestItems: {
              [PROJECTS_TABLE]: batch
            }
          }));
          
          totalDeleted += batchSize;
          console.log(`Completed batch ${batchCount}, total deleted: ${totalDeleted}`);
        }
      } else {
        console.log('No valid items to delete in this batch');
      }
        
      
      lastEvaluatedKey = queryResult.LastEvaluatedKey;
    } while (lastEvaluatedKey);
    
    const endTime = new Date();
    const executionTime = (endTime - startTime) / 1000;
    
    console.log(`[${endTime.toISOString()}] Deletion operation completed in ${executionTime} seconds`);
    console.log(`Total RELATIONSHIP items deleted: ${totalDeleted}`);
    
    return {
      statusCode: 200,
      body: {
        message: "All RELATIONSHIP items deleted successfully.",
        totalDeleted,
        executionTime: `${executionTime} seconds`
      }
    };
  } catch (error) {
    console.error('Error deleting RELATIONSHIP items:', error);
    return {
      statusCode: 500,
      body: {
        error: "Failed to delete RELATIONSHIP items.",
        message: error.message
      }
    };
  }
}

// Execute the function
deleteAllRelationships()
  .then(result => {
    console.log('Execution completed:');
    console.log(JSON.stringify(result, null, 2));
  })
  .catch(error => {
    console.error('Execution failed:', error);
    process.exit(1);
  });
