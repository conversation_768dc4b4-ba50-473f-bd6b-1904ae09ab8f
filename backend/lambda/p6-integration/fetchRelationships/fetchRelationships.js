const axios = require('axios');
const xml2js = require('xml2js');
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, BatchWriteCommand, PutCommand } = require('@aws-sdk/lib-dynamodb');
const { getCredentials, generateAuthHeaders } = require('../utils');

// Initialize AWS SDK v3 DynamoDB clients
const dbClient = new DynamoDBClient({ region: 'eu-central-1' });
const docClient = DynamoDBDocumentClient.from(dbClient);

const ACTIVITIES_TABLE = process.env.PROJECTS_TABLE;
const P6_WS_URL = process.env.P6_WS_URL;

/**
 * Builds the SOAP request body to read relationships.
 * @returns {string} - SOAP request XML body.
 */
const buildSoapRequest = (username, password, projectId) => {
    const { createdTime, expiresTime, nonce } = generateAuthHeaders(password);

    return `
        <soapenv:Envelope 
            xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" 
            xmlns:v1="http://xmlns.oracle.com/Primavera/P6/WS/RelationshipService/V1"
            xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
            xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">
           <soapenv:Header>
              <wsse:Security soapenv:mustUnderstand="1">
                 <wsu:Timestamp wsu:Id="TS-1">
                    <wsu:Created>${createdTime}</wsu:Created>
                    <wsu:Expires>${expiresTime}</wsu:Expires>
                 </wsu:Timestamp>
                 <wsse:UsernameToken wsu:Id="UsernameToken-2">
                    <wsse:Username>${username}</wsse:Username>
                    <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">${password}</wsse:Password>
                    <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">${nonce}</wsse:Nonce>
                    <wsu:Created>${createdTime}</wsu:Created>
                 </wsse:UsernameToken>
              </wsse:Security>
           </soapenv:Header>
           <soapenv:Body>
              <v1:ReadRelationships>
                 <!-- Request all relevant fields -->
                 <v1:Field>Comments</v1:Field>
                 <v1:Field>ObjectId</v1:Field>
                <v1:Field>SuccessorProjectObjectId</v1:Field>
                 <v1:Field>PredecessorProjectObjectId</v1:Field>
                 <v1:Field>PredecessorActivityObjectId</v1:Field>
                 <v1:Field>SuccessorActivityObjectId</v1:Field>
                 <v1:Field>PredecessorActivityId</v1:Field>
                 <v1:Field>SuccessorActivityId</v1:Field>
                 <v1:Field>Lag</v1:Field>

                 <!-- Filter by the ProjectObjectId -->
                 <v1:Filter>PredecessorProjectObjectId = ${projectId}</v1:Filter>
              </v1:ReadRelationships>
           </soapenv:Body>
        </soapenv:Envelope>
    `;
};

/**
 * Fetches relationships from Primavera P6 Web Service.
 * @returns {Promise<Object>} - Parsed relationships data.
 */
const fetchRelationships = async (projectId) => {
    const { username, password } = await getCredentials();

    const soapRequest = buildSoapRequest(username, password, projectId);

    // Construct endpoint
    const P6_ENDPOINT = P6_WS_URL + '/p6ws/services/RelationshipService?wsdl';

    try {
        const response = await axios.post(P6_ENDPOINT, soapRequest, {
            headers: {
                'Content-Type': 'text/xml'
            }
        });

        // Parse the XML response
        const parser = new xml2js.Parser({ explicitArray: false });
        const parsedResult = await parser.parseStringPromise(response.data);

        // Extract relationships data
        var relationships = parsedResult['SOAP-ENV:Envelope']['SOAP-ENV:Body']['ReadRelationshipsResponse']['Relationship'];

        // Convert webservice output to json
        relationships = replaceNilValues(relationships);

        return relationships ? relationships : { message: 'No relationships found' };
    } catch (error) {
        console.error('[ERROR] Error fetching relationships:', error.message);
        throw new Error('Failed to fetch relationships');
    }
};

/**
 * Write sync job log to DynamoDB
 * @param {string} projectId - The project ID
 * @param {string} status - The sync status (SUCCEEDED or FAILED)
 * @param {Object} metadata - Additional metadata about the sync job
 * @returns {Promise<Object>} - The sync record that was written
 */
async function writeSyncJobLog(projectId, status, metadata = {}) {
    if (!ACTIVITIES_TABLE) {
        console.warn('ACTIVITIES_TABLE environment variable not set, skipping sync job log write');
        return null;
    }

    const timestamp = new Date().toISOString();
    const syncRecord = {
        PK: "SYNCJOB",
        SK: `RELATIONSHIPS#${projectId}#${timestamp}`,
        type: "syncjob",
        projectId: projectId,
        status: status, // "SUCCEEDED" or "FAILED"
        timestamp: timestamp,
        ...metadata
    };
    
    try {
        // Initialize DynamoDB client
        const client = new DynamoDBClient();
        const docClient = DynamoDBDocumentClient.from(client);
        
        // Write to DynamoDB
        await docClient.send(new PutCommand({
            TableName: ACTIVITIES_TABLE,
            Item: syncRecord
        }));
        
        console.log(`Sync job log written for project ${projectId}: ${status}`);
        return syncRecord;
    } catch (error) {
        console.error(`Error writing sync job log for project ${projectId}:`, error);
        // We don't throw here to avoid affecting the main function flow
        return null;
    }
}

/**
 * Recursively replaces {"$": {"xsi:nil":"true"}} with null in a JSON object.
 * @param {any} obj - JSON object to process.
 * @returns {any} - Processed JSON with null values.
 */
function replaceNilValues(obj) {
    if (Array.isArray(obj)) {
        return obj.map(replaceNilValues);
    } else if (typeof obj === 'object' && obj !== null) {
        return Object.fromEntries(
            Object.entries(obj).map(([key, value]) => [
                key,
                value && value.$ && value.$["xsi:nil"] === "true" ? null : replaceNilValues(value)
            ])
        );
    }
    return obj;
}

/**
 * Prepares a relationship item for DynamoDB
 * @param {Object} relationship - Relationship from SOAP response
 * @returns {Object} - Item ready for DynamoDB
 */
const prepareRelationshipItem = (relationship) => {
    const now = new Date().toISOString();
    
    // Extract the required fields from the relationship
    const predecessorActivityId = relationship.PredecessorActivityId;
    const successorActivityId = relationship.SuccessorActivityId;
    const predecessorActivityObjectId = relationship.PredecessorActivityObjectId;
    const successorActivityObjectId = relationship.SuccessorActivityObjectId;
    const predecessorProjectObjectId = relationship.PredecessorProjectObjectId;
    const successorProjectObjectId = relationship.SuccessorProjectObjectId;
    const lag = relationship.Lag;
    const comments = relationship.Comments;
    const relationshipId = relationship.ObjectId;
    
    // Create the item to be written to DynamoDB
    return {
        PK: `RELATIONSHIP#${relationshipId}`,
        SK: `RELATIONSHIP`,
        id: relationshipId,
        predecessorActivityId: predecessorActivityId,
        successorActivityId: successorActivityId,
        predecessorActivityObjectId: predecessorActivityObjectId,
        successorActivityObjectId: successorActivityObjectId,
        predecessorProjectObjectId: predecessorProjectObjectId,
        successorProjectObjectId: successorProjectObjectId,
        lag: lag,
        comments: comments,
        type: 'RELATIONSHIP',
        updatedAt: now,
        createdAt: now
    };
};

/**
 * Writes relationships to DynamoDB in batches
 * @param {Array} relationships - Array of relationships to write
 * @returns {Promise<void>}
 */
const batchWriteRelationshipsToDynamoDB = async (relationships) => {
    try {
        // DynamoDB BatchWrite can process up to 25 items at once
        const BATCH_SIZE = 25;
        const batches = [];
        
        // Create batches of up to 25 items
        for (let i = 0; i < relationships.length; i += BATCH_SIZE) {
            batches.push(relationships.slice(i, i + BATCH_SIZE));
        }
        
        console.log(`Writing ${relationships.length} relationships in ${batches.length} batches`);
        
        // Process each batch
        for (let i = 0; i < batches.length; i++) {
            const batch = batches[i];
            const batchItems = batch.map(relationship => ({
                PutRequest: {
                    Item: prepareRelationshipItem(relationship)
                }
            }));
            
            const params = {
                RequestItems: {
                    [ACTIVITIES_TABLE]: batchItems
                }
            };
            
            await docClient.send(new BatchWriteCommand(params));
            console.log(`Completed batch ${i + 1}/${batches.length} (${batch.length} items)`);
        }
    } catch (error) {
        console.error('Failed to batch write relationships:', error);
        throw error;
    }
};

/**
 * Processes relationships and writes them to DynamoDB
 * @param {Array|Object} relationships - Relationships from SOAP response
 */
const processRelationships = async (relationships) => {
    try {
        if (!relationships || (Array.isArray(relationships) && relationships.length === 0)) {
            console.log('No relationships to process');
            return;
        }
        
        // Convert to array if it's a single object
        const relationshipsArray = Array.isArray(relationships) ? relationships : [relationships];
        
        console.log(`Processing ${relationshipsArray.length} relationships`);
        
        // Use batch write for better performance
        await batchWriteRelationshipsToDynamoDB(relationshipsArray);
        
        console.log(`Completed processing ${relationshipsArray.length} relationships`);
    } catch (error) {
        console.error('Error processing relationships:', error);
        throw new Error('Failed to process relationships');
    }
};

/**
 * Main function to fetch and process relationships
 * @param {string} projectId - The ID of the project to fetch relationships for
 */
const fetchAndWriteRelationships = async (projectId) => {
    const startTime = new Date();
    console.log(`[${startTime.toISOString()}] Starting relationship fetch and write operation for project ${projectId}`);
    
    try {
        
        // Fetch operation
        const fetchStartTime = new Date();
        console.log(`[${fetchStartTime.toISOString()}] Starting fetch operation for project ${projectId}`);
        const relationships = await fetchRelationships(projectId);
        const fetchEndTime = new Date();
        console.log(`[${fetchEndTime.toISOString()}] Fetch operation completed in ${(fetchEndTime - fetchStartTime)/1000} seconds`);
        
        if (relationships && relationships.message === 'No relationships found') {
            console.log(`No relationships found for project ${projectId}`);
            return {
                statusCode: 200,
                body: JSON.stringify({ message: 'No relationships found to process' })
            };
        }
        
        // Write operation
        const writeStartTime = new Date();
        console.log(`[${writeStartTime.toISOString()}] Starting write operation`);
        await processRelationships(relationships);
        const writeEndTime = new Date();
        console.log(`[${writeEndTime.toISOString()}] Write operation completed in ${(writeEndTime - writeStartTime)/1000} seconds`);
        
        const endTime = new Date();
        const executionTime = (endTime - startTime)/1000;
        console.log(`[${endTime.toISOString()}] Operation completed in ${executionTime} seconds`);
        
        // Get count of relationships processed
        const relationshipsCount = Array.isArray(relationships) ? relationships.length : (relationships ? 1 : 0);
        
        // Write sync job log for successful operation
        await writeSyncJobLog(projectId, "SUCCEEDED", {
            executionTime: executionTime,
            fetchTime: (fetchEndTime - fetchStartTime)/1000,
            writeTime: (writeEndTime - writeStartTime)/1000,
            relationshipsProcessed: relationshipsCount
        });
        
        return {
            statusCode: 200,
            body: JSON.stringify({ 
                message: 'Relationships processed successfully',
                executionTime: `${executionTime} seconds`,
                relationshipsProcessed: relationshipsCount
            })
        };
    } catch (error) {
        const endTime = new Date();
        const executionTime = (endTime - startTime)/1000;
        console.error(`[${endTime.toISOString()}] Error in fetchAndWriteRelationships:`, error);
        
        // Write sync job log for failed operation
        await writeSyncJobLog(projectId, "FAILED", {
            executionTime: executionTime,
            error: error.message
        });
        
        return {
            statusCode: 500,
            body: JSON.stringify({ 
                error: error.message,
                executionTime: `${executionTime} seconds`
            })
        };
    }
};

module.exports = { fetchAndWriteRelationships };
