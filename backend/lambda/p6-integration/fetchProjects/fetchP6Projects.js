const axios = require('axios');
const xml2js = require('xml2js');
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, BatchWriteCommand, UpdateCommand, PutCommand } = require('@aws-sdk/lib-dynamodb');
const { getCredentials, generateAuthHeaders } = require('../utils');

// Initialize AWS SDK v3 DynamoDB clients
const dbClient = new DynamoDBClient({ region: 'eu-central-1' });
const docClient = DynamoDBDocumentClient.from(dbClient);

const PROJECTS_TABLE = process.env.PROJECTS_TABLE;
const P6_WS_URL = process.env.P6_WS_URL;
const SOAP_ACTION = 'ReadProjects';

// Helper function to convert date fields to ISO string
const toISOString = (date) => (date ? new Date(date).toISOString() : null);

/**
 * Builds the SOAP request body to read projects.
  * @returns {string} - SOAP request XML body.
 */
const buildSoapRequest = (username, password) => {
    const { createdTime, expiresTime, nonce } = generateAuthHeaders(password);

    return `
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
                          xmlns:tns="http://xmlns.oracle.com/Primavera/P6/WS/Project/V2"
                        xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">
            <soapenv:Header>
                <wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
                     <wsu:Timestamp wsu:Id="TS-1">
                        <wsu:Created>${createdTime}</wsu:Created>
                        <wsu:Expires>${expiresTime}</wsu:Expires>
                    </wsu:Timestamp>
                    <wsse:UsernameToken>
                        <wsse:Username>${username}</wsse:Username>
                        <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">${password}</wsse:Password>
                        <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">${nonce}</wsse:Nonce>
                        <wsu:Created xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">${createdTime}</wsu:Created>
                        <wsu:Expires xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">${expiresTime}</wsu:Expires>
                    </wsse:UsernameToken>
                </wsse:Security>
            </soapenv:Header>
            <soapenv:Body>
                <tns:ReadProjects>
                    <tns:Field>ObjectId</tns:Field>
                    <tns:Field>AllowStatusReview</tns:Field>
                    <tns:Field>OBSName</tns:Field>
                    <tns:Field>OBSObjectId</tns:Field>
                    <tns:Field>ParentEPSObjectId</tns:Field>
                    <tns:Field>ParentEPSId</tns:Field>
                    <tns:Field>ParentEPSName</tns:Field>
                    <tns:Field>WBSObjectId</tns:Field>
                    <tns:Field>Id</tns:Field>
                    <tns:Field>Name</tns:Field>
                    <tns:Field>Description</tns:Field>
                    <tns:Field>Status</tns:Field>
                    <tns:Field>StartDate</tns:Field>
                    <tns:Field>FinishDate</tns:Field>
                    <tns:Field>LocationName</tns:Field>
                    <tns:Field>SummaryActivityCount</tns:Field>
                    <tns:Field>ForecastStartDate</tns:Field>
                    <tns:Field>ForecastFinishDate</tns:Field>
                </tns:ReadProjects>
            </soapenv:Body>
        </soapenv:Envelope>
    `;
};

/**
 * Fetches projects from Primavera P6 Web Service.
 * @returns {Promise<Object>} - Parsed project data.
 */
const fetchProjects = async () => {
    const { username, password } = await getCredentials();

    const soapRequest = buildSoapRequest(username, password);

    // Construct endpoint
    const P6_ENDPOINT = process.env.P6_WS_URL + '/p6ws/services/ProjectService?wsdl';

    try {
        const response = await axios.post(P6_ENDPOINT, soapRequest, {
            headers: {
                'Content-Type': 'text/xml',
                'SOAPAction': SOAP_ACTION,
            }
        });

        // Parse the XML response
        const parser = new xml2js.Parser({ explicitArray: false });
        const parsedResult = await parser.parseStringPromise(response.data);

        // Extract project data
        var projects = parsedResult['SOAP-ENV:Envelope']['SOAP-ENV:Body']['ReadProjectsResponse']['Project'];

        // Convert webservice output to json
        projects = replaceNilValues(projects);

        return projects ? projects : { message: 'No projects found' };
    } catch (error) {
        console.error('Error fetching projects:', error.message);
        throw new Error('Failed to fetch projects');
    }
};

/**
 * Recursively replaces {"$": {"xsi:nil":"true"}} with null in a JSON object.
 * @param {any} obj - JSON object to process.
 * @returns {any} - Processed JSON with null values.
 */
function replaceNilValues(obj) {
    if (Array.isArray(obj)) {
        return obj.map(replaceNilValues);
    } else if (typeof obj === 'object' && obj !== null) {
        return Object.fromEntries(
            Object.entries(obj).map(([key, value]) => [
                key,
                value && value.$ && value.$["xsi:nil"] === "true" ? null : replaceNilValues(value)
            ])
        );
    }
    return obj;
}

/**
 * Prepares a project item for DynamoDB
 * @param {Object} project - Project from SOAP response
 * @returns {Object} - Item ready for DynamoDB
 */
const prepareProjectItem = (project) => {
    const now = new Date().toISOString();
    const statusMapping = {
        "Planned": "PLANNED",
        "Active": "ACTIVE",
        "What-If": "WHATIF",
        "Requested": "REQUESTED",
        "Template": "TEMPLATE"
    };

    return {
        PK: `PROJECT#${project.ObjectId}`,
        SK: `METADATA#${project.ObjectId}`,
        name: project.Name || null,
        description: project.Description || null,
        status: statusMapping[project.Status] || null,
        startDate: toISOString(project.StartDate),
        finishDate: toISOString(project.FinishDate),
        locationName: project.LocationName || null,
        activityCount: project.SummaryActivityCount || null,
        updatedAt: now,
        type: "PROJECT",
        id: project.ObjectId,
        projectId: project.Id,
        forecastStartDate: toISOString(project.ForecastStartDate),
        forecastFinishDate: toISOString(project.ForecastFinishDate),
        allowStatusReview: project.AllowStatusReview || null,
        obsName: project.OBSName || null,
        obsObjectId: project.OBSObjectId || null,
        parentEPSObjectId: project.ParentEPSObjectId || null,
        parentEPSId: project.ParentEPSId || null,
        parentEPSName: project.ParentEPSName || null,
        wbsObjectId: project.WBSObjectId || null
    };
};

/**
 * Write sync job log to DynamoDB
 * @param {string} status - The sync status (SUCCEEDED or FAILED)
 * @param {Object} metadata - Additional metadata about the sync job
 * @returns {Promise<Object>} - The sync record that was written
 */
async function writeSyncJobLog(status, metadata = {}) {
    if (!PROJECTS_TABLE) {
        console.warn('PROJECTS_TABLE environment variable not set, skipping sync job log write');
        return null;
    }

    const timestamp = new Date().toISOString();
    const syncRecord = {
        PK: "SYNCJOB",
        SK: `PROJECTS#${timestamp}`,
        type: "syncjob",
        status: status, // "SUCCEEDED" or "FAILED"
        timestamp: timestamp,
        ...metadata
    };
    
    try {
        // Initialize DynamoDB client
        const client = new DynamoDBClient();
        const docClient = DynamoDBDocumentClient.from(client);
        
        // Write to DynamoDB
        await docClient.send(new PutCommand({
            TableName: PROJECTS_TABLE,
            Item: syncRecord
        }));
        
        console.log(`Sync job log written: ${status}`);
        return syncRecord;
    } catch (error) {
        console.error(`Error writing sync job log:`, error);
        // We don't throw here to avoid affecting the main function flow
        return null;
    }
}

/**
 * Updates projects in DynamoDB using individual update commands to preserve existing properties
 * @param {Array} projects - Array of projects to update
 * @returns {Promise<void>}
 */
const batchUpdateProjectsInDynamoDB = async (projects) => {
    try {
        // Process in batches of 25 for better performance
        const BATCH_SIZE = 25;
        const batches = [];
        
        // Create batches of up to 25 items
        for (let i = 0; i < projects.length; i += BATCH_SIZE) {
            batches.push(projects.slice(i, i + BATCH_SIZE));
        }
        
        console.log(`Updating ${projects.length} projects in ${batches.length} batches`);
        
        // Process each batch
        for (let i = 0; i < batches.length; i++) {
            const batchStartTime = new Date();
            console.log(`[${batchStartTime.toISOString()}] Processing batch ${i + 1}/${batches.length}`);
            
            const batch = batches[i];
            
            // Create an array of update promises for each project in the batch
            const updatePromises = batch.map(project => {
                const item = prepareProjectItem(project);
                
                // Create expression attribute names and values
                const expressionAttributeNames = {};
                const expressionAttributeValues = {};
                
                // Build the update expression and populate attribute names and values
                let updateExpression = 'SET ';
                const updateExpressions = [];
                
                // Process each attribute in the item (except PK and SK which are part of the key)
                Object.entries(item).forEach(([key, value]) => {
                    if (key !== 'PK' && key !== 'SK') {
                        const attributeName = `#${key}`;
                        const attributeValue = `:${key}`;
                        
                        expressionAttributeNames[attributeName] = key;
                        expressionAttributeValues[attributeValue] = value;
                        
                        updateExpressions.push(`${attributeName} = ${attributeValue}`);
                    }
                });
                
                updateExpression += updateExpressions.join(', ');
                
                // Create the update command
                return docClient.send(new UpdateCommand({
                    TableName: PROJECTS_TABLE,
                    Key: {
                        PK: item.PK,
                        SK: item.SK
                    },
                    UpdateExpression: updateExpression,
                    ExpressionAttributeNames: expressionAttributeNames,
                    ExpressionAttributeValues: expressionAttributeValues
                }));
            });
            
            // Execute all update commands in parallel
            await Promise.all(updatePromises);
            
            const batchEndTime = new Date();
            console.log(`[${batchEndTime.toISOString()}] Completed batch ${i + 1}/${batches.length} (${batch.length} items) in ${(batchEndTime - batchStartTime)/1000} seconds`);
        }
    } catch (error) {
        console.error('Failed to update projects:', error);
        throw error;
    }
};

/**
 * Processes projects and writes them to DynamoDB
 * @param {Array|Object} projects - Projects from SOAP response
 */
const processProjects = async (projects) => {
    try {
        if (!projects || (Array.isArray(projects) && projects.length === 0)) {
            console.log('No projects to process');
            return;
        }
        
        // Convert to array if it's a single object
        const projectsArray = Array.isArray(projects) ? projects : [projects];
        
        console.log(`Processing ${projectsArray.length} projects`);
        
        // Use batch update to preserve existing properties
        await batchUpdateProjectsInDynamoDB(projectsArray);
        
        console.log(`Completed processing ${projectsArray.length} projects`);
    } catch (error) {
        console.error('Error processing projects:', error);
        throw new Error('Failed to process projects');
    }
};

/**
 * Main function to fetch and process projects
 */
const fetchAndWriteProjects = async () => {
    const startTime = new Date();
    console.log(`[${startTime.toISOString()}] Starting project fetch and write operation`);
    
    try {
        // Fetch operation
        const fetchStartTime = new Date();
        console.log(`[${fetchStartTime.toISOString()}] Starting fetch operation`);
        const projects = await fetchProjects();
        const fetchEndTime = new Date();
        console.log(`[${fetchEndTime.toISOString()}] Fetch operation completed in ${(fetchEndTime - fetchStartTime)/1000} seconds`);
        
        if (projects && projects.message === 'No projects found') {
            console.log('No projects found');
            return {
                statusCode: 200,
                body: JSON.stringify({ message: 'No projects found to process' })
            };
        }
        
        // Write operation
        const writeStartTime = new Date();
        console.log(`[${writeStartTime.toISOString()}] Starting write operation`);
        await processProjects(projects);
        const writeEndTime = new Date();
        console.log(`[${writeEndTime.toISOString()}] Write operation completed in ${(writeEndTime - writeStartTime)/1000} seconds`);
        
        const endTime = new Date();
        const executionTime = (endTime - startTime)/1000;
        console.log(`[${endTime.toISOString()}] Operation completed in ${executionTime} seconds`);
        
        // Write sync job log for successful operation
        const projectCount = Array.isArray(projects) ? projects.length : 1;
        await writeSyncJobLog("SUCCEEDED", {
            executionTime: executionTime,
            fetchTime: (fetchEndTime - fetchStartTime)/1000,
            writeTime: (writeEndTime - writeStartTime)/1000,
            projectsProcessed: projectCount
        });
        
        return {
            statusCode: 200,
            body: JSON.stringify({ 
                message: 'Projects processed successfully',
                executionTime: `${executionTime} seconds`,
                fetchTime: `${(fetchEndTime - fetchStartTime)/1000} seconds`,
                writeTime: `${(writeEndTime - writeStartTime)/1000} seconds`,
                projectsProcessed: projectCount
            })
        };
    } catch (error) {
        const endTime = new Date();
        const executionTime = (endTime - startTime)/1000;
        console.error(`[${endTime.toISOString()}] Error in fetchAndWriteProjects:`, error);
        
        // Write sync job log for failed operation
        await writeSyncJobLog("FAILED", {
            executionTime: executionTime,
            error: error.message
        });
        
        return {
            statusCode: 500,
            body: JSON.stringify({ 
                error: error.message,
                executionTime: `${executionTime} seconds`
            })
        };
    }
};

module.exports = { fetchAndWriteProjects };
