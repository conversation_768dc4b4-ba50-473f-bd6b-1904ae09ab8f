const { fetchAndWriteProjects } = require('./fetchP6Projects');
const { getCredentials } = require('../utils');
const axios = require('axios');
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, PutCommand } = require('@aws-sdk/lib-dynamodb');

const PASSWORD = process.env.P6_PASSWORD;

jest.mock('../utils', () => ({
    getCredentials: jest.fn(),
    generateAuthHeaders: jest.fn().mockReturnValue({
        createdTime: '2024-01-01T00:00:00Z',
        expiresTime: '2024-01-01T00:05:00Z',
        nonce: 'testNonce'
    })
}));

// Mock DynamoDB
jest.mock('@aws-sdk/client-dynamodb', () => ({
    DynamoDBClient: jest.fn().mockImplementation(() => ({
        send: jest.fn()
    }))
}));

jest.mock('@aws-sdk/lib-dynamodb', () => ({
    DynamoDBDocumentClient: {
        from: jest.fn().mockImplementation(() => ({
            send: jest.fn().mockResolvedValue({})
        }))
    },
    PutCommand: jest.fn(),
    UpdateCommand: jest.fn()
}));

// Set environment variables for testing
process.env.PROJECTS_TABLE = 'test-table';

describe("fetchAndWriteProjects Tests", () => {
    let mockDynamoSend;
    
    beforeEach(() => {
        jest.clearAllMocks();
        
        // Setup mock for DynamoDB send
        mockDynamoSend = jest.fn().mockResolvedValue({});
        DynamoDBDocumentClient.from.mockImplementation(() => ({
            send: mockDynamoSend
        }));
    });

    test("should fetch and process projects successfully and write sync log", async () => {
        // Mock successful project fetch
        getCredentials.mockResolvedValue({ username: 'TAEX_COM', password: 'testPassword' });
        
        // Mock axios to return a successful response with project data
        jest.spyOn(axios, 'post').mockResolvedValueOnce({
            data: `
                <SOAP-ENV:Envelope>
                    <SOAP-ENV:Body>
                        <ReadProjectsResponse>
                            <Project>
                                <ObjectId>P1</ObjectId>
                                <Id>101</Id>
                                <Name>Test Project</Name>
                                <Status>Active</Status>
                            </Project>
                        </ReadProjectsResponse>
                    </SOAP-ENV:Body>
                </SOAP-ENV:Envelope>
            `
        });

        const response = await fetchAndWriteProjects();

        // Verify response
        expect(response).toHaveProperty('statusCode', 200);
        expect(response).toHaveProperty('body');
        
        const responseBody = JSON.parse(response.body);
        expect(responseBody).toHaveProperty('message', 'Projects processed successfully');
        expect(responseBody).toHaveProperty('projectsProcessed', 1);
        
        // Verify sync job log was written
        expect(PutCommand).toHaveBeenCalledWith(expect.objectContaining({
            TableName: 'test-table',
            Item: expect.objectContaining({
                PK: 'SYNCJOB',
                SK: expect.stringMatching(/^PROJECTS#.+/),
                type: 'syncjob',
                status: 'SUCCEEDED',
                projectsProcessed: 1
            })
        }));
        
        // Verify DynamoDB send was called for the sync log
        expect(mockDynamoSend).toHaveBeenCalled();
    });

    test("should handle errors properly and write failed sync log", async () => {
        // Mock credential error
        const testError = new Error("Test Error");
        getCredentials.mockRejectedValue(testError);

        const response = await fetchAndWriteProjects();

        // Verify error response
        expect(response).toHaveProperty('statusCode', 500);
        expect(response).toHaveProperty('body');
        
        const responseBody = JSON.parse(response.body);
        expect(responseBody).toHaveProperty('error', 'Test Error');
        
        // Verify failed sync job log was written
        expect(PutCommand).toHaveBeenCalledWith(expect.objectContaining({
            TableName: 'test-table',
            Item: expect.objectContaining({
                PK: 'SYNCJOB',
                SK: expect.stringMatching(/^PROJECTS#.+/),
                type: 'syncjob',
                status: 'FAILED',
                error: 'Test Error'
            })
        }));
        
        // Verify DynamoDB send was called for the sync log
        expect(mockDynamoSend).toHaveBeenCalled();
    });
    
    test("should handle missing TABLE_NAME environment variable", async () => {
        // Save original and temporarily remove TABLE_NAME
        const originalTableName = process.env.PROJECTS_TABLE;
        delete process.env.PROJECTS_TABLE;
        
        // Mock successful project fetch
        getCredentials.mockResolvedValue({ username: 'TAEX_COM', password: 'testPassword' });
        
        // Mock axios to return a successful response with project data
        jest.spyOn(axios, 'post').mockResolvedValueOnce({
            data: `
                <SOAP-ENV:Envelope>
                    <SOAP-ENV:Body>
                        <ReadProjectsResponse>
                            <Project>
                                <ObjectId>P1</ObjectId>
                                <Id>101</Id>
                                <Name>Test Project</Name>
                            </Project>
                        </ReadProjectsResponse>
                    </SOAP-ENV:Body>
                </SOAP-ENV:Envelope>
            `
        });

        const response = await fetchAndWriteProjects();

        // Verify response
        expect(response).toHaveProperty('statusCode', 200);
        
        // Verify DynamoDB send was not called when TABLE_NAME is missing
        expect(mockDynamoSend).not.toHaveBeenCalled();
        
        // Restore TABLE_NAME for other tests
        process.env.PROJECTS_TABLE = originalTableName;
    });
});
