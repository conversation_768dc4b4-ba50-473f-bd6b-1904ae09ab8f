const axios = require('axios');
const xml2js = require('xml2js');
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, PutCommand } = require('@aws-sdk/lib-dynamodb');
const { getCredentials, generateAuthHeaders } = require('../utils');

const P6_WS_URL = process.env.P6_WS_URL;
const TABLE_NAME = process.env.PROJECTS_TABLE;

// Function to build SOAP request for updating an activity
const buildSoapRequest = (username, password, activityId, updates) => {
    const { createdTime, expiresTime, nonce } = generateAuthHeaders(password);
    
    return `
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
                           xmlns:v1="http://xmlns.oracle.com/Primavera/P6/WS/Project/V2"
                            xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
                            xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">
            <soapenv:Header>
                <wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
                     <wsu:Timestamp wsu:Id="TS-1">
                        <wsu:Created>${createdTime}</wsu:Created>
                        <wsu:Expires>${expiresTime}</wsu:Expires>
                    </wsu:Timestamp>
                    <wsse:UsernameToken>
                        <wsse:Username>${username}</wsse:Username>
                        <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">${password}</wsse:Password>
                        <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">${nonce}</wsse:Nonce>
                        <wsu:Created xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">${createdTime}</wsu:Created>
                        <wsu:Expires xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">${expiresTime}</wsu:Expires>
                    </wsse:UsernameToken>
                </wsse:Security>
            </soapenv:Header>
            <soapenv:Body>
                <v1:UpdateActivities>
                 <v1:Activity>
                    <v1:ObjectId>${activityId}</v1:ObjectId>
                    ${Object.entries(updates).map(([key, value]) => `<v1:${key}>${value}</v1:${key}>`).join('\n')}
                    </v1:Activity>
                </v1:UpdateActivities>
            </soapenv:Body>
        </soapenv:Envelope>
    `;
};

/**
 * Write sync status to DynamoDB
 * @param {string} activityId - The activity ID
 * @param {string} status - The sync status (SUCCEEDED or FAILED)
 * @returns {Promise<Object>} - The sync record that was written
 */
async function writeSyncStatus(activityId, status) {
    if (!TABLE_NAME) {
        console.warn('TABLE_NAME environment variable not set, skipping sync status write');
        return null;
    }

    const timestamp = new Date().toISOString();
    const syncRecord = {
        PK: `ACTIVITY#${activityId}`,
        SK: "SYNC",
        type: "ActivitySync",
        targetSystem: "Primavera-P6",
        lastSyncedAt: timestamp,
        syncStatus: status // "SUCCEEDED" or "FAILED"
    };
    
    try {
        // Initialize DynamoDB client
        const client = new DynamoDBClient();
        const docClient = DynamoDBDocumentClient.from(client);
        
        // Write to DynamoDB
        await docClient.send(new PutCommand({
            TableName: TABLE_NAME,
            Item: syncRecord
        }));
        
        console.log(`Sync status written for activity ${activityId}: ${status}`);
        return syncRecord;
    } catch (error) {
        console.error(`Error writing sync status for activity ${activityId}:`, error);
        // We don't throw here to avoid affecting the main function flow
        return null;
    }
}

// Function to update an activity in P6
const updateP6Activity = async (activityId, updates) => {
    if (!activityId || !updates || Object.keys(updates).length === 0) {
        throw new Error('Invalid input: activityId and updates are required');
    }
    
    const { username, password } = await getCredentials();
    const soapRequest = buildSoapRequest(username, password, activityId, updates);

    // Construct endpoint
    const P6_ENDPOINT = process.env.P6_WS_URL + '/p6ws/services/ActivityService?wsdl';
    
    try {
        const response = await axios.post(P6_ENDPOINT, soapRequest, {
            headers: {
                'Content-Type': 'text/xml'
            }
        });
        
        const parser = new xml2js.Parser({ explicitArray: false });
        const parsedResult = await parser.parseStringPromise(response.data);
        
        // Write success status to DynamoDB
        await writeSyncStatus(activityId, "SUCCEEDED");
        
        return parsedResult;
    } catch (error) {
        console.error(`Error updating activity ${activityId}:`, error.message);
        
        // Write failure status to DynamoDB
        await writeSyncStatus(activityId, "FAILED");
        
        throw new Error('Failed to update activity');
    }
};

module.exports = { updateP6Activity };
