const { updateP6Activity } = require('./updateP6Activity');
const { getCredentials, generateAuthHeaders } = require('../utils');
const axios = require('axios');
const xml2js = require('xml2js');
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, PutCommand } = require('@aws-sdk/lib-dynamodb');

jest.mock('../utils', () => ({
    getCredentials: jest.fn(),
    generateAuthHeaders: jest.fn()
}));

jest.mock('axios');

// Mock DynamoDB
jest.mock('@aws-sdk/client-dynamodb', () => ({
    DynamoDBClient: jest.fn().mockImplementation(() => ({
        send: jest.fn()
    }))
}));

jest.mock('@aws-sdk/lib-dynamodb', () => ({
    DynamoDBDocumentClient: {
        from: jest.fn().mockImplementation(() => ({
            send: jest.fn().mockResolvedValue({})
        }))
    },
    PutCommand: jest.fn()
}));

// Set environment variables for testing
process.env.TABLE_NAME = 'test-table';

describe("updateP6Activity Tests", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    test("should successfully update an activity and write success sync status", async () => {
        getCredentials.mockResolvedValue({ username: 'TAEX_COM', password: 'testPass' });
        
        generateAuthHeaders.mockReturnValue({
            createdTime: '2024-01-01T00:00:00Z',
            expiresTime: '2024-01-01T00:05:00Z',
            nonce: 'testNonce'
        });
        
        axios.post.mockResolvedValue({
            data: `
                <SOAP-ENV:Envelope>
                    <SOAP-ENV:Body>
                        <UpdateActivitiesResponse>
                            <Result>Success</Result>
                        </UpdateActivitiesResponse>
                    </SOAP-ENV:Body>
                </SOAP-ENV:Envelope>
            `
        });
        
        const mockDynamoSend = jest.fn().mockResolvedValue({});
        DynamoDBDocumentClient.from.mockImplementation(() => ({
            send: mockDynamoSend
        }));
        
        const activityId = 'A123';
        const updates = { Name: 'Updated Activity', Status: 'In Progress' };
        
        const response = await updateP6Activity(activityId, updates);
        
        expect(response).toBeDefined();
        expect(axios.post).toHaveBeenCalled();
        
        // Verify DynamoDB write was called with correct parameters
        expect(PutCommand).toHaveBeenCalledWith(expect.objectContaining({
            TableName: 'test-table',
            Item: expect.objectContaining({
                PK: `ACTIVITY#${activityId}`,
                SK: 'SYNC',
                type: 'ActivitySync',
                targetSystem: 'Primavera-P6',
                syncStatus: 'SUCCEEDED'
            })
        }));
        expect(mockDynamoSend).toHaveBeenCalled();
    });

    test("should handle errors properly and write failure sync status", async () => {
        getCredentials.mockResolvedValue({ username: 'testUser', password: 'testPass' });
        
        // Mock axios to throw an error
        axios.post.mockRejectedValue(new Error('API Error'));
        
        const mockDynamoSend = jest.fn().mockResolvedValue({});
        DynamoDBDocumentClient.from.mockImplementation(() => ({
            send: mockDynamoSend
        }));
        
        const activityId = 'A123';
        const updates = { Name: 'Updated Activity' };
        
        await expect(updateP6Activity(activityId, updates)).rejects.toThrow('Failed to update activity');
        
        // Verify DynamoDB write was called with failure status
        expect(PutCommand).toHaveBeenCalledWith(expect.objectContaining({
            TableName: 'test-table',
            Item: expect.objectContaining({
                PK: `ACTIVITY#${activityId}`,
                SK: 'SYNC',
                type: 'ActivitySync',
                targetSystem: 'Primavera-P6',
                syncStatus: 'FAILED'
            })
        }));
        expect(mockDynamoSend).toHaveBeenCalled();
    });
    
    test("should handle missing TABLE_NAME environment variable", async () => {
        // Save original and temporarily remove TABLE_NAME
        const originalTableName = process.env.TABLE_NAME;
        delete process.env.TABLE_NAME;
        
        getCredentials.mockResolvedValue({ username: 'TAEX_COM', password: 'testPass' });
        
        generateAuthHeaders.mockReturnValue({
            createdTime: '2024-01-01T00:00:00Z',
            expiresTime: '2024-01-01T00:05:00Z',
            nonce: 'testNonce'
        });
        
        axios.post.mockResolvedValue({
            data: `
                <SOAP-ENV:Envelope>
                    <SOAP-ENV:Body>
                        <UpdateActivitiesResponse>
                            <Result>Success</Result>
                        </UpdateActivitiesResponse>
                    </SOAP-ENV:Body>
                </SOAP-ENV:Envelope>
            `
        });
        
        const mockDynamoSend = jest.fn().mockResolvedValue({});
        DynamoDBDocumentClient.from.mockImplementation(() => ({
            send: mockDynamoSend
        }));
        
        const activityId = 'A123';
        const updates = { Name: 'Updated Activity' };
        
        const response = await updateP6Activity(activityId, updates);
        
        expect(response).toBeDefined();
        expect(axios.post).toHaveBeenCalled();
        
        // DynamoDB write should not be called when TABLE_NAME is missing
        expect(mockDynamoSend).not.toHaveBeenCalled();
        
        // Restore TABLE_NAME for other tests
        process.env.TABLE_NAME = originalTableName;
    });
});
