const { updateP6Activity } = require('./updateP6Activity');
const xml2js = require('xml2js');
require('dotenv').config(); // Load environment variables
const { getCredentials } = require('../utils');
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, PutCommand } = require('@aws-sdk/lib-dynamodb');

const PASSWORD = process.env.P6_PASSWORD;

// Mock DynamoDB to prevent actual writes during integration tests
jest.mock('@aws-sdk/client-dynamodb', () => ({
    DynamoDBClient: jest.fn().mockImplementation(() => ({
        send: jest.fn()
    }))
}));

jest.mock('@aws-sdk/lib-dynamodb', () => ({
    DynamoDBDocumentClient: {
        from: jest.fn().mockImplementation(() => ({
            send: jest.fn().mockResolvedValue({})
        }))
    },
    PutCommand: jest.fn()
}));

// Set test environment variables
process.env.TABLE_NAME = 'test-table';

jest.mock('../utils', () => {
    const originalModule = jest.requireActual('../utils'); // Import real module
    return {
        ...originalModule, // Spread real functions
        getCredentials: jest.fn(), // Mock only getCredentials
    };
});

describe("Integration Test - updateP6Activity", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });
    
    test("should update an activity in P6 using real API and write sync status", async () => {
        getCredentials.mockResolvedValue({ username: 'TAEX_COM', password: PASSWORD });

        const mockDynamoSend = jest.fn().mockResolvedValue({});
        DynamoDBDocumentClient.from.mockImplementation(() => ({
            send: mockDynamoSend
        }));

        const activityId = "35865"; // Replace with a real P6 Activity ID
        const updates = {
            UnitsPercentComplete: 0.8,
        };

        try {
            const response = await updateP6Activity(activityId, updates);
            
            console.log("P6 Update Response:", response);

            // Validate P6 response
            expect(response).toHaveProperty('SOAP-ENV:Envelope');
            expect(response["SOAP-ENV:Envelope"]).toHaveProperty('SOAP-ENV:Body');
            expect(response["SOAP-ENV:Envelope"]["SOAP-ENV:Body"]).toHaveProperty('UpdateActivitiesResponse');
            expect(response["SOAP-ENV:Envelope"]["SOAP-ENV:Body"]["UpdateActivitiesResponse"]).toHaveProperty('Return', 'true');
            
            // Verify DynamoDB write was called with correct parameters
            expect(PutCommand).toHaveBeenCalledWith(expect.objectContaining({
                TableName: 'test-table',
                Item: expect.objectContaining({
                    PK: `ACTIVITY#${activityId}`,
                    SK: 'SYNC',
                    type: 'ActivitySync',
                    TargetSystem: 'Primavery-P6',
                    SyncStatus: 'SUCCEEDED'
                })
            }));
            expect(mockDynamoSend).toHaveBeenCalled();
        } catch (error) {
            console.error("Integration Test Error:", error);
            throw error;
        }
    }, 20000); // Increase timeout if needed
});
