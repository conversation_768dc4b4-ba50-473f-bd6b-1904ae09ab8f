# updateActivity Lambda Function

## Overview
This AWS Lambda function updates activity data in Primavera P6 via SOAP API and records the sync status in DynamoDB. It's part of the p6-integration module that synchronizes data between Primavera P6 and the application's database, enabling bidirectional data flow.

## Function Purpose
- Provides a way to update activity data in Primavera P6 from the application
- Supports updating any activity field by passing the appropriate updates object
- Enables bidirectional synchronization between the application and P6
- Maintains data consistency between systems
- Records sync status in DynamoDB for tracking and auditing purposes

## How It Works
1. **Authentication**: Uses WS-Security with username/password, nonce, and timestamp for P6 SOAP API authentication
2. **Request Building**: Constructs a SOAP request with the activity ID and fields to update
3. **API Interaction**: Sends the request to the P6 Activity Service endpoint
4. **Response Processing**: Parses the XML response to confirm successful update
5. **Sync Status Recording**: Writes a sync status record to DynamoDB with success or failure status

## Environment Variables
- `P6_WS_URL`: Primavera P6 Web Service URL
- `SECRET_NAME`: AWS Secrets Manager secret name for P6 credentials
- `TABLE_NAME`: DynamoDB table name for storing sync status records

## Function Parameters
- `activityId` (string): The ObjectId of the activity to update in P6
- `updates` (object): Key-value pairs of fields to update and their new values

### Example Usage
```javascript
const { updateP6Activity } = require('./updateP6Activity');

// Update an activity's percent complete
const activityId = '12345';
const updates = {
  UnitsPercentComplete: 0.75,
  Status: 'In Progress'
};

try {
  const response = await updateP6Activity(activityId, updates);
  console.log('Activity updated successfully:', response);
} catch (error) {
  console.error('Failed to update activity:', error);
}
```

## Return Values
### Success Response
The function returns the parsed XML response from the P6 SOAP API, which includes confirmation of the update:

```javascript
{
  'SOAP-ENV:Envelope': {
    'SOAP-ENV:Body': {
      'UpdateActivitiesResponse': {
        'Return': 'true'
      }
    }
  }
}
```

### Error Response
If the update fails, the function throws an error with the message 'Failed to update activity'.

## DynamoDB Sync Status Record
The function writes a sync status record to DynamoDB with the following structure:

```javascript
{
  // —————— Primary Key ——————
  "PK":           "ACTIVITY#1234",        // partition on the Activity
  "SK":           "SYNC",                 // indicating sync

  // —————— Attributes ——————
  "type":         "ActivitySync",         // discriminator
  "TargetSystem": "Primavery-P6",         // e.g. "CRM", "Billing", etc.
  "LastSyncedAt": "2025-04-28T16:03:00Z", // ISO-8601 UTC timestamp
  "SyncStatus":   "SUCCEEDED",            // or "FAILED"
}
```

This record can be used for tracking synchronization status, auditing, and troubleshooting.

## Input Validation
The function validates that:
- `activityId` is provided and not empty
- `updates` object is provided and contains at least one field to update

## Error Handling
- Validates required input parameters before making API calls
- Logs detailed error information when API calls fail
- Throws standardized errors for easier handling by calling functions
- Includes the activity ID in error messages for better troubleshooting

## Dependencies
- **AWS SDK**: For accessing AWS services and DynamoDB
- **axios**: For making HTTP requests to the SOAP API
- **xml2js**: For parsing XML responses
- **Shared Utilities**: 
  - `getCredentials`: Retrieves P6 credentials from AWS Secrets Manager
  - `generateAuthHeaders`: Creates WS-Security headers for SOAP authentication

## Testing
The function includes both unit tests and integration tests:

### Unit Tests
- Tests successful activity updates with mocked responses
- Tests error handling with simulated failures
- Validates proper construction of SOAP requests

### Integration Tests
- Tests actual updates to P6 activities in a test environment
- Validates complete end-to-end functionality
- Verifies response structure from the real P6 API
