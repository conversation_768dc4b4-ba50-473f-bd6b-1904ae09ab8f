const { handler } = require('./index');
const { fetchAndWriteProjects } = require('./fetchP6Projects');

jest.mock('./fetchP6Projects', () => ({
    fetchAndWriteProjects: jest.fn()
}));

describe("Lambda Handler - fetchProjects Tests", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    test("should return a 200 response with projects", async () => {
        const mockProjects = [{ id: 'P1', name: 'Project 1' }];
        fetchAndWriteProjects.mockResolvedValue(mockProjects);

        const testEvent = { action: 'fetchProjects' };
        const response = await handler(testEvent);

        expect(response).toHaveProperty('statusCode', 200);
        expect(response).toHaveProperty('body');
        
        const responseBody = JSON.parse(response.body);
        expect(responseBody).toBeDefined();
        expect(responseBody).toHaveProperty('message', 'Projects fetched successfully');
        expect(responseBody).toHaveProperty('result', mockProjects);
    });

    test("should return a 500 response on error", async () => {
        fetchAndWriteProjects.mockRejectedValue(new Error("Test Error"));

        const testEvent = { action: 'fetchProjects' };
        const response = await handler(testEvent);

        expect(response).toHaveProperty('statusCode', 500);
        expect(response).toHaveProperty('body');
        
        const responseBody = JSON.parse(response.body);
        expect(responseBody).toHaveProperty('error', 'Test Error');
    });
});
