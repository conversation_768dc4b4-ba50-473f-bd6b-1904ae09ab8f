const axios = require('axios');
const xml2js = require('xml2js');
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, UpdateCommand, ScanCommand } = require('@aws-sdk/lib-dynamodb');
const { getCredentials, generateAuthHeaders } = require('../utils');
const { getAllProjects } = require('../fetchActivities/fetchP6Activities');


//for testing

// Initialize AWS SDK v3 DynamoDB clients
let dbClient = new DynamoDBClient({ region: 'eu-central-1' });
let docClient = DynamoDBDocumentClient.from(dbClient);

let ACTIVITIES_TABLE = process.env.PROJECTS_TABLE || 'your-local-table-name'; // Fallback for local testing
let P6_WS_URL = process.env.P6_WS_URL || 'your-local-p6-url'; // Fallback for local testing

// Debug mode flag - set to true to log actions without updating DynamoDB
const DRY_RUN = process.env.DRY_RUN === 'true' || true; // Default to true for local testing
const LOCAL_TESTING = process.env.LOCAL_TESTING === 'true' || true; // Default to true for local testing



//for testing end

/**
 * 
// Initialize AWS SDK v3 DynamoDB clients
const dbClient = new DynamoDBClient({ region: 'eu-central-1' });
const docClient = DynamoDBDocumentClient.from(dbClient);

const ACTIVITIES_TABLE = process.env.PROJECTS_TABLE;
const P6_WS_URL = process.env.P6_WS_URL;
*/



/**
 * Builds the SOAP request body to read projects.
 * @returns {string} - SOAP request XML body.
 */
const buildSoapRequest = (username, password, projectId) => {
    const { createdTime, expiresTime, nonce } = generateAuthHeaders(password);

    return `
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
                          xmlns:v1="http://xmlns.oracle.com/Primavera/P6/WS/ActivityCodeAssignment/V1"
                          xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">
            <soapenv:Header>
                <wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
                     <wsu:Timestamp wsu:Id="TS-1">
                        <wsu:Created>${createdTime}</wsu:Created>
                        <wsu:Expires>${expiresTime}</wsu:Expires>
                    </wsu:Timestamp>
                    <wsse:UsernameToken>
                        <wsse:Username>${username}</wsse:Username>
                        <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">${password}</wsse:Password>
                        <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">${nonce}</wsse:Nonce>
                        <wsu:Created>${createdTime}</wsu:Created>
                        <wsu:Expires>${expiresTime}</wsu:Expires>
                    </wsse:UsernameToken>
                </wsse:Security>
            </soapenv:Header>
            <soapenv:Body>
                 <v1:ReadActivityCodeAssignments>
                    <!-- Request all relevant fields -->
                    <v1:Field>ActivityCodeValue</v1:Field>
                    <v1:Field>ActivityCodeTypeName</v1:Field>
                    <v1:Field>ActivityCodeDescription</v1:Field>
                    <v1:Field>ActivityObjectId</v1:Field>
                    <v1:Field>ActivityName</v1:Field>
                    <v1:Field>ActivityId</v1:Field>
                    <v1:Field>ProjectId</v1:Field>
                    <v1:Field>ProjectObjectId</v1:Field>

                    <!-- Filter by the ProjectId and relevant code -->
                    <v1:Filter>ProjectId = '${projectId}' AND ActivityCodeTypeName IN ('SCNR ','EQPT','JOBORDERNO','NOTIFICATIONNR','MPHA ','CTR','WBS','SCOP ','SCNR ','OBS','CBS','SBS','SHCO','ADIS')</v1:Filter>
                </v1:ReadActivityCodeAssignments>
            </soapenv:Body>
        </soapenv:Envelope>
    `;
};

/**
 * Builds SOAP request for a specific activity
 */
const buildSoapRequestForActivity = (username, password, projectId, activityId) => {
    const { createdTime, expiresTime, nonce } = generateAuthHeaders(password);

    return `
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
                          xmlns:v1="http://xmlns.oracle.com/Primavera/P6/WS/ActivityCodeAssignment/V1"
                          xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">
            <soapenv:Header>
                <wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
                     <wsu:Timestamp wsu:Id="TS-1">
                        <wsu:Created>${createdTime}</wsu:Created>
                        <wsu:Expires>${expiresTime}</wsu:Expires>
                    </wsu:Timestamp>
                    <wsse:UsernameToken>
                        <wsse:Username>${username}</wsse:Username>
                        <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">${password}</wsse:Password>
                        <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">${nonce}</wsse:Nonce>
                        <wsu:Created>${createdTime}</wsu:Created>
                        <wsu:Expires>${expiresTime}</wsu:Expires>
                    </wsse:UsernameToken>
                </wsse:Security>
            </soapenv:Header>
            <soapenv:Body>
                <v1:ReadActivityCodeAssignments>
                    <v1:Field>ActivityCodeValue</v1:Field>
                    <v1:Field>ActivityCodeTypeName</v1:Field>
                    <v1:Field>ActivityCodeDescription</v1:Field>
                    <v1:Field>ActivityObjectId</v1:Field>
                    <v1:Field>ActivityName</v1:Field>
                    <v1:Field>ActivityId</v1:Field>
                    <v1:Field>ProjectId</v1:Field>
                    <v1:Field>ProjectObjectId</v1:Field>

                    <!-- Filter by both ProjectId and ActivityId -->
                    <v1:Filter>ProjectId = '${projectId}' AND ActivityId = '${activityId}' AND ActivityCodeTypeName IN ('SCNR ','EQPT','JOBORDERNO','NOTIFICATIONNR','MPHA ','CTR','WBS','SCOP ','SCNR ','OBS','CBS','SBS','SHCO','ADIS')</v1:Filter>
                </v1:ReadActivityCodeAssignments>
            </soapenv:Body>
        </soapenv:Envelope>
    `;
};

/**
 * Fetches activityCodes from Primavera P6 Web Service.
 * @returns {Promise<Object>} - Parsed activityCodes data.
 */
const fetchActivityCodes = async (projectId) => {
    //console.log(`[DEBUG] Fetching codes for Project: ${projectId}, Activity: ${activityId}`);
    const { username, password } = await getCredentials();

    const soapRequest = buildSoapRequest(username, password, projectId);

    // Construct endpoint
    const P6_ENDPOINT = P6_WS_URL + '/p6ws/services/ActivityCodeAssignmentService?wsdl';

    try {
        const response = await axios.post(P6_ENDPOINT, soapRequest, {
            headers: {
                'Content-Type': 'text/xml'
            }
        });

        // Parse the XML response
        const parser = new xml2js.Parser({ explicitArray: false });
        const parsedResult = await parser.parseStringPromise(response.data);

        // Extract activityCodes data
        var activityCodes = parsedResult['SOAP-ENV:Envelope']['SOAP-ENV:Body']['ReadActivityCodeAssignmentsResponse']['ActivityCodeAssignment'];

        // Convert webservice output to json
        activityCodes = replaceNilValues(activityCodes);

    
        return activityCodes ? activityCodes : { message: 'No activityCodes found' };
    } catch (error) {
        console.error('[ERROR] Error fetching activityCodes:', error.message);
        throw new Error('Failed to fetch activityCodes');
    }
};


/**
 * Fetches activity codes for a specific activity.
 */
const fetchActivityCodesForActivity = async (projectId, activityId) => {
    console.log(`[DEBUG] Fetching codes for Project: ${projectId}, Activity: ${activityId}`);
    const { username, password } = await getCredentials();

    const soapRequest = buildSoapRequestForActivity(username, password, projectId, activityId);
    const P6_ENDPOINT = P6_WS_URL + '/p6ws/services/ActivityCodeAssignmentService?wsdl';

    try {
        const response = await axios.post(P6_ENDPOINT, soapRequest, {
            headers: { 'Content-Type': 'text/xml' }
        });

        const parser = new xml2js.Parser({ explicitArray: false });
        const parsedResult = await parser.parseStringPromise(response.data);

        var activityCodes = parsedResult['SOAP-ENV:Envelope']['SOAP-ENV:Body']['ReadActivityCodeAssignmentsResponse']['ActivityCodeAssignment'];
        activityCodes = replaceNilValues(activityCodes);

        const count = Array.isArray(activityCodes) ? activityCodes.length : (activityCodes ? 1 : 0);
        console.log(`[DEBUG] Found ${count} code assignments for activity ${activityId}`);

        return activityCodes ? activityCodes : { message: 'No activityCodes found' };
    } catch (error) {
        console.error(`[ERROR] Error fetching codes for activity ${activityId}:`, error.message);
        throw new Error(`Failed to fetch codes for activity ${activityId}`);
    }
};


const getActivitiesForProject = async (projectId) => {
  
    try {

        // Now get all activities for this project
        const activityParams = {
            TableName: ACTIVITIES_TABLE,
            FilterExpression: "begins_with(PK, :pk_prefix) AND #projectId = :projectId",
            ExpressionAttributeNames: {
                "#projectId": "projectId"
            },
            ExpressionAttributeValues: {
                ":pk_prefix": "ACTIVITY#",
                ":projectId": projectId
            }
        };
        
        let allActivities = [];
        let lastEvaluatedKey;
        
        do {
            if (lastEvaluatedKey) {
                activityParams.ExclusiveStartKey = lastEvaluatedKey;
            }
            
            const response = await docClient.send(new ScanCommand(activityParams));
            
            if (response.Items && response.Items.length > 0) {
                allActivities = [...allActivities, ...response.Items];
                //console.log(`Retrieved ${response.Items.length} activities, total: ${allActivities.length}`);
            }
            
            lastEvaluatedKey = response.LastEvaluatedKey;
            
        } while (lastEvaluatedKey);
        
        // Extract relevant fields
        const activities = allActivities.map(activity => ({
            activityId: activity.activityId,
            activityObjectId: activity.activityObjectId
        }));
        
        console.log(`Found ${activities.length} activities for project ${projectId}`);
        return activities;
    } catch (error) {
        console.error(`Error retrieving activities for project ${projectId}:`, error);
        throw new Error(`Failed to retrieve activities for project ${projectId}`);
    }
};

/**
 * Recursively replaces {"$": {"xsi:nil":"true"}} with null in a JSON object.
 * @param {any} obj - JSON object to process.
 * @returns {any} - Processed JSON with null values.
 */
function replaceNilValues(obj) {
    if (Array.isArray(obj)) {
        return obj.map(replaceNilValues);
    } else if (typeof obj === 'object' && obj !== null) {
        return Object.fromEntries(
            Object.entries(obj).map(([key, value]) => [
                key,
                value && value.$ && value.$["xsi:nil"] === "true" ? null : replaceNilValues(value)
            ])
        );
    }
    return obj;
}
/**
 * Groups activity codes by ActivityId
 * @param {Array} activityCodes - Array of activity codes
 * @returns {Object} - Object with ActivityId as keys and arrays of related codes as values
 */
const groupActivityCodesByActivity = (activityCodes) => {
  
    const groupedCodes = {};
    
    if (Array.isArray(activityCodes) && activityCodes.length > 0) {
        activityCodes.forEach(code => {
            if (!groupedCodes[code.ActivityId]) {
                groupedCodes[code.ActivityId] = [];
            }
            groupedCodes[code.ActivityId].push(code);
        });
       
    } else if (activityCodes && activityCodes.ActivityId) {
        // Handle single activity code case
        groupedCodes[activityCodes.ActivityId] = [activityCodes];
       
    } else {
        console.log(`[DEBUG] No valid activity codes to group`);
    }

   
    return groupedCodes;
};

/**
 * Create update expression and attribute values based on activity codes
 * @param {Array} activityCodes - Array of activity codes for a single activity
 * @returns {Object} - Update expression and attribute values for DynamoDB update
 */
const createUpdateExpressionForActivity = (activityCodes) => {
    
    let updateExpression = "SET updatedAt = :updatedAt";
    let expressionAttributeValues = {
        ":updatedAt": new Date().toISOString()
    };
    let expressionAttributeNames = {};
    
    // Field name mappings for each code type
    const fieldMappings = {
        'SCNR': { 
            codeField: 'stopPointNr',
            descField: 'stopPointDescription'
        },
        'EQPT': {
            codeField: 'equipmentTagCode',
            descField: 'equipmentTagDescription'
        },
        'JOBORDERNO': {
            codeField: 'jobOrderNoCode',
            descField: 'jobOrderNoDescription'
        },
        'NOTIFICATIONNR': {
            codeField: 'notificationNrCode',
            descField: 'notificationNrDescription'
        },
        'MPHA': {
            codeField: 'mainPhaseCode',
            descField: 'mainPhaseDescription'
        },
        'CTR': {
            codeField: 'contractorCode',
            descField: 'contractorDescription'
        },
        'WBS': {
            codeField: 'wbsCode',
            descField: 'wbsDescription'
        },
        'SCOP': {
            codeField: 'scopeNrCode',
            descField: 'scopeNrDescription'
        },
        'OBS': {
            codeField: 'obsCode',
            descField: 'obsDescription'
        },
        'CBS': {
            codeField: 'cbsCode',
            descField: 'cbsDescription'
        },
        'SBS': {
            codeField: 'sbs1Code',
            descField: 'sbs1Description'
        },
        'SHCO': {
            codeField: 'shortCode',
            descField: 'shortDescription'
        },
        'ADIS': {
            codeField: 'disciplineCode',
            descField: 'disciplineDescription'
        }
    };
    
    // Process each activity code and create field mappings
    activityCodes.forEach((code) => {
        const codeType = code.ActivityCodeTypeName;
        
        // Get the field mappings for this code type
        const mapping = fieldMappings[codeType];
        
        // If no mapping exists, use a generic one
        const codeField = mapping ? mapping.codeField : `${codeType}Code`;
        const descField = mapping ? mapping.descField : `${codeType}Description`;

        
        // Create attribute names for the expression
        const codeAttrName = `#${codeType}_code`;
        const descAttrName = `#${codeType}_desc`;
        
        const codeAttrValue = `:${codeType}_code`;
        const descAttrValue = `:${codeType}_desc`;
        
        // Add code value
        updateExpression += `, ${codeAttrName} = ${codeAttrValue}`;
        expressionAttributeNames[codeAttrName] = codeField;
        expressionAttributeValues[codeAttrValue] = code.ActivityCodeValue;
        
        // Add code description
        updateExpression += `, ${descAttrName} = ${descAttrValue}`;
        expressionAttributeNames[descAttrName] = descField;
        expressionAttributeValues[descAttrValue] = code.ActivityCodeDescription;
    });

    return {
        updateExpression,
        expressionAttributeValues,
        expressionAttributeNames
    };
};
/**
 * Updates an activity in DynamoDB with activity code data
 * @param {string} activityId - Activity ID to update
 * @param {Array} activityCodes - Array of activity codes for this activity
 */
const updateActivityInDynamoDB = async (activityId, activityCodes) => {
    try {
        // Get ActivityObjectId from the first activity code (they all refer to the same activity)
        const activityObjectId = activityCodes[0].ActivityObjectId;
        
        const { updateExpression, expressionAttributeValues, expressionAttributeNames } = 
            createUpdateExpressionForActivity(activityCodes);
        
        const params = {
            TableName: ACTIVITIES_TABLE,
            Key: {
                PK: `ACTIVITY#${activityObjectId}`,
                SK: `METADATA#${activityObjectId}`
            },
            UpdateExpression: updateExpression,
            ExpressionAttributeValues: expressionAttributeValues,
            ExpressionAttributeNames: expressionAttributeNames
        };

        await docClient.send(new UpdateCommand(params));
      
    } catch (error) {
        console.error(`Failed to update activity ${activityId}:`, error);
    }
};

/**
 * Processes activity codes and updates them in DynamoDB
 * @param {Array|Object} activityCodes - Activity codes from SOAP response
 */
const processActivityCodes = async (activityCodes) => {
    try {
        if (!activityCodes || (Array.isArray(activityCodes) && activityCodes.length === 0)) {
            console.log('No activity codes to process');
            return;
        }
        // Group activity codes by activity
        const groupedActivityCodes = groupActivityCodesByActivity(activityCodes);
        
        const activityCount = Object.keys(groupedActivityCodes).length;
        console.log(`[DEBUG] Grouped into ${activityCount} unique activities`);
        
        // Process each activity's codes
        let processedCount = 0;
        for (const [activityId, codes] of Object.entries(groupedActivityCodes)) {
            console.log(`[DEBUG] Processing activity ${++processedCount}/${activityCount}: ${activityId}`);
            
            await updateActivityInDynamoDB(activityId, codes);
        }
       // console.log(`Processed activity codes for ${Object.keys(groupedActivityCodes).length} activities`);
    } catch (error) {
        console.error('Error processing activity codes:', error);
        throw new Error('Failed to process activity codes');
    }
};

/**
 * Process a project activity by activity when bulk request fails
 */
const processProjectByActivity = async (projectId) => {
    console.log(`[DEBUG] Processing project ${projectId} activity by activity`);
    
    try {
        // Get all activities for this project
        const activities = await getActivitiesForProject(projectId);
        
        if (activities.length === 0) {
            console.log(`No activities found for project ${projectId}`);
            return;
        }
        
        console.log(`Found ${activities.length} activities for project ${projectId}, processing in batches`);
        
        // Process in batches to manage memory usage
        const BATCH_SIZE = 20;
        for (let i = 0; i < activities.length; i += BATCH_SIZE) {
            const activityBatch = activities.slice(i, i + BATCH_SIZE);
            console.log(`Processing batch ${Math.floor(i/BATCH_SIZE) + 1} of ${Math.ceil(activities.length/BATCH_SIZE)}`);
            
            // Process each activity in the batch
            for (const activity of activityBatch) {
                try {
                    const activityCodes = await fetchActivityCodesForActivity(projectId, activity.activityId);
                    
                    if (activityCodes && activityCodes.message === 'No activityCodes found') {
                        console.log(`No activity codes found for activity ${activity.activityId}`);
                        continue;
                    }
                    
                    await processActivityCodes(activityCodes);
                } catch (error) {
                    console.error(`Error processing activity ${activity.activityId}:`, error.message);
                    // Continue with next activity
                }
            }
            
            // Add a small delay between batches
            if (i + BATCH_SIZE < activities.length) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        
        console.log(`Completed processing project ${projectId} activity by activity`);
    } catch (error) {
        console.error(`Error in processProjectByActivity for ${projectId}:`, error);
        throw error; 
    }
};
/**
 * Main function to fetch projects and process activity codes
 */
const fetchAndWriteActivityCodes = async () => {
    try {
        // For local testing with a specific project ID
        if (LOCAL_TESTING) {
            const projectIds = [
                'CAS.DESW.FAL',
                'PUR.T-T.FAL',
                'PUR.MR1.SPG-OB-P',
                'PUR.M-T.SPG - B3'
            ];
            
            for (const projectId of projectIds) {
                console.log(`[DEBUG] Processing project ID: ${projectId}`);
                
                try {
                    // First try fetching all activity codes for the project at once
                    const activityCodes = await fetchActivityCodes(projectId);
                    
                    if (activityCodes && activityCodes.message === 'No activityCodes found') {
                        console.log(`[DEBUG] No activity codes found for project ${projectId}`);
                        continue;
                    }
                    
                    // Process all the activity codes 
                    await processActivityCodes(activityCodes);
                    console.log(`[DEBUG] Finished processing project ${projectId} in bulk mode`);
                    
                } catch (error) {
                    // If bulk fetch fails, switch to activity-by-activity approach
                    console.log(`[DEBUG] Bulk fetch failed for project ${projectId}, switching to activity-by-activity approach`);
                    await processProjectByActivity(projectId);
                }
            }
            
            return {
                statusCode: 200,
                body: JSON.stringify({ message: 'Activity codes processed successfully' })
            };
        }
        
        // Fetch all projects from DynamoDB for production
        const projects = await getAllProjects();
        console.log(`Processing ${projects.length} projects`);
        
        // Process each project
        for (const project of projects) {
            const projectId = project.ProjectId || project.projectId;
            if (!projectId) {
                console.warn('Project missing Id, skipping:', project);
                continue;
            }
            
            console.log(`Processing project ${projectId}`);
            
            try {
                // First try fetching all activity codes for the project at once
                const activityCodes = await fetchActivityCodes(projectId);
                
                if (activityCodes && activityCodes.message === 'No activityCodes found') {
                    console.log(`[DEBUG] No activity codes found for project ${projectId}`);
                    continue;
                }
                
                // Process all the activity codes
                await processActivityCodes(activityCodes);
                console.log(`[DEBUG] Finished processing project ${projectId} in bulk mode`);
                
            } catch (error) {
                // If bulk fetch fails, switch to activity-by-activity approach
                console.log(`[DEBUG] Bulk fetch failed for project ${projectId}, switching to activity-by-activity approach`);
                await processProjectByActivity(projectId);
            }
        }
        
        console.log(`[DEBUG] All projects processed ${DRY_RUN ? '(DRY RUN MODE - no updates made)' : ''}`);
        
        return {
            statusCode: 200,
            body: JSON.stringify({ message: 'Activity codes processed successfully' })
        };
    } catch (error) {
        console.error('Error in fetchAndWriteActivityCodes:', error);
        return {
            statusCode: 500,
            body: JSON.stringify({ error: error.message })
        };
    }
};
// Function to allow local execution
const runLocally = async () => {
    // Import credential provider and configure AWS SDK with SSO
    const { fromSSO } = require('@aws-sdk/credential-providers');
    
    // Override the AWS clients with SSO credentials
    const profile = process.env.AWS_PROFILE || 'taggo';
    console.log(`Setting up AWS SSO with profile: ${profile}`);
    
    // Re-initialize clients with SSO credentials
    dbClient = new DynamoDBClient({ 
        region: process.env.AWS_REGION || 'eu-central-1',
        credentials: fromSSO({ profile })
    });
    docClient = DynamoDBDocumentClient.from(dbClient);
    console.log('---- STARTING LOCAL EXECUTION ----');
    console.log(`LOCAL_TESTING mode: ${LOCAL_TESTING ? 'ENABLED (using specific project)' : 'DISABLED'}`);
    

    try {
        const result = await fetchAndWriteActivityCodes();
        console.log('Execution completed with result:', result);
    } catch (error) {
        console.error('Execution failed with error:', error);
    }
    
    console.log('---- LOCAL EXECUTION FINISHED ----');
};


// Run locally if this file is executed directly
if (require.main === module) {
    runLocally();
}
module.exports = { fetchAndWriteActivityCodes };