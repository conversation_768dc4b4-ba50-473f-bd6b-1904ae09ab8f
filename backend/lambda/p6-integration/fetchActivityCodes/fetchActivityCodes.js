const axios = require('axios');
const xml2js = require('xml2js');
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, UpdateCommand, PutCommand } = require('@aws-sdk/lib-dynamodb');
const { getCredentials, generateAuthHeaders } = require('../utils');

// Initialize AWS SDK v3 DynamoDB clients
const dbClient = new DynamoDBClient({ region: 'eu-central-1' });
const docClient = DynamoDBDocumentClient.from(dbClient);

const ACTIVITIES_TABLE = process.env.PROJECTS_TABLE;
const P6_WS_URL = process.env.P6_WS_URL;




/**
 * Builds the SOAP request body to read projects.
 * @returns {string} - SOAP request XML body.
 */
const buildSoapRequest = (username, password, projectId) => {
    const { createdTime, expiresTime, nonce } = generateAuthHeaders(password);

    return `
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
                          xmlns:v1="http://xmlns.oracle.com/Primavera/P6/WS/ActivityCodeAssignment/V1"
                          xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">
            <soapenv:Header>
                <wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
                     <wsu:Timestamp wsu:Id="TS-1">
                        <wsu:Created>${createdTime}</wsu:Created>
                        <wsu:Expires>${expiresTime}</wsu:Expires>
                    </wsu:Timestamp>
                    <wsse:UsernameToken>
                        <wsse:Username>${username}</wsse:Username>
                        <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">${password}</wsse:Password>
                        <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">${nonce}</wsse:Nonce>
                        <wsu:Created>${createdTime}</wsu:Created>
                        <wsu:Expires>${expiresTime}</wsu:Expires>
                    </wsse:UsernameToken>
                </wsse:Security>
            </soapenv:Header>
            <soapenv:Body>
                 <v1:ReadActivityCodeAssignments>
                    <!-- Request all relevant fields -->
                    <v1:Field>ActivityCodeObjectId</v1:Field>
                    <v1:Field>ActivityCodeValue</v1:Field>
                    <v1:Field>ActivityCodeTypeName</v1:Field>
                    <v1:Field>ActivityCodeDescription</v1:Field>
                    <v1:Field>ActivityObjectId</v1:Field>
                    <v1:Field>ActivityId</v1:Field>

                    <!-- Filter by the ProjectId and relevant code -->
                    <v1:Filter>ProjectObjectId = ${projectId} AND ActivityCodeTypeName IN ('SCNR', 'LIB', 'EQPT', 'JOBORDERNO', 'NOTIFICATIONNR', 'MPHA', 'CTR', 'WBS', 'SCOP', 'OBS', 'CBS', 'SBS', 'SHCO', 'ADIS', 'PROCESSAREA', 'FLOC', 'JOBLOCATION', 'PHAS', 'UNIT', 'WONR')</v1:Filter>
                </v1:ReadActivityCodeAssignments>
            </soapenv:Body>
        </soapenv:Envelope>
    `;
};


/**
 * Fetches activityCodes from Primavera P6 Web Service.
 * @returns {Promise<Object>} - Parsed activityCodes data.
 */
const fetchActivityCodes = async (projectId) => {
    const { username, password } = await getCredentials();

    const soapRequest = buildSoapRequest(username, password, projectId);

    // Construct endpoint
    const P6_ENDPOINT = P6_WS_URL + '/p6ws/services/ActivityCodeAssignmentService?wsdl';

    try {
        const response = await axios.post(P6_ENDPOINT, soapRequest, {
            headers: {
                'Content-Type': 'text/xml'
            }
        });

        // Parse the XML response
        const parser = new xml2js.Parser({ explicitArray: false });
        const parsedResult = await parser.parseStringPromise(response.data);

        // Extract activityCodes data
        var activityCodes = parsedResult['SOAP-ENV:Envelope']['SOAP-ENV:Body']['ReadActivityCodeAssignmentsResponse']['ActivityCodeAssignment'];

        // Convert webservice output to json
        activityCodes = replaceNilValues(activityCodes);

        return activityCodes ? activityCodes : { message: 'No activityCodes found' };
    } catch (error) {
        console.error('[ERROR] Error fetching activityCodes:', error.message);
        throw new Error('Failed to fetch activityCodes');
    }
};

/**
 * Write sync job log to DynamoDB
 * @param {string} projectId - The project ID
 * @param {string} status - The sync status (SUCCEEDED or FAILED)
 * @param {Object} metadata - Additional metadata about the sync job
 * @returns {Promise<Object>} - The sync record that was written
 */
async function writeSyncJobLog(projectId, status, metadata = {}) {
    if (!ACTIVITIES_TABLE) {
        console.warn('ACTIVITIES_TABLE environment variable not set, skipping sync job log write');
        return null;
    }

    const timestamp = new Date().toISOString();
    const syncRecord = {
        PK: "SYNCJOB",
        SK: `ACTIVITYCODES#${projectId}#${timestamp}`,
        type: "syncjob",
        projectId: projectId,
        status: status, // "SUCCEEDED" or "FAILED"
        timestamp: timestamp,
        ...metadata
    };
    
    try {
        // Initialize DynamoDB client
        const client = new DynamoDBClient();
        const docClient = DynamoDBDocumentClient.from(client);
        
        // Write to DynamoDB
        await docClient.send(new PutCommand({
            TableName: ACTIVITIES_TABLE,
            Item: syncRecord
        }));
        
        console.log(`Sync job log written for project ${projectId}: ${status}`);
        return syncRecord;
    } catch (error) {
        console.error(`Error writing sync job log for project ${projectId}:`, error);
        // We don't throw here to avoid affecting the main function flow
        return null;
    }
}

/**
 * Recursively replaces {"$": {"xsi:nil":"true"}} with null in a JSON object.
 * @param {any} obj - JSON object to process.
 * @returns {any} - Processed JSON with null values.
 */
function replaceNilValues(obj) {
    if (Array.isArray(obj)) {
        return obj.map(replaceNilValues);
    } else if (typeof obj === 'object' && obj !== null) {
        return Object.fromEntries(
            Object.entries(obj).map(([key, value]) => [
                key,
                value && value.$ && value.$["xsi:nil"] === "true" ? null : replaceNilValues(value)
            ])
        );
    }
    return obj;
}

/**
 * Mapping configuration for activity code types to field names
 * This makes it easy to add new mappings in the future
 */
const ACTIVITY_CODE_MAPPINGS = {
    'ADIS': 'discipline',
    'FLOC': 'floc',
    'WONR': 'workorderNo',
    'SCNR': 'scopeNr',
    'EQPT': 'equipment',
    'JOBORDERNO': 'jobOrderNumber',
    'NOTIFICATIONNR': 'notificationNumber',
    'SCOP': 'scopeId',
    'PHAS': 'phase',
    'CTR': 'contractor'
};

/**
 * Helper function to apply activity code mappings
 * @param {Object} activityData - The activity data object to update
 * @param {string} codeTypeName - The code type name
 * @param {string} codeTypeValue - The code type value
 */
const applyActivityCodeMappings = (activityData, codeTypeName, codeTypeValue) => {
    const fieldName = ACTIVITY_CODE_MAPPINGS[codeTypeName];
    if (fieldName) {
        activityData[fieldName] = codeTypeValue;
    }
};

/**
 * Groups activity codes by ActivityObjectId and maps special code types to standard fields
 * @param {Array} activityCodes - Array of activity codes from SOAP response
 * @returns {Object} - Grouped activity codes by ActivityObjectId with mapped fields
 */
const groupActivityCodesByObjectId = (activityCodes) => {
    const groupedCodes = {};
    
    // Process each activity code
    activityCodes.forEach(activityCode => {
        const activityObjectId = activityCode.ActivityObjectId;
        const codeTypeName = activityCode.ActivityCodeTypeName?.trim();
        const codeTypeValue = activityCode.ActivityCodeValue;
        const codeObjectId = activityCode.ActivityCodeObjectId;
        const codeTypeDescription = activityCode.ActivityCodeDescription;
        
        // Initialize group if it doesn't exist
        if (!groupedCodes[activityObjectId]) {
            const initialData = {
                activityId: activityCode.ActivityId,
                activityCodes: []
            };
            
            // Initialize all possible mapped fields to null
            Object.values(ACTIVITY_CODE_MAPPINGS).forEach(fieldName => {
                initialData[fieldName] = null;
            });
            
            groupedCodes[activityObjectId] = initialData;
        }
        
        // Add the code to the activity's codes array
        groupedCodes[activityObjectId].activityCodes.push({
            id: codeObjectId,
            codeTypeName,
            codeTypeValue,
            codeTypeDescription
        });
        
        // Apply mappings dynamically
        applyActivityCodeMappings(groupedCodes[activityObjectId], codeTypeName, codeTypeValue);
    });
    
    return groupedCodes;
};

/**
 * Updates activity records in DynamoDB with grouped activity codes and mapped fields
 * @param {Object} groupedCodes - Activity codes grouped by ActivityObjectId
 * @returns {Promise<void>}
 */
const updateActivitiesWithCodes = async (groupedCodes) => {
    try {
        const now = new Date().toISOString();
        const activityObjectIds = Object.keys(groupedCodes);
        
        console.log(`Updating ${activityObjectIds.length} activities with grouped activity codes`);
        
        // Process each activity
        for (let i = 0; i < activityObjectIds.length; i++) {
            const activityObjectId = activityObjectIds[i];
            const activityData = groupedCodes[activityObjectId];
            
            // Prepare update expression and attribute values
            let updateExpression = 'SET updatedAt = :updatedAt, activityCodes = :activityCodes';
            const expressionAttributeValues = {
                ':updatedAt': now,
                ':activityCodes': activityData.activityCodes
            };
            
            // Dynamically add mapped fields if they exist
            Object.entries(ACTIVITY_CODE_MAPPINGS).forEach(([codeType, fieldName]) => {
                
                if (activityData[fieldName]) {
                    updateExpression += `, ${fieldName} = :${fieldName}`;
                    expressionAttributeValues[`:${fieldName}`] = activityData[fieldName];
                }
            });
            
            const params = {
                TableName: ACTIVITIES_TABLE,
                Key: {
                    PK: `ACTIVITY#${activityObjectId}`,
                    SK: `METADATA#${activityObjectId}`
                },
                UpdateExpression: updateExpression,
                ExpressionAttributeValues: expressionAttributeValues
            };
            
            try {
                await docClient.send(new UpdateCommand(params));
                console.log(`Updated activity ${activityObjectId} (${i + 1}/${activityObjectIds.length})`);
            } catch (error) {
                console.error(`Failed to update activity ${activityObjectId}:`, error);
                // Continue with other activities even if one fails
            }
        }
        
        console.log(`Completed updating ${activityObjectIds.length} activities`);
    } catch (error) {
        console.error('Failed to update activities with codes:', error);
        throw error;
    }
};

/**
 * Processes activity codes, groups them by ActivityObjectId, and updates activity records
 * @param {Array|Object} activityCodes - Activity codes from SOAP response
 */
const processActivityCodes = async (activityCodes) => {
    try {
        if (!activityCodes || (Array.isArray(activityCodes) && activityCodes.length === 0)) {
            console.log('No activity codes to process');
            return;
        }
        
        // Convert to array if it's a single object
        const activityCodesArray = Array.isArray(activityCodes) ? activityCodes : [activityCodes];
        
        console.log(`Processing ${activityCodesArray.length} activity codes`);
        
        // Group activity codes by ActivityObjectId and map special code types
        const groupedCodes = groupActivityCodesByObjectId(activityCodesArray);
        
        // Update activity records with grouped codes and mapped fields
        await updateActivitiesWithCodes(groupedCodes);
        
        console.log(`Completed processing ${activityCodesArray.length} activity codes`);
    } catch (error) {
        console.error('Error processing activity codes:', error);
        throw new Error('Failed to process activity codes');
    }
};

/**
 * Main function to fetch projects and process activity codes
 * @param {string} projectId - The ID of the project to fetch activity codes for
 */
const fetchAndWriteActivityCodes = async (projectId) => {
    const startTime = new Date();
    console.log(`[${startTime.toISOString()}] Starting activity code fetch and write operation for project ${projectId}`);
    
    try {
        
        // Fetch operation
        const fetchStartTime = new Date();
        console.log(`[${fetchStartTime.toISOString()}] Starting fetch operation for project ${projectId}`);
        const activityCodes = await fetchActivityCodes(projectId);
        const fetchEndTime = new Date();
        console.log(`[${fetchEndTime.toISOString()}] Fetch operation completed in ${(fetchEndTime - fetchStartTime)/1000} seconds`);
        
        if (activityCodes && activityCodes.message === 'No activityCodes found') {
            console.log(`No activity codes found for project ${projectId}`);
            return {
                statusCode: 200,
                body: JSON.stringify({ message: 'No activity codes found to process' })
            };
        }
        
        // Write operation
        const writeStartTime = new Date();
        console.log(`[${writeStartTime.toISOString()}] Starting write operation`);
        await processActivityCodes(activityCodes);
        const writeEndTime = new Date();
        console.log(`[${writeEndTime.toISOString()}] Write operation completed in ${(writeEndTime - writeStartTime)/1000} seconds`);
        
        const endTime = new Date();
        const executionTime = (endTime - startTime)/1000;
        console.log(`[${endTime.toISOString()}] Operation completed in ${executionTime} seconds`);
        
        // Get count of activity codes processed
        const activityCodesCount = Array.isArray(activityCodes) ? activityCodes.length : (activityCodes ? 1 : 0);
        
        // Write sync job log for successful operation
        await writeSyncJobLog(projectId, "SUCCEEDED", {
            executionTime: executionTime,
            fetchTime: (fetchEndTime - fetchStartTime)/1000,
            writeTime: (writeEndTime - writeStartTime)/1000,
            activityCodesProcessed: activityCodesCount
        });
        
        return {
            statusCode: 200,
            body: JSON.stringify({ 
                message: 'Activity codes processed successfully',
                executionTime: `${executionTime} seconds`,
                activityCodesProcessed: activityCodesCount
            })
        };
    } catch (error) {
        const endTime = new Date();
        const executionTime = (endTime - startTime)/1000;
        console.error(`[${endTime.toISOString()}] Error in fetchAndWriteActivityCodes:`, error);
        
        // Write sync job log for failed operation
        await writeSyncJobLog(projectId, "FAILED", {
            executionTime: executionTime,
            error: error.message
        });
        
        return {
            statusCode: 500,
            body: JSON.stringify({ 
                error: error.message,
                executionTime: `${executionTime} seconds`
            })
        };
    }
};

module.exports = { fetchAndWriteActivityCodes };
