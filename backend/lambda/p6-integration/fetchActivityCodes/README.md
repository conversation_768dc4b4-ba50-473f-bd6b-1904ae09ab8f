# fetchActivityCodes Lambda Function

## Overview
This AWS Lambda function fetches activity code assignments from Primavera P6 via SOAP API and updates activity records in DynamoDB with the code information. It's part of the p6-integration module that synchronizes data between Primavera P6 and the application's database.

## Function Purpose
- Retrieves activity code assignments for projects from Primavera P6
- Maps specific activity code types to standardized fields in the database
- Updates activity records with code information for better categorization and filtering
- Enhances project data with additional metadata from activity codes

## How It Works
1. **Authentication**: Uses WS-Security with username/password, nonce, and timestamp for P6 SOAP API authentication
2. **Data Retrieval**: Sends SOAP requests to fetch activity code assignments for a specific project
3. **Data Processing**:
   - Extracts relevant code information from the SOAP response
   - Groups codes by activity object ID
   - Maps special code types to standard fields using the ACTIVITY_CODE_MAPPINGS configuration
4. **Database Update**: Updates DynamoDB records with the processed information
5. **Fallback Mechanism**: If bulk processing fails, switches to activity-by-activity processing

## Environment Variables
- `P6_WS_URL`: Primavera P6 Web Service URL
- `PROJECTS_TABLE`: DynamoDB table name for storing project and activity data
- `SECRET_NAME`: AWS Secrets Manager secret name for P6 credentials

## Running Locally
To run the function locally, use the provided `local-run-fetch-activity-codes.js` script:

```powershell
# Set environment variables
$env:P6_WS_URL = "http://p6ta-pri-weblogic-qa.749506386854.aws.glpoly.net:8212"
$env:SECRET_NAME = "PrimaveraCredentials"
$env:PROJECTS_TABLE = "TAExGraphQlApiStack-TAExTable948F8D9B-PNHWWR1O612L"
$env:AWS_PROFILE = "taggo"  # Your AWS CLI profile name

# Run the script with a project ID
node local-run-fetch-activity-codes.js "CAS.DESW.FAL"
```

The script configures AWS credentials using the specified profile and calls the `fetchAndWriteActivityCodes` function with the provided project ID.

## Function Parameters
- `projectId` (string): The ID of the project to fetch activity codes for

## Return Values
### Success Response
```json
{
  "statusCode": 200,
  "body": {
    "message": "Activity codes processed successfully",
    "executionTime": "10.5 seconds"
  }
}
```

### Error Response
```json
{
  "statusCode": 500,
  "body": {
    "error": "Failed to fetch activityCodes",
    "executionTime": "2.3 seconds"
  }
}
```

## Activity Code Mappings
The function maps specific P6 activity code types to standardized fields in the database:

| P6 Code Type | Database Field |
|--------------|----------------|
| ADIS         | discipline     |
| FLOC         | floc           |
| WONR         | workorderNo    |
| SCNR         | scopeNr        |
| EQPT         | equipment      |
| JOBORDERNO   | jobOrderNumber |
| NOTIFICATIONNR | notificationNumber |
| SCOP         | scopeId        |
| PHAS         | phase          |

These mappings are defined in the `ACTIVITY_CODE_MAPPINGS` object and can be extended as needed.

## Error Handling
- Comprehensive error logging with timestamps
- Fallback to activity-by-activity processing when bulk operations fail
- Continues processing remaining activities even if individual updates fail
- Returns appropriate HTTP status codes and error messages

## Performance Optimization
- Execution time tracking for overall operation, fetch phase, and write phase
- Batch processing of activities to manage memory usage
- Efficient DynamoDB update operations with dynamic expression building

## Dependencies
- **AWS SDK v3**: For DynamoDB operations
  - `@aws-sdk/client-dynamodb`
  - `@aws-sdk/lib-dynamodb`
- **axios**: For HTTP requests to the SOAP API
- **xml2js**: For parsing XML responses
- **Shared Utilities**: 
  - `getCredentials`: Retrieves P6 credentials from AWS Secrets Manager
  - `generateAuthHeaders`: Creates WS-Security headers for SOAP authentication
