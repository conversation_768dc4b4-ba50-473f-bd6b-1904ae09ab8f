const axios = require('axios');
const xml2js = require('xml2js');
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, UpdateCommand, ScanCommand } = require('@aws-sdk/lib-dynamodb');
const { getCredentials, generateAuthHeaders } = require('../utils');
const { getAllProjects } = require('../fetchActivities/fetchP6Activities');

// Initialize AWS SDK v3 DynamoDB clients
const dbClient = new DynamoDBClient({ region: 'eu-central-1' });
const docClient = DynamoDBDocumentClient.from(dbClient);

const ACTIVITIES_TABLE = process.env.PROJECTS_TABLE || 'your-local-table-name'; // Fallback for local testing
const P6_WS_URL = process.env.P6_WS_URL || 'your-local-p6-url'; // Fallback for local testing

// Debug mode flag - set to true to log actions without updating DynamoDB
const DRY_RUN = process.env.DRY_RUN === 'true' || true; // Default to true for local testing
const LOCAL_TESTING = process.env.LOCAL_TESTING === 'true' || true; // Default to true for local testing
const LOCAL_PROJECT_ID = process.env.LOCAL_PROJECT_ID || 'CAS.DESW.FAL'; // Default project ID for local testing

/**
 * Builds the SOAP request body to read resource assignments.
 * @param {string} username - P6 username
 * @param {string} password - P6 password
 * @param {string} projectId - Project ID to fetch resource assignments for
 * @returns {string} - SOAP request XML body
 */
const buildSoapRequest = (username, password, projectId) => {
    const { createdTime, expiresTime, nonce } = generateAuthHeaders(password);

    return `
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
                          xmlns:v1="http://xmlns.oracle.com/Primavera/P6/WS/ResourceAssignment/V1"
                          xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">
            <soapenv:Header>
                <wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
                     <wsu:Timestamp wsu:Id="TS-1">
                        <wsu:Created>${createdTime}</wsu:Created>
                        <wsu:Expires>${expiresTime}</wsu:Expires>
                    </wsu:Timestamp>
                    <wsse:UsernameToken>
                        <wsse:Username>${username}</wsse:Username>
                        <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">${password}</wsse:Password>
                        <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">${nonce}</wsse:Nonce>
                        <wsu:Created>${createdTime}</wsu:Created>
                        <wsu:Expires>${expiresTime}</wsu:Expires>
                    </wsse:UsernameToken>
                </wsse:Security>
            </soapenv:Header>
            <soapenv:Body>
                <v1:ReadResourceAssignments>
                    <!-- Request fields -->
                    <v1:Field>ActivityId</v1:Field>
                    <v1:Field>ResourceId</v1:Field>
                    <v1:Field>ResourceName</v1:Field>
                    <v1:Field>RoleName</v1:Field>
                    <v1:Field>RoleId</v1:Field>
                    <v1:Field>RoleShortName</v1:Field>
                    <v1:Field>ProjectId</v1:Field>
                    <v1:Field>ActivityObjectId</v1:Field>
                    
                    <!-- Filter by ProjectId -->
                    <v1:Filter>ProjectId = '${projectId}'</v1:Filter>
                </v1:ReadResourceAssignments>
            </soapenv:Body>
        </soapenv:Envelope>
    `;
};

/**
 * Fetches resource assignments from Primavera P6 Web Service.
 * @param {string} projectId - Project ID to fetch resource assignments for
 * @returns {Promise<Object>} - Parsed resource assignments data
 */
const fetchResourceAssignments = async (projectId) => {
    console.log(`[DEBUG] Preparing to fetch resource assignments for project ID: ${projectId}`);
    const { username, password } = await getCredentials();

    const soapRequest = buildSoapRequest(username, password, projectId);

    // Construct endpoint
    const P6_ENDPOINT = P6_WS_URL + '/p6ws/services/ResourceAssignmentService?wsdl';

    try {
        const response = await axios.post(P6_ENDPOINT, soapRequest, {
            headers: {
                'Content-Type': 'text/xml'
            }
        });

        // Parse the XML response
        const parser = new xml2js.Parser({ explicitArray: false });
        const parsedResult = await parser.parseStringPromise(response.data);

        // Extract resource assignments data
        var resourceAssignments = parsedResult['SOAP-ENV:Envelope']['SOAP-ENV:Body']['ReadResourceAssignmentsResponse']['ResourceAssignment'];

        // Convert webservice output to json, handling nil values
        resourceAssignments = replaceNilValues(resourceAssignments);

        // Log some debug info
        const count = Array.isArray(resourceAssignments) ? resourceAssignments.length : (resourceAssignments ? 1 : 0);
        console.log(`[DEBUG] Extracted ${count} resource assignments from response`);
        

        return resourceAssignments ? resourceAssignments : { message: 'No resource assignments found' };
    } catch (error) {
        console.error('[ERROR] Error fetching resource assignments:', error.message);
        if (error.response) {
            console.error('[ERROR] Response status:', error.response.status);
            console.error('[ERROR] Response data:', error.response.data);
        }
        throw new Error('Failed to fetch resource assignments');
    }
};

/**
 * Recursively replaces {"$": {"xsi:nil":"true"}} with null in a JSON object.
 * @param {any} obj - JSON object to process.
 * @returns {any} - Processed JSON with null values.
 */
function replaceNilValues(obj) {
    if (Array.isArray(obj)) {
        return obj.map(replaceNilValues);
    } else if (typeof obj === 'object' && obj !== null) {
        return Object.fromEntries(
            Object.entries(obj).map(([key, value]) => [
                key,
                value && value.$ && value.$["xsi:nil"] === "true" ? null : replaceNilValues(value)
            ])
        );
    }
    return obj;
}

/**
 * Groups resource assignments by ActivityId
 * @param {Array|Object} resourceAssignments - Resource assignments from SOAP response
 * @returns {Object} - Object with ActivityId as keys and arrays of related resources as values
 */
const groupResourcesByActivity = (resourceAssignments) => {
    const groupedResources = {};
    
    if (Array.isArray(resourceAssignments) && resourceAssignments.length > 0) {
        resourceAssignments.forEach(resource => {
            if (!groupedResources[resource.ActivityId]) {
                groupedResources[resource.ActivityId] = [];
            }
            groupedResources[resource.ActivityId].push(resource);
        });
       
    } else if (resourceAssignments && resourceAssignments.ActivityId) {
        // Handle single resource assignment case
        groupedResources[resourceAssignments.ActivityId] = [resourceAssignments];
    } else {
        console.log(`[DEBUG] No valid resource assignments to group`);
    }

    return groupedResources;
};

/**
 * Updates an activity in DynamoDB with resource assignment data
 * @param {string} activityId - Activity ID to update
 * @param {Array} resources - Array of resource assignments for this activity
 */
const updateActivityInDynamoDB = async (activityId, resources) => {
    try {
        if (!resources || resources.length === 0) {
            console.log(`No resources to update for activity ${activityId}`);
            return;
        }

        // Get ActivityObjectId from the first resource (they all refer to the same activity)
        const activityObjectId = resources[0].ActivityObjectId;
        
        // Extract and concatenate resource names and role names with semicolons
        const resourceNames = resources.map(r => r.ResourceName || 'Unknown').join(' ; ');
        const roleNames = resources.map(r => r.RoleName || 'Unknown').join(' ; ');
        
        // Create update expression for DynamoDB
        let updateExpression = "SET updatedAt = :updatedAt, resourceName = :resourceName, roleName = :roleName";
        let expressionAttributeValues = {
            ":updatedAt": new Date().toISOString(),
            ":resourceName": resourceNames,
            ":roleName": roleNames
        };
        
        /* // Add role ID if available
        const roleIds = resources.map(r => r.RoleId || 'Unknown').join(' ; ');
        if (roleIds) {
            updateExpression += ", roleId = :roleId";
            expressionAttributeValues[":roleId"] = roleIds;
        }
        
        // Add role short name if available
        const roleShortNames = resources.map(r => r.RoleShortName || 'Unknown').join(' ; ');
        if (roleShortNames) {
            updateExpression += ", roleShortName = :roleShortName";
            expressionAttributeValues[":roleShortName"] = roleShortNames;
        } */
        
        const params = {
            TableName: ACTIVITIES_TABLE,
            Key: {
                PK: `ACTIVITY#${activityObjectId}`,
                SK: `METADATA#${activityObjectId}`
            },
            UpdateExpression: updateExpression,
            ExpressionAttributeValues: expressionAttributeValues
        };
        
    
        // Actually update DynamoDB
        await docClient.send(new UpdateCommand(params));
        
    } catch (error) {
        console.error(`Failed to update activity ${activityId}:`, error);
    }
};

/**
 * Processes resource assignments and updates them in DynamoDB
 * @param {Array|Object} resourceAssignments - Resource assignments from SOAP response
 */
const processResourceAssignments = async (resourceAssignments) => {
    try {
        if (!resourceAssignments || (Array.isArray(resourceAssignments) && resourceAssignments.length === 0)) {
            console.log('No resource assignments to process');
            return;
        }
        
        // Group resource assignments by activity
        const groupedResources = groupResourcesByActivity(resourceAssignments);
        
        // Process each activity's resources
        for (const [activityId, resources] of Object.entries(groupedResources)) {
            
            await updateActivityInDynamoDB(activityId, resources);
        }
       
    } catch (error) {
        console.error('Error processing resource assignments:', error);
        throw new Error('Failed to process resource assignments');
    }
};

/**
 * Main function to fetch projects and process resource assignments
 */
const fetchAndWriteResourceAssignments = async () => {
    try {
        // For local testing with a specific project ID
        if (LOCAL_TESTING) {
            const projectIds = [
              'CAS.DESW.FAL',
              'PUR.T-T.FAL',
              'PUR.MR1.SPG-OB-P'
            ];
            
            for (const projectId of projectIds) {
                console.log(`[DEBUG] Processing project ID: ${projectId}`);
                try {
                    const resourceAssignments = await fetchResourceAssignments(projectId);
                    
                    if (resourceAssignments && resourceAssignments.message === 'No resource assignments found') {
                        console.log(`[DEBUG] No resource assignments found for project ${projectId}`);
                        continue;
                    }
                    
                    await processResourceAssignments(resourceAssignments);
                    console.log(`[DEBUG] Finished processing project ${projectId}`);
                } catch (error) {
                    console.error(`[ERROR] Failed to process project ${projectId}:`, error.message);
                    // Continue with next project
                }
            }
            
            return {
                statusCode: 200,
                body: JSON.stringify({ message: 'Resource assignments processed successfully' })
            };
        }
        
        // Fetch all projects from DynamoDB
        const projects = await getAllProjects();
        console.log(`Processing ${projects.length} projects`);
        
        // Process each project
        for (const project of projects) {
            const projectId = project.ProjectId || project.projectId;
            if (!projectId) {
                console.warn('Project missing Id, skipping:', project);
                continue;
            }
            
            console.log(`Fetching resource assignments for project ${projectId}`);
            try {
                const resourceAssignments = await fetchResourceAssignments(projectId);

                if (resourceAssignments && resourceAssignments.message === 'No resource assignments found') {
                    console.log(`[DEBUG] No resource assignments found for project ${projectId}`);
                    continue;
                }
                
                await processResourceAssignments(resourceAssignments);
            } catch (error) {
                console.error(`[ERROR] Failed to process project ${projectId}:`, error.message);
                // Continue with next project
            }
        }
        
        return {
            statusCode: 200,
            body: JSON.stringify({ message: 'Resource assignments processed successfully' })
        };
    } catch (error) {
        console.error('Error in fetchAndWriteResourceAssignments:', error);
        return {
            statusCode: 500,
            body: JSON.stringify({ error: error.message })
        };
    }
};


module.exports = { fetchAndWriteResourceAssignments };
