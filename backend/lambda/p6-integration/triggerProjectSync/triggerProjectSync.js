const { DynamoDBClient, QueryCommand } = require("@aws-sdk/client-dynamodb");
const { SQSClient, SendMessageCommand } = require("@aws-sdk/client-sqs");

const dynamoDBClient = new DynamoDBClient({});
const sqsClient = new SQSClient({});

const TABLE_NAME = process.env.PROJECTS_TABLE;
const SQS_QUEUE_URL = process.env.SYNC_QUEUE_URL;

async function triggerProjectSync() {
    try {
        const params = {
            TableName: TABLE_NAME,
            IndexName: 'type-index', // Ensure this index exists
            KeyConditionExpression: "#type = :projectType",
            FilterExpression: "#isSyncing = :isSyncing",
            ExpressionAttributeNames: {
                "#type": "type",
                "#isSyncing": "isSyncing"
            },
            ExpressionAttributeValues: {
                ":projectType": { S: "PROJECT" },
                ":isSyncing": { BOOL: true }
            }
        };

        const { Items } = await dynamoDBClient.send(new QueryCommand(params));

        if (!Items || Items.length === 0) {
            console.log("No projects found with isSyncing = true.");
            return { message: "No projects to process." };
        }

        // Send messages to SQS
        const sendMessages = Items.map(async (project) => {
            const messageBody = JSON.stringify({
                action: "fetchActivities",
                id: project.id.S,
                projectId: project.projectId.S,
                createdAt: new Date().toISOString()
            });

            const sendMessageParams = {
                QueueUrl: SQS_QUEUE_URL,
                MessageBody: messageBody
            };

            return sqsClient.send(new SendMessageCommand(sendMessageParams));
        });

        await Promise.all(sendMessages);

        console.log(`Sent ${Items.length} messages to SQS.`);
        return { message: `Sent ${Items.length} fetchActivities messages to SQS.` };
    } catch (error) {
        console.error("Error processing projects:", error);
        return { error: "Failed to process projects." };
    }
}

module.exports = { triggerProjectSync };
