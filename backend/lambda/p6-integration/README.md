# AWS Lambda - Primavera P6 SOAP API

## Overview
This AWS Lambda function interacts with the Primavera P6 Web Services API to fetch project details using SOAP requests. It requires authentication using WS-Security with a nonce, timestamp, and password digest.

## Setting Environment Variables (PowerShell)
To run the Lambda function locally, you must set the following environment variables in PowerShell:

```
$env:P6_WS_URL = "http://p6ta-pri-weblogic-qa.749506386854.aws.glpoly.net:8212"
$env:SECRET_NAME="PrimaveraCredentials"
$env:P6_USERNAME = "your_username"
$env:P6_PASSWORD = "your_password"
$env:PROJECTS_TABLE = "TAExGraphQlApiStack-TAExTable948F8D9B-PNHWWR1O612L"
$env:AWS_PROFILE = my-sso-profile
```

## Install Dependencies
Run the following command to install required dependencies:
```
npm install
```

## Running the Function Locally
To invoke the function locally using Node.js, run:
```
node index.js
```