const axios = require('axios');
const xml2js = require('xml2js');
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, BatchWriteCommand, UpdateCommand, PutCommand } = require('@aws-sdk/lib-dynamodb');
const { getCredentials, generateAuthHeaders } = require('../utils');

// Initialize AWS SDK v3 DynamoDB clients
const dbClient = new DynamoDBClient({ region: 'eu-central-1' });
const docClient = DynamoDBDocumentClient.from(dbClient);

const ACTIVITIES_TABLE = process.env.PROJECTS_TABLE;
const P6_WS_URL = process.env.P6_WS_URL;



/**
 * Builds the SOAP request body to read projects.
  * @returns {string} - SOAP request XML body.
 */
const buildSoapRequest = (username, password, projectId) => {
    const { createdTime, expiresTime, nonce, passwordDigest } = generateAuthHeaders(password);

    return `
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
                          xmlns:tns="http://xmlns.oracle.com/Primavera/P6/WS/Project/V2"
                        xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">
            <soapenv:Header>
                <wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
                     <wsu:Timestamp wsu:Id="TS-1">
                        <wsu:Created>${createdTime}</wsu:Created>
                        <wsu:Expires>${expiresTime}</wsu:Expires>
                    </wsu:Timestamp>
                    <wsse:UsernameToken>
                        <wsse:Username>${username}</wsse:Username>
                        <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">${password}</wsse:Password>
                        <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">${nonce}</wsse:Nonce>
                        <wsu:Created xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">${createdTime}</wsu:Created>
                        <wsu:Expires xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">${expiresTime}</wsu:Expires>
                    </wsse:UsernameToken>
                </wsse:Security>
            </soapenv:Header>
            <soapenv:Body>
                <tns:ReadActivities>
                    <tns:Field>Id</tns:Field>
                    <tns:Field>ObjectId</tns:Field>
                    <tns:Field>Name</tns:Field>
                   
                    <tns:Field>StartDate</tns:Field>
                    <tns:Field>FinishDate</tns:Field>
                    <tns:Field>ActualStartDate</tns:Field>
                    <tns:Field>ActualFinishDate</tns:Field>
                    <tns:Field>PlannedStartDate</tns:Field>
                    <tns:Field>PlannedFinishDate</tns:Field>
                    
                    <tns:Field>LocationName</tns:Field>
                    <tns:Field>PercentComplete</tns:Field>
                    <tns:Field>ProjectId</tns:Field>
                    <tns:Field>ProjectName</tns:Field>
                    <tns:Field>StatusCode</tns:Field>
                    <tns:Field>ReviewStatus</tns:Field>
                    <tns:Field>Status</tns:Field>
                    <tns:Field>Type</tns:Field>

                    <tns:Field>PrimaryResourceId</tns:Field>
                    <tns:Field>PrimaryResourceName</tns:Field>
                    <tns:Field>PrimaryResourceObjectId</tns:Field>

                    <tns:Filter>ProjectObjectId = ${projectId}</tns:Filter>
                </tns:ReadActivities>
            </soapenv:Body>
        </soapenv:Envelope>
    `;
};

/**
 * Fetches Activities from Primavera P6 Web Service.
 * @returns {Promise<Object>} - Parsed Activities data.
 */
const fetchActivities = async (projectId) => {
    const { username, password } = await getCredentials();

    const soapRequest = buildSoapRequest(username, password, projectId);

    // Construct endpoint
    const P6_ENDPOINT = P6_WS_URL + '/p6ws/services/ActivityService?wsdl';

    try {
        const response = await axios.post(P6_ENDPOINT, soapRequest, {
            headers: {
                'Content-Type': 'text/xml'
            }
        });

        console.log(soapRequest)

        // Parse the XML response
        const parser = new xml2js.Parser({ explicitArray: false });
        const parsedResult = await parser.parseStringPromise(response.data);

        console.log(parsedResult)

        // Extract project data
        var activities = parsedResult['SOAP-ENV:Envelope']['SOAP-ENV:Body']['ReadActivitiesResponse']['Activity'];

        // Convert webservice output to json
        activities = replaceNilValues(activities);

        return activities ? activities : { message: 'No activities found' };
    } catch (error) {
        console.error('Error fetching activities:', error.message);
        throw new Error('Failed to fetch activities');
    }
};

/**
 * Recursively replaces {"$": {"xsi:nil":"true"}} with null in a JSON object.
 * @param {any} obj - JSON object to process.
 * @returns {any} - Processed JSON with null values.
 */
function replaceNilValues(obj) {
    if (Array.isArray(obj)) {
        return obj.map(replaceNilValues);
    } else if (typeof obj === 'object' && obj !== null) {
        return Object.fromEntries(
            Object.entries(obj).map(([key, value]) => [
                key,
                value && value.$ && value.$["xsi:nil"] === "true" ? null : replaceNilValues(value)
            ])
        );
    }
    return obj;
}

/**
 * Maps activity data to DynamoDB update parameters.
 */
const mapActivityToDynamoDBUpdateParams = (activity, project) => {
    const statusMapping = {
        "Not Started": "NOT_STARTED",
        "In Progress": "IN_PROGRESS",
        "Completed": "COMPLETED",
    };

    const statusCodeMapping = {
       "Planned": "PLANNED",
       "Active": "ACTIVE",
       "Inactive": "INACTIVE",
       "What-If": "WHATIF",
       "Requested": "REQUESTED",
       "Template": "TEMPLATE"
    };

    // Helper function to convert date fields to ISO string
    const toISOString = (date) => date ? new Date(date).toISOString() : null;

    return {
        TableName: ACTIVITIES_TABLE,
        Key: {
            PK: `ACTIVITY#${activity.ObjectId}`,
            SK: `METADATA#${activity.ObjectId}`
        },
        UpdateExpression: `
            SET #type = :type,
                #activityId = :activityId,
                #id = :id,
                #name = :name,
                #startDate = :startDate,
                #finishDate = :finishDate,
                #actualStartDate = :actualStartDate,
                #actualFinishDate = :actualFinishDate,
                #plannedStartDate = :plannedStartDate,
                #plannedFinishDate = :plannedFinishDate,
                #estimatedStartDate = :estimatedStartDate,
                #estimatedFinishDate = :estimatedFinishDate,
                #locationName = :locationName,
                #percentComplete = :percentComplete,
                #projectId = :projectId,
                #projectName = :projectName,
                #projectObjectId = :projectObjectId,
                #status = :status,
                #statusCode = :statusCode,
                #reviewStatus = :reviewStatus,
                #activityType = :activityType,
                #resourceId = :resourceId,
                #resourceName = :resourceName,
                #resourceObjectId = :resourceObjectId,
                #updatedAt = :updatedAt
        `,
        ExpressionAttributeNames: {
            "#type": "type",
            "#activityId": "activityId",
            "#id": "id",
            "#name": "name",
            "#startDate": "startDate",
            "#finishDate": "finishDate",
            "#actualStartDate": "actualStartDate",
            "#actualFinishDate": "actualFinishDate",
            "#plannedStartDate": "plannedStartDate",
            "#plannedFinishDate": "plannedFinishDate",
            "#estimatedStartDate": "estimatedStartDate",
            "#estimatedFinishDate": "estimatedFinishDate",
            "#locationName": "locationName",
            "#percentComplete": "percentComplete",
            "#projectId": "projectId",
            "#projectName": "projectName",
            "#projectObjectId": "projectObjectId",
            "#status": "status",
            "#statusCode": "statusCode",
            "#reviewStatus": "reviewStatus",
            "#activityType": "activityType",
            "#resourceId": "resourceId",
            "#resourceName": "resourceName",
            "#resourceObjectId": "resourceObjectId",
            "#updatedAt": "updatedAt"
        },
        ExpressionAttributeValues: {
            ":type": "ACTIVITY",
            ":activityId": activity.Id,
            ":id": activity.ObjectId || null,
            ":name": activity.Name || null,
            ":startDate": toISOString(activity.StartDate) || null,
            ":finishDate": toISOString(activity.FinishDate) || null,
            ":actualStartDate": toISOString(activity.ActualStartDate),
            ":actualFinishDate": toISOString(activity.ActualFinishDate),
            ":plannedStartDate": toISOString(activity.PlannedStartDate),
            ":plannedFinishDate": toISOString(activity.PlannedFinishDate),
            ":estimatedStartDate": project && project.forecastStartDate ? project.forecastStartDate : null,
            ":estimatedFinishDate": project && project.forecastFinishDate ? project.forecastFinishDate : null,
            ":locationName": activity.LocationName || null,
            ":percentComplete": activity.PercentComplete !== undefined ? parseFloat(activity.PercentComplete) : null,
            ":projectId": activity.ProjectId,
            ":projectName": activity.ProjectName,
            ":projectObjectId": project && project.id ? project.id: null,
            ":status": statusMapping[activity.Status] || null,
            ":statusCode": statusCodeMapping[activity.StatusCode] || null,
            ":reviewStatus": activity.ReviewStatus || null,
            ":activityType": activity.Type || null,
            ":resourceId": activity.PrimaryResourceId || null,
            ":resourceName": activity.PrimaryResourceName || null,
            ":resourceObjectId": activity.PrimaryResourceObjectId || null,
            ":updatedAt": new Date().toISOString()
        },
        ReturnValues: "UPDATED_NEW"
    };
};

/**
 * Write sync job log to DynamoDB
 * @param {string} projectId - The project ID
 * @param {string} status - The sync status (SUCCEEDED or FAILED)
 * @param {Object} metadata - Additional metadata about the sync job
 * @returns {Promise<Object>} - The sync record that was written
 */
async function writeSyncJobLog(projectId, status, metadata = {}) {
    if (!ACTIVITIES_TABLE) {
        console.warn('ACTIVITIES_TABLE environment variable not set, skipping sync job log write');
        return null;
    }

    const timestamp = new Date().toISOString();
    const syncRecord = {
        PK: "SYNCJOB",
        SK: `ACTIVITIES#${projectId}#${timestamp}`,
        type: "syncjob",
        projectId: projectId,
        status: status, // "SUCCEEDED" or "FAILED"
        timestamp: timestamp,
        ...metadata
    };
    
    try {
        // Initialize DynamoDB client
        const client = new DynamoDBClient();
        const docClient = DynamoDBDocumentClient.from(client);
        
        // Write to DynamoDB
        await docClient.send(new PutCommand({
            TableName: ACTIVITIES_TABLE,
            Item: syncRecord
        }));
        
        console.log(`Sync job log written for project ${projectId}: ${status}`);
        return syncRecord;
    } catch (error) {
        console.error(`Error writing sync job log for project ${projectId}:`, error);
        // We don't throw here to avoid affecting the main function flow
        return null;
    }
}

/**
 * Saves project data to DynamoDB.
 */
const saveActivityToDynamoDB = async (activity, project) => {
    try {
        const params = mapActivityToDynamoDBUpdateParams(activity, project);
        await docClient.send(new UpdateCommand(params));
    } catch (error) {
        console.error(`Failed to save activity ${activity.Id}:`, error.message);
        if (error.name === 'ValidationException') {
            console.error('Validation error details:', JSON.stringify(error, null, 2));
            // Log the params that caused the error (excluding sensitive data)
            const sanitizedParams = { ...params };
            delete sanitizedParams.ExpressionAttributeValues; // Remove potentially large values
            console.error('Update params causing the error:', JSON.stringify(sanitizedParams, null, 2));
        }
    }
};

/**
 * Saves activities to DynamoDB using batch write.
 * @param {Array} activities - Array of activities to save.
 * @param {Object} project - Project data.
 * @returns {Promise<Object>} - Result of the batch write operation.
 */
const batchSaveActivitiesToDynamoDB = async (activities, project) => {
    if (!activities || activities.length === 0) {
        console.log('No activities to save');
        return { processed: 0 };
    }

    // Process in batches for better performance
    const batchSize = 25; // Process 25 activities at a time
    let processed = 0;
    let failed = 0;

    console.log(`Preparing to save ${Array.isArray(activities) ? activities.length : 1} activities in batches`);

    // Convert to array if single activity
    const activitiesArray = Array.isArray(activities) ? activities : [activities];
    
    // Process items in batches
    for (let i = 0; i < activitiesArray.length; i += batchSize) {
        const batch = activitiesArray.slice(i, i + batchSize);
        const batchNumber = Math.floor(i / batchSize) + 1;
        const totalBatches = Math.ceil(activitiesArray.length / batchSize);
        
        console.log(`Processing batch ${batchNumber}/${totalBatches} (${batch.length} items)`);
        
        try {
            // Use Promise.all to process all activities in the batch concurrently
            await Promise.all(batch.map(activity => saveActivityToDynamoDB(activity, project)));
            
            processed += batch.length;
            console.log(`Batch ${batchNumber} complete: ${batch.length} items processed successfully`);
            
            // Small delay between batches to avoid throttling
            if (i + batchSize < activitiesArray.length) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        } catch (error) {
            console.error(`Error processing batch ${batchNumber}:`, error.message);
            failed += batch.length;
        }
    }

    console.log(`Batch processing complete: ${processed} activities saved, ${failed} failed`);
    return { processed, failed };
};

/**
 * Fetches activities for a specific project and writes them to DynamoDB.
 * @param {string} projectId - The ID of the project to fetch activities for.
 * @returns {Promise<Object>} - Result of the operation.
 */
const fetchAndWriteActivities = async (projectId) => {
    const startTime = new Date();
    console.time('fetchAndWriteActivities-total');
    
    if (!projectId) {
        console.error('No projectId provided');
        return {
            statusCode: 400,
            body: JSON.stringify({ error: 'projectId is required' }),
        };
    }
    
    try {
        console.log(`Fetching activities for project ID: ${projectId}`);
        
        // Create a minimal project object with the required fields
        const project = {
            projectId: projectId,
            id: projectId // Using projectId as id since we don't have the actual project object
        };
        
        const activities = await fetchActivities(projectId);
        
        if (!activities) {
            console.log(`No activities found for project ${projectId}`);
            console.timeEnd('fetchAndWriteActivities-total');
            return {
                statusCode: 200,
                body: JSON.stringify({ message: 'No activities found' }),
            };
        }
        
        let activitiesProcessed = 0;
        
        if (Array.isArray(activities) && activities.length > 0) {
            console.log(`Found ${activities.length} activities for project ${projectId}`);
            const result = await batchSaveActivitiesToDynamoDB(activities, project);
            activitiesProcessed = result.processed;
        } else if (activities && !activities.message) {
            // Single activity case
            await saveActivityToDynamoDB(activities, project);
            activitiesProcessed = 1;
        } else {
            console.log(`No activities found or error: ${activities.message || 'Unknown error'}`);
        }
        
        console.log(`Total activities processed: ${activitiesProcessed}`);
        console.timeEnd('fetchAndWriteActivities-total');
        
        // Calculate execution time
        const endTime = new Date();
        const executionTime = (endTime - startTime)/1000;
        
        // Write sync job log for successful operation
        await writeSyncJobLog(projectId, "SUCCEEDED", {
            executionTime: executionTime,
            activitiesProcessed: activitiesProcessed
        });
        
        return {
            statusCode: 200,
            body: JSON.stringify({ 
                message: 'Activities processing completed', 
                summary: {
                    projectId: projectId,
                    activitiesProcessed: activitiesProcessed,
                    executionTime: `${executionTime} seconds`
                }
            }),
        };
    } catch (error) {
        console.error('Error in fetchAndWriteActivities:', error);
        console.timeEnd('fetchAndWriteActivities-total');
        
        // Calculate execution time
        const endTime = new Date();
        const executionTime = (endTime - startTime)/1000;
        
        // Write sync job log for failed operation
        await writeSyncJobLog(projectId, "FAILED", {
            executionTime: executionTime,
            error: error.message
        });
        
        return {
            statusCode: 500,
            body: JSON.stringify({ 
                error: error.message,
                executionTime: `${executionTime} seconds`
            }),
        };
    }
};

module.exports = { fetchAndWriteActivities };
