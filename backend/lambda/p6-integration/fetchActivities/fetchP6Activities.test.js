const { fetchAndWriteActivities } = require('./fetchP6Activities');
const { getCredentials } = require('../utils');
const axios = require('axios');
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, PutCommand } = require('@aws-sdk/lib-dynamodb');

const PASSWORD = process.env.P6_PASSWORD;

jest.mock('../utils', () => ({
    getCredentials: jest.fn(),
    generateAuthHeaders: jest.fn().mockReturnValue({
        createdTime: '2024-01-01T00:00:00Z',
        expiresTime: '2024-01-01T00:05:00Z',
        nonce: 'testNonce',
        passwordDigest: 'testDigest'
    })
}));

// Mock DynamoDB
jest.mock('@aws-sdk/client-dynamodb', () => ({
    DynamoDBClient: jest.fn().mockImplementation(() => ({
        send: jest.fn()
    }))
}));

jest.mock('@aws-sdk/lib-dynamodb', () => ({
    DynamoDBDocumentClient: {
        from: jest.fn().mockImplementation(() => ({
            send: jest.fn().mockResolvedValue({})
        }))
    },
    PutCommand: jest.fn(),
    UpdateCommand: jest.fn()
}));

// Set environment variables for testing
process.env.PROJECTS_TABLE = 'test-table';

describe("fetchAndWriteActivities Tests", () => {
    let mockDynamoSend;
    
    beforeEach(() => {
        jest.clearAllMocks();
        
        // Setup mock for DynamoDB send
        mockDynamoSend = jest.fn().mockResolvedValue({});
        DynamoDBDocumentClient.from.mockImplementation(() => ({
            send: mockDynamoSend
        }));
    });

    test("should fetch and process activities successfully and write sync log", async () => {
        // Mock successful activity fetch
        getCredentials.mockResolvedValue({ username: 'TAEX_COM', password: 'testPassword' });
        
        // Mock axios to return a successful response with activity data
        jest.spyOn(axios, 'post').mockResolvedValueOnce({
            data: `
                <SOAP-ENV:Envelope>
                    <SOAP-ENV:Body>
                        <ReadActivitiesResponse>
                            <Activity>
                                <ObjectId>A1</ObjectId>
                                <Id>101</Id>
                                <Name>Test Activity</Name>
                                <Status>In Progress</Status>
                                <ProjectId>P1</ProjectId>
                                <ProjectName>Test Project</ProjectName>
                            </Activity>
                        </ReadActivitiesResponse>
                    </SOAP-ENV:Body>
                </SOAP-ENV:Envelope>
            `
        });

        const projectId = 'P1';
        const response = await fetchAndWriteActivities(projectId);

        // Verify response
        expect(response).toHaveProperty('statusCode', 200);
        expect(response).toHaveProperty('body');
        
        const responseBody = JSON.parse(response.body);
        expect(responseBody).toHaveProperty('message', 'Activities processing completed');
        expect(responseBody.summary).toHaveProperty('activitiesProcessed', 1);
        
        // Verify sync job log was written
        expect(PutCommand).toHaveBeenCalledWith(expect.objectContaining({
            TableName: 'test-table',
            Item: expect.objectContaining({
                PK: 'SYNCJOB',
                SK: expect.stringMatching(/^ACTIVITIES#P1#.+/),
                type: 'syncjob',
                status: 'SUCCEEDED',
                projectId: 'P1',
                activitiesProcessed: 1
            })
        }));
        
        // Verify DynamoDB send was called for the sync log
        expect(mockDynamoSend).toHaveBeenCalled();
    });

    test("should handle errors properly and write failed sync log", async () => {
        // Mock credential error
        const testError = new Error("Test Error");
        getCredentials.mockRejectedValue(testError);

        const projectId = 'P1';
        const response = await fetchAndWriteActivities(projectId);

        // Verify error response
        expect(response).toHaveProperty('statusCode', 500);
        expect(response).toHaveProperty('body');
        
        const responseBody = JSON.parse(response.body);
        expect(responseBody).toHaveProperty('error', 'Test Error');
        
        // Verify failed sync job log was written
        expect(PutCommand).toHaveBeenCalledWith(expect.objectContaining({
            TableName: 'test-table',
            Item: expect.objectContaining({
                PK: 'SYNCJOB',
                SK: expect.stringMatching(/^ACTIVITIES#P1#.+/),
                type: 'syncjob',
                status: 'FAILED',
                projectId: 'P1',
                error: 'Test Error'
            })
        }));
        
        // Verify DynamoDB send was called for the sync log
        expect(mockDynamoSend).toHaveBeenCalled();
    });
    
    test("should handle missing TABLE_NAME environment variable", async () => {
        // Save original and temporarily remove TABLE_NAME
        const originalTableName = process.env.PROJECTS_TABLE;
        delete process.env.PROJECTS_TABLE;
        
        // Mock successful activity fetch
        getCredentials.mockResolvedValue({ username: 'TAEX_COM', password: 'testPassword' });
        
        // Mock axios to return a successful response with activity data
        jest.spyOn(axios, 'post').mockResolvedValueOnce({
            data: `
                <SOAP-ENV:Envelope>
                    <SOAP-ENV:Body>
                        <ReadActivitiesResponse>
                            <Activity>
                                <ObjectId>A1</ObjectId>
                                <Id>101</Id>
                                <Name>Test Activity</Name>
                            </Activity>
                        </ReadActivitiesResponse>
                    </SOAP-ENV:Body>
                </SOAP-ENV:Envelope>
            `
        });

        const projectId = 'P1';
        const response = await fetchAndWriteActivities(projectId);

        // Verify response
        expect(response).toHaveProperty('statusCode', 200);
        
        // Verify DynamoDB send was not called when TABLE_NAME is missing
        expect(mockDynamoSend).not.toHaveBeenCalled();
        
        // Restore TABLE_NAME for other tests
        process.env.PROJECTS_TABLE = originalTableName;
    });
});
