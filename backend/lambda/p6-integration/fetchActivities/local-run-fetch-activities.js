// local-run-fetch-activities.js
const { fromIni } = require('@aws-sdk/credential-providers');

// Configuration - Replace these values with your own
const AWS_PROFILE = 'taggo'; // Your AWS CLI profile name
const AWS_REGION = 'eu-central-1';
const PROJECTS_TABLE = 'TAExGraphQlApiStack-TAExTable948F8D9B-PNHWWR1O612L'; // Your DynamoDB table name
const P6_WS_URL = 'http://p6ta-pri-weblogic-qa.749506386854.aws.glpoly.net:8212'; // Your P6 Web Service URL
const SECRET_NAME = 'PrimaveraCredentials'; // Your AWS Secrets Manager secret name

// Configure AWS SDK to use the specified profile
process.env.AWS_SDK_LOAD_CONFIG = 1;
process.env.AWS_PROFILE = AWS_PROFILE;

// Set environment variables needed by the function
process.env.PROJECTS_TABLE = PROJECTS_TABLE;
process.env.P6_WS_URL = P6_WS_URL;
process.env.LOCAL_TESTING = 'true';
process.env.SECRET_NAME = SECRET_NAME;

// Import the module after setting environment variables
const { fetchAndWriteActivities } = require('./fetchP6Activities');

// For local testing, we need to configure the AWS SDK to use the profile credentials
const credentials = fromIni({ profile: AWS_PROFILE });

// Override the DynamoDB client configuration in the module
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const originalDynamoDBClient = DynamoDBClient;
require('@aws-sdk/client-dynamodb').DynamoDBClient = class extends originalDynamoDBClient {
  constructor(config) {
    super({
      ...config,
      credentials,
      region: AWS_REGION
    });
  }
};

// Also override the SecretsManagerClient to use the same credentials
const { SecretsManagerClient } = require('@aws-sdk/client-secrets-manager');
const originalSecretsManagerClient = SecretsManagerClient;
require('@aws-sdk/client-secrets-manager').SecretsManagerClient = class extends originalSecretsManagerClient {
  constructor(config) {
    super({
      ...config,
      credentials,
      region: AWS_REGION
    });
  }
};

// Execute the function
async function run() {
  try {
    console.log('Starting fetchActivities execution...');
    console.log(`Using AWS Profile: ${AWS_PROFILE}`);
    console.log(`Using DynamoDB Table: ${PROJECTS_TABLE}`);
    console.log(`Using P6 Web Service URL: ${P6_WS_URL}`);
    console.log(`Using Secret Name: ${SECRET_NAME}`);
    
    // Get projectId from command line args or use default
    const projectId = process.argv[2] || '783'; // Default to '783' if no argument provided
    console.log(`Using Project ID: ${projectId}`);
    
    const result = await fetchAndWriteActivities(projectId);
    console.log('Execution completed successfully:');
    console.log(JSON.stringify(result, null, 2));
  } catch (error) {
    console.error('Error executing fetchActivities:', error);
    process.exit(1);
  }
}

run();
