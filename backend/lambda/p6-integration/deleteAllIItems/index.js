import { DynamoDBClient, ScanCommand, BatchWriteItemCommand } from "@aws-sdk/client-dynamodb";

const client = new DynamoDBClient({});
const tableName = "TAExGraphQlApiStack-TAExTable948F8D9B-PNHWWR1O612L";

export const handler = async () => {
    try {
        let lastEvaluatedKey = null;

        do {
            // Scan to get items
            const scanParams = { TableName: tableName, ExclusiveStartKey: lastEvaluatedKey };
            const scanResult = await client.send(new ScanCommand(scanParams));

            if (!scanResult.Items || scanResult.Items.length === 0) break;

            // Convert items to delete requests
            const deleteRequests = scanResult.Items.map((item) => ({
              DeleteRequest: { 
                  Key: { 
                      PK: item.PK, 
                      SK: item.SK 
                  } 
              }
          }));

            // Process in chunks of 25
            for (let i = 0; i < deleteRequests.length; i += 25) {
                const batch = deleteRequests.slice(i, i + 25);
                await client.send(new BatchWriteItemCommand({ RequestItems: { [tableName]: batch } }));
            }

            lastEvaluatedKey = scanResult.LastEvaluatedKey;
        } while (lastEvaluatedKey);

        console.log("All items deleted successfully.");
        return { statusCode: 200, body: JSON.stringify({ message: "All items deleted successfully." }) };
    } catch (error) {
        console.error("Error deleting items:", error);
        return { statusCode: 500, body: JSON.stringify({ error: "Failed to delete items." }) };
    }
};
