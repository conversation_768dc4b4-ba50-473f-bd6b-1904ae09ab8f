const { fetchAndWriteProjects } = require('./fetchProjects/fetchP6Projects');
const { updateP6Activity } = require('./updateActivity/updateP6Activity');
const { fetchAndWriteActivities } = require('./fetchActivities/fetchP6Activities');
const { fetchAndWriteActivityCodes } = require('./fetchActivityCodes/fetchActivityCodes');
const { fetchAndWriteResourceAssignments } = require('./fetchResourceAssignments/fetchResouceAssignments');
const { triggerProjectSync } = require('./triggerProjectSync/triggerProjectSync');
const { fetchAndWriteRelationships } = require('./fetchRelationships/fetchRelationships');

/**
 * AWS Lambda Handler
 * @param {Object} event - Lambda event containing query parameters.
 */
exports.handler = async function (event) {
    console.log('Received event:', JSON.stringify(event, null, 2));

    try {
        // Check if the event is from SQS and extract the first record's body
        if (event.Records && event.Records.length > 0 && event.Records[0].eventSource === 'aws:sqs') {
            event = JSON.parse(event.Records[0].body);
            console.log('Processed SQS event:', JSON.stringify(event, null, 2));
        }

        if (event.action === 'fetchProjects') {
            const result = await fetchAndWriteProjects();
            return {
                statusCode: 200,
                body: JSON.stringify({ message: 'Projects fetched successfully', result }),
            };
        } else if (event.action === 'updateActivity') {
            if (!event.activityId || !event.updates) {
                throw new Error('Missing required parameters: activityId and updates');
            }
            
            const result = await updateP6Activity(event.activityId, event.updates);
            return {
                statusCode: 200,
                body: JSON.stringify({ message: 'Activity updated successfully', result }),
            };
        } else if (event.action === 'triggerProjectSync') {
            const result = await triggerProjectSync();
            return {
                statusCode: 200,
                body: JSON.stringify({ message: 'Activity updated successfully', result }),
            };
        } else if (event.action === 'fetchRelationships') {
            if (!event.id) {
                throw new Error('Missing required parameter: id');
            }
            
            const result = await fetchAndWriteRelationships(event.id);
            return {
                statusCode: 200,
                body: JSON.stringify({ message: 'Relationships fetched successfully', result }),
            };
        } else if (event.action === 'fetchActivities') {

            if (!event.id) {
                throw new Error('Missing required parameter: id');
            }

            const activitiesResult = await fetchAndWriteActivities(event.id);
            const activityCodesResult = await fetchAndWriteActivityCodes(event.id);
            const relationshipsResult = await fetchAndWriteRelationships(event.id);
            return {
                statusCode: 200,
                body: JSON.stringify({
                    message: 'Data fetched successfully',
                    result: {
                        activitiesResult,
                        activityCodesResult,
                        relationshipsResult
                    }
                }),
            };
        } else {
            console.log('No valid action provided, exiting.');
            return {
                statusCode: 400,
                body: JSON.stringify({ message: 'Invalid action' }),
            };
        }
    } catch (error) {
        console.error('Error processing request:', error);
        return {
            statusCode: 500,
            body: JSON.stringify({ error: error.message }),
        };
    }
};
