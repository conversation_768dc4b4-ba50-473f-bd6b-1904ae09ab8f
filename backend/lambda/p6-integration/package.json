{"name": "aws-lambda-auth", "version": "1.0.0", "description": "AWS Lambda function with authentication logic", "main": "index.js", "scripts": {"start": "node index.js", "test": "jest", "deploy": "serverless deploy"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.290.0", "@aws-sdk/client-secrets-manager": "^3.290.0", "@aws-sdk/credential-providers": "^3.782.0", "@aws-sdk/lib-dynamodb": "^3.290.0"}, "devDependencies": {"crypto-js": "^4.1.1", "dotenv": "^16.0.3", "jest": "^29.6.2", "moment": "^2.30.1", "serverless": "^3.32.2", "xml2js": "^0.6.2"}}