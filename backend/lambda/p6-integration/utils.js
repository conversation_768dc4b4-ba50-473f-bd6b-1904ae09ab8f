const crypto = require('crypto');
const moment = require('moment');
const { SecretsManagerClient, GetSecretValueCommand } = require('@aws-sdk/client-secrets-manager');

const SECRET_NAME = process.env.SECRET_NAME;

/**
 * Fetches credentials from AWS Secrets Manager.
 * @returns {Promise<Object>} - The credentials containing username and password.
 */
async function getCredentials() {
    try {
        const secretsClient = new SecretsManagerClient();
        const secretResponse = await secretsClient.send(new GetSecretValueCommand({ SecretId: SECRET_NAME }));
        if (!secretResponse.SecretString) {
            throw new Error("SecretString is empty");
        }
        const secretData = JSON.parse(secretResponse.SecretString);
        return { username: secretData.username, password: secretData.password };
    } catch (error) {
        console.error("Error retrieving secret:", error);
        throw error;
    }
}

/**
 * Generates authentication headers for a SOAP request.
 * @param {string} password - The user's password.
 * @returns {Object} - Auth headers containing createdTime, expiresTime, nonce, and passwordDigest.
 */
function generateAuthHeaders(password) {
    const createdTime = moment().subtract(30, 'seconds').toISOString();
    const expiresTime = moment(createdTime).add(5, 'minutes').toISOString();
    const nonce = crypto.randomBytes(16).toString('base64');
    const passwordDigest = crypto.createHash('sha1').update(nonce + createdTime + password).digest('base64');
    
    return { createdTime, expiresTime, nonce, passwordDigest };
};

module.exports = { getCredentials, generateAuthHeaders };