/**
 * User Processor Lambda Function
 * 
 * This Lambda function processes messages from the SQS queue containing user verification events.
 * It writes the user data to DynamoDB in the same format as the existing user data.
 */

const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, PutCommand, GetCommand } = require('@aws-sdk/lib-dynamodb');

const client = new DynamoDBClient({ region: process.env.AWS_REGION });
const dynamodb = DynamoDBDocumentClient.from(client);

/**
 * Handler function for processing SQS messages
 * 
 * @param {Object} event - The SQS event
 * @param {Object} context - The Lambda context
 * @returns {Promise<void>}
 */
exports.handler = async (event, context) => {
  console.log('Processing SQS event:', JSON.stringify(event, null, 2));
  
  const tableName = process.env.TABLE_NAME;
  if (!tableName) {
    throw new Error('TABLE_NAME environment variable is required');
  }
  
  // Process each record in the SQS event
  const processPromises = event.Records.map(async (record) => {
    try {
      // Parse the message body
      const messageBody = JSON.parse(record.body);
      console.log('Processing message:', JSON.stringify(messageBody, null, 2));
      
      // Check if this is a user verification event
      const messageAttributes = record.messageAttributes || {};
      const eventType = messageAttributes.event_type?.stringValue;
      
      if (eventType === 'USER_VERIFIED') {
        await processUserVerification(messageBody, tableName);
      } else {
        console.log(`Ignoring message with event type: ${eventType}`);
      }
    } catch (error) {
      console.error('Error processing record:', error);
      // Don't throw the error to allow processing of other records
    }
  });
  
  // Wait for all records to be processed
  await Promise.all(processPromises);
  
  console.log('Finished processing SQS event');
};

/**
 * Process a user verification event
 * 
 * @param {Object} userData - The user data from the message
 * @param {string} tableName - The DynamoDB table name
 * @returns {Promise<void>}
 */
async function processUserVerification(userData, tableName) {
  const { username, email, sub } = userData;
  
  if (!username) {
    throw new Error('Username is required in the user data');
  }
  
  // Check if the user already exists in DynamoDB
  const existingUser = await getUserByUsername(username, tableName);
  if (existingUser) {
    console.log(`User ${username} already exists in DynamoDB, skipping`);
    return;
  }
  
  const timestamp = new Date().toISOString();
  
  // Create the user record in the same format as the existing user data
  const user = {
    username,
    email,
    sub,
    PK: `USER#${username}`,
    SK: `METADATA#${username}`,
    managerGroupIds: [],
    operatorGroupIds: [],
    workerGroupIds: [],
    type: 'USER',
    createdAt: timestamp,
    updatedAt: timestamp
  };
  
  console.log('Creating user in DynamoDB:', JSON.stringify(user, null, 2));
  
  // Write the user record to DynamoDB
  await dynamodb.send(new PutCommand({
    TableName: tableName,
    Item: user
  }));
  
  console.log(`User ${username} created successfully`);
}

/**
 * Get a user by username
 * 
 * @param {string} username - The username
 * @param {string} tableName - The DynamoDB table name
 * @returns {Promise<Object|null>} - The user or null if not found
 */
async function getUserByUsername(username, tableName) {
  try {
    const response = await dynamodb.send(new GetCommand({
      TableName: tableName,
      Key: {
        PK: `USER#${username}`,
        SK: `METADATA#${username}`
      }
    }));
    
    return response.Item;
  } catch (error) {
    console.error(`Error getting user ${username}:`, error);
    return null;
  }
}
