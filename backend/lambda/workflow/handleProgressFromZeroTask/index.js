const { DynamoDBClient, GetItemCommand, UpdateItemCommand } = require('@aws-sdk/client-dynamodb');
const dynamoClient = new DynamoDBClient({ region: 'eu-central-1' });

const tableName = process.env.TABLE_NAME;

exports.handler = async (event) => {

    // Parse the input event
    const newProgress = event.newImage.progress;
    const oldProgress = event.oldImage.progress;
    const activityId = event.newImage.id; // Assuming you have an 'id' field to identify the item
    const status = "IN_PROGRESS";

    // Check if progress is 100
    if (newProgress >= 0 && oldProgress === 0) {

        // Get current timestamp
        const currentTimestamp = new Date().toISOString();

        // Update the item in DynamoDB
        const params = {
            TableName: tableName,
            Key: {
                PK: {
                    "S": `ACTIVITY#${activityId}`
                },
                SK: {
                    "S": `METADATA#${activityId}`
                },
            },
            UpdateExpression: 'SET #status = :status, #actualTimeline = :actualTimeline',
            ExpressionAttributeNames: {
                '#status': 'status',
                '#actualTimeline': 'actualTimeline'
            },
            ExpressionAttributeValues: {
                ':status': {
                    "S": status
                },
                ':actualTimeline': {
                    "M": {
                        "start": { "S": currentTimestamp }
                    }
                }
            }
        };

        await dynamoClient.send(new UpdateItemCommand(params));

        return {
            statusCode: 200,
            body: JSON.stringify('Activity status updated to IN_PROGRESS')
        };
    } else {
        return {
            statusCode: 200,
            body: JSON.stringify('Progress is not 100, no action taken')
        };
    }
};
