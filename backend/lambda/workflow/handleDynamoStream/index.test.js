// index.test.js
const { SFNClient, StartExecutionCommand } = require("@aws-sdk/client-sfn");
const { unmarshall } = require("@aws-sdk/util-dynamodb");

// ---
// Mock the AWS SDK modules BEFORE importing the module under test.
jest.mock("@aws-sdk/client-sfn", () => {
  const sendMock = jest.fn();
  return {
    SFNClient: jest.fn(() => ({
      send: sendMock,
    })),
    StartExecutionCommand: jest.fn(),
    __sendMock: sendMock, // export sendMock for use in tests
  };
});

jest.mock("@aws-sdk/util-dynamodb", () => ({
  unmarshall: jest.fn(),
}));
// ---

// Now import the module under test.
const { handler } = require("./index");

describe("Lambda Handler", () => {
  // Access our send mock from the SFNClient mock module.
  const { __sendMock: sendMock } = require("@aws-sdk/client-sfn");

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    process.env.AWS_REGION = "us-east-1";
    process.env.STATE_MACHINE_ARN = "arn:aws:states:us-east-1:123456789012:stateMachine:TestStateMachine";
  });

  test("starts the state machine when progress changes", async () => {
    // Set up an event with a DynamoDB record where progress changed.
    const event = {
      Records: [
        {
          eventName: "MODIFY",
          dynamodb: {
            NewImage: { progress: { S: "in-progress" } },
            OldImage: { progress: { S: "not-started" } },
          },
        },
      ],
    };

    // Control the behavior of unmarshall to return a simple JSON.
    unmarshall.mockImplementation((image) => ({ progress: image.progress.S }));

    // Simulate a successful response from the state machine start.
    sendMock.mockResolvedValue({ executionArn: "executionArnValue" });

    // Call the handler.
    const result = await handler(event);

    // Build the expected JSON input to the state machine.
    const expectedInput = JSON.stringify({
      eventName: "MODIFY",
      newImage: { progress: "in-progress" },
      oldImage: { progress: "not-started" },
    });

    // Verify that StartExecutionCommand was created with the correct parameters.
    expect(StartExecutionCommand).toHaveBeenCalledWith({
      stateMachineArn: process.env.STATE_MACHINE_ARN,
      input: expectedInput,
    });

    // Verify that the send method was called (i.e. the state machine was started).
    expect(sendMock).toHaveBeenCalled();

    // Verify that the handler returns the expected summary.
    expect(result).toBe("Processed 1 records.");
  });

  test("does not start the state machine when progress is unchanged", async () => {
    // Create an event where the progress attribute remains the same.
    const event = {
      Records: [
        {
          eventName: "MODIFY",
          dynamodb: {
            NewImage: { progress: { S: "in-progress" } },
            OldImage: { progress: { S: "in-progress" } },
          },
        },
      ],
    };

    // Set unmarshall to return the simplified JSON.
    unmarshall.mockImplementation((image) => ({ progress: image.progress.S }));

    // Call the handler.
    const result = await handler(event);

    // Since progress did not change, neither StartExecutionCommand nor send should be called.
    expect(StartExecutionCommand).not.toHaveBeenCalled();
    expect(sendMock).not.toHaveBeenCalled();
    expect(result).toBe("Processed 1 records.");
  });

  test("handles non-DynamoDB events", async () => {
    // Create an event with a record that is not a DynamoDB record.
    const event = {
      Records: [
        {
          someKey: "someValue",
        },
      ],
    };

    // Call the handler.
    const result = await handler(event);

    // Since the record is not a DynamoDB event, the code should use the record as-is.
    // As a result, newImage and oldImage will be undefined and no state machine should be triggered.
    expect(StartExecutionCommand).not.toHaveBeenCalled();
    expect(sendMock).not.toHaveBeenCalled();
    expect(result).toBe("Processed 1 records.");
  });
});
