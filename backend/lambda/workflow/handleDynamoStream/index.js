// index.js
const { SFNClient, StartExecutionCommand } = require("@aws-sdk/client-sfn");
const { unmarshall } = require("@aws-sdk/util-dynamodb");

// Create an SFN client. The region is set from the Lambda environment variable.
const client = new SFNClient({ region: process.env.AWS_REGION });

exports.handler = async (event) => {
  console.log("Received event:", JSON.stringify(event, null, 2));

  // Process each record in the event
  for (const record of event.Records) {
    let standardJSON = {
      eventName: record.eventName,
      newImage: null,
      oldImage: null,
    };

    if (record.dynamodb) {
      if (record.dynamodb.NewImage) {
        standardJSON.newImage = unmarshall(record.dynamodb.NewImage);
      }
      if (record.dynamodb.OldImage) {
        standardJSON.oldImage = unmarshall(record.dynamodb.OldImage);
      }
    } else {
      // If it's not a DynamoDB event, use the record as is.
      standardJSON = record;
    }

    // Check if the 'progress' attribute has changed between the new and old images.
    const newProgress = standardJSON.newImage?.percentComplete;
    const oldProgress = standardJSON.oldImage?.percentComplete;

    if (newProgress !== oldProgress) {
      const params = {
        stateMachineArn: process.env.STATE_MACHINE_ARN,
        input: JSON.stringify(standardJSON),
      };

      try {
        // Create and send the StartExecutionCommand to start the state machine.
        const command = new StartExecutionCommand(params);
        const result = await client.send(command);
        console.log("State machine started:", result);
      } catch (error) {
        console.error("Error starting state machine:", error);
      }
    } else {
      console.log("Progress unchanged, state machine not triggered");
    }
  }

  return `Processed ${event.Records.length} records.`;
};
