const { DynamoDBClient, GetItemCommand, UpdateItemCommand } = require('@aws-sdk/client-dynamodb');
const { SQSClient, SendMessageCommand } = require('@aws-sdk/client-sqs');

const sqsClient = new SQSClient({ region: 'eu-central-1' });
const dynamoClient = new DynamoDBClient({ region: 'eu-central-1' });

const tableName = process.env.TABLE_NAME;
const queueUrl = process.env.ACTIVITY_UPDATE_QUEUE;

// Helper to extract a string attribute from a DynamoDB record attribute
function getStringAttr(attr) {
  return attr && attr.S ? attr.S : undefined;
}

// Helper to extract a boolean attribute
function getBoolAttr(attr) {
  return attr && attr.BOOL ? attr.BOOL : false;
}

exports.handler = async (event) => {

    // Parse the input event
    const percentComplete = event.newImage.percentComplete;
    const activityId = event.newImage.id;
    const projectObjectId = event.newImage.projectObjectId;

    console.log(`Activity ${activityId} is reported percentComplete:`, percentComplete);

    // Check if progress is 100
    if (percentComplete === 1) {

      // TODO: Implement additional udpateLogic

      // // Fetch the corresponding Project item from DynamoDB
      // const getProjectParams = {
      //   TableName: tableName,
      //   Key: {
      //     PK: { S: `PROJECT#${projectObjectId}` },
      //     SK: { S: `METADATA#${projectObjectId}` }
      //   },
      // };


      // // Check project updateStatus
      // const projectResult = await dynamoClient.send(new GetItemCommand(getProjectParams));
      // if (!projectResult.Item) {
      //   console.log(projectResult.Item);
      //   const message = `Project with ProjectObjectID ${projectObjectId} not found. Skipping.`;
      //   return {
      //     statusCode: 200,
      //     body: JSON.stringify(message)
      //   };
      // }

      // // Check if `autoClose` is true on the Project
      // const autoClose = getBoolAttr(projectResult.Item.autoClose);
      // var status = "COMPLETED";
      // if (autoClose) {
      //   status = "AUTO_CLOSED";
      // }

      // // Get current timestamp
      // const currentTimestamp = new Date().toISOString();

      // // Update the item in DynamoDB
      // const params = {
      //   TableName: tableName,
      //   Key: {
      //     PK: { S: `ACTIVITY#${activityId}` },
      //     SK: { S: `METADATA#${activityId}` },
      //   },
      //   UpdateExpression: 'SET #status = :status, #actualTimeline.#end = :end',
      //   ExpressionAttributeNames: {
      //     '#status': 'status',
      //     '#actualTimeline': 'actualTimeline',
      //     '#end': 'end',
      //   },
      //   ExpressionAttributeValues: {
      //     ':status': { S: status },
      //     ':end': { S: currentTimestamp },
      //   }
      // };
      
      // await dynamoClient.send(new UpdateItemCommand(params));

      // Publish an event to the SQS queue
      const sqsMessage = {
        action: "updateActivity",
        activityId: activityId,
        percentComplete: percentComplete
    };

    const sqsParams = {
        QueueUrl: queueUrl,
        MessageBody: JSON.stringify(sqsMessage),
    };

    await sqsClient.send(new SendMessageCommand(sqsParams));

      return {
        statusCode: 200,
        body: JSON.stringify('Activity status updated to COMPLETE')
      };
    } else {
      return {
        statusCode: 200,
        body: JSON.stringify('Progress is not 100, no action taken')
      };
    }
};
