# Lambda Function for Activity Progress Tracking

Create an AWS Lambda function in Node.js that processes activity progress updates and manages the closure of activities based on project settings. The function should interact with a DynamoDB table to retrieve and update data.

## Requirements

1. Use the AWS SDK v3 for DynamoDB operations.
2. The function should be triggered by changes to activity items in DynamoDB.
3. The DynamoDB table name should be retrieved from an environment variable.

## Input

The function receives an event object containing the new image of the updated DynamoDB item, including:
- `progress`: The current progress of the activity (number)
- `id`: The unique identifier of the activity (string)
- `workPackageId`: The ID of the associated work package (string)

## Process Flow

1. Check if the activity progress is 100%.
2. If progress is 100%:
   a. Retrieve the associated work package from DynamoDB.
   b. Retrieve the project associated with the work package.
   c. Check if the project has `autoClose` set to true.
   d. Update the activity status to either "COMPLETE" or "AUTO_CLOSED" based on the `autoClose` setting.
   e. Set the `actualTimeline.end` to the current timestamp.
3. If progress is not 100%, take no action.

## DynamoDB Schema

- Activities: 
  - PK: `ACTIVITY#<activityId>`
  - SK: `METADATA#<activityId>`
- Work Packages:
  - PK: `WOR<PERSON><PERSON><PERSON>KAGE#<workPackageId>`
  - SK: `METADATA#<workPackageId>`
- Projects:
  - PK: `PROJECT#<projectId>`
  - SK: `METADATA#<projectId>`

## Error Handling

- Handle cases where the work package or project is not found.
- Return appropriate status codes and messages for different scenarios.

## Helper Functions

Implement helper functions to:
1. Extract string attributes from DynamoDB items.
2. Extract boolean attributes from DynamoDB items.

## Output

The function should return an object with:
- `statusCode`: HTTP status code (200 for success)
- `body`: A JSON stringified message indicating the result of the operation

## Additional Notes

- Use async/await for asynchronous operations.
- Implement proper error handling and logging.
- Ensure the code is well-commented for clarity.
