// handler.test.js
const { mockClient } = require("aws-sdk-client-mock");
const { DynamoDBClient, GetItemCommand, UpdateItemCommand } = require("@aws-sdk/client-dynamodb");
const { handler } = require("./index");

// Create a mock for the DynamoDBClient
const ddbMock = mockClient(DynamoDBClient);

describe("Lambda Handler", () => {
  beforeEach(() => {
    ddbMock.reset();
    ddbMock.onAnyCommand().resolves({});
    process.env.TABLE_NAME = "TestTable";
  });

  test("should do nothing when progress is not 100", async () => {
    const event = {
      newImage: {
        progress: 50,
        id: "activity1"
      }
    };

    const result = await handler(event);

    expect(result.statusCode).toBe(200);
    expect(JSON.parse(result.body)).toEqual("Progress is not 100, no action taken");
    // No DynamoDB commands should be executed.
    expect(ddbMock.commandCalls(GetItemCommand).length).toBe(0);
    expect(ddbMock.commandCalls(UpdateItemCommand).length).toBe(0);
  });

  test("should skip processing when no workPackageId is provided", async () => {
    const event = {
      newImage: {
        progress: 100,
        id: "activity1"
        // workPackageId is missing
      }
    };

    const result = await handler(event);

    expect(result.statusCode).toBe(200);
    expect(JSON.parse(result.body)).toEqual("No workPackageId associated with this Activity. Skipping.");
    expect(ddbMock.commandCalls(GetItemCommand).length).toBe(0);
  });

  test("should skip processing when workpackage is not found", async () => {
    // Simulate GetItemCommand for the workpackage returning no item.
    ddbMock
      .on(GetItemCommand, {
        TableName: "TestTable",
        Key: {
          PK: { S: "WORKPACKAGE#wp1" },
          SK: { S: "METADATA#wp1" },
        },
      })
      .resolves({ Item: undefined });

    const event = {
      newImage: {
        progress: 100,
        id: "activity1",
        workPackageId: "wp1",
      },
    };

    const result = await handler(event);

    expect(ddbMock.commandCalls(GetItemCommand).length).toBe(1);
    expect(result.statusCode).toBe(200);
    expect(JSON.parse(result.body)).toEqual("Workpackage with ID wp1 not found. Skipping.");
  });

  test("should skip processing when project is not found", async () => {
    // Simulate a found workpackage with a projectId attribute.
    ddbMock
      .on(GetItemCommand, {
        TableName: "TestTable",
        Key: {
          PK: { S: "WORKPACKAGE#wp1" },
          SK: { S: "METADATA#wp1" },
        },
      })
      .resolves({ Item: { projectId: { S: "proj1" } } });
    // Simulate the project query returning no item.
    ddbMock
      .on(GetItemCommand, {
        TableName: "TestTable",
        Key: {
          PK: { S: "PROJECT#proj1" },
          SK: { S: "METADATA#proj1" },
        },
      })
      .resolves({ Item: undefined });

    const event = {
      newImage: {
        progress: 100,
        id: "activity1",
        workPackageId: "wp1",
      },
    };

    const result = await handler(event);

    // Two GetItemCommand calls should have been made (workpackage then project).
    expect(ddbMock.commandCalls(GetItemCommand).length).toBe(1);
    expect(result.statusCode).toBe(200);
    // The error message uses the raw attribute (an object) for projectId.
    expect(JSON.parse(result.body)).toEqual("Project with ID [object Object] not found. Skipping.");
  });

  test("should update activity status to COMPLETE when autoClose is false", async () => {
    // Simulate a found workpackage with a projectId.
    ddbMock
      .on(GetItemCommand, {
        TableName: "TestTable",
        Key: {
          PK: { S: "WORKPACKAGE#wp1" },
          SK: { S: "METADATA#wp1" },
        },
      })
      .resolves({ Item: { projectId: { S: "proj1" } } });
    // Simulate the project query returning autoClose false.
    ddbMock
      .on(GetItemCommand, {
        TableName: "TestTable",
        Key: {
          PK: { S: "PROJECT#proj1" },
          SK: { S: "METADATA#proj1" },
        },
      })
      .resolves({ Item: { autoClose: { BOOL: false } } });
    // Simulate a successful update.
    ddbMock.on(UpdateItemCommand).resolves({});

    const event = {
      newImage: {
        progress: 100,
        id: "activity1",
        workPackageId: "wp1",
      },
    };

    const result = await handler(event);

    expect(ddbMock.commandCalls(GetItemCommand).length).toBe(2);
    expect(ddbMock.commandCalls(UpdateItemCommand).length).toBe(1);
    expect(result.statusCode).toBe(200);
    expect(JSON.parse(result.body)).toEqual("Activity status updated to COMPLETE");

    // Verify the update command parameters.
    const updateCalls = ddbMock.commandCalls(UpdateItemCommand);
    expect(updateCalls.length).toBe(1);
    const updateInput = updateCalls[0].args[0].input;
    expect(updateInput.Key.PK.S).toEqual("ACTIVITY#activity1");
    expect(updateInput.Key.SK.S).toEqual("METADATA#activity1");
    expect(updateInput.ExpressionAttributeValues[":status"].S).toEqual("COMPLETE");
  });

  test("should update activity status to AUTO_CLOSED when autoClose is true", async () => {
    // Simulate a found workpackage with a projectId.
    ddbMock
      .on(GetItemCommand, {
        TableName: "TestTable",
        Key: {
          PK: { S: "WORKPACKAGE#wp1" },
          SK: { S: "METADATA#wp1" },
        },
      })
      .resolves({ Item: { projectId: { S: "proj1" } } });
    // Simulate the project query returning autoClose true.
    ddbMock
      .on(GetItemCommand, {
        TableName: "TestTable",
        Key: {
          PK: { S: "PROJECT#proj1" },
          SK: { S: "METADATA#proj1" },
        },
      })
      .resolves({ Item: { autoClose: { BOOL: true } } });
    // Simulate a successful update.
    ddbMock.on(UpdateItemCommand).resolves({});

    const event = {
      newImage: {
        progress: 100,
        id: "activity1",
        workPackageId: "wp1",
      },
    };

    const result = await handler(event);

    expect(ddbMock.commandCalls(GetItemCommand).length).toBe(2);
    expect(ddbMock.commandCalls(UpdateItemCommand).length).toBe(1);
    expect(result.statusCode).toBe(200);
    expect(JSON.parse(result.body)).toEqual("Activity status updated to COMPLETE");

    const updateCalls = ddbMock.commandCalls(UpdateItemCommand);
    expect(updateCalls.length).toBe(1);
    const updateInput = updateCalls[0].args[0].input;
    expect(updateInput.ExpressionAttributeValues[":status"].S).toEqual("AUTO_CLOSED");
  });
});
