/**
 * Contractor query operations
 */
const { isAdmin } = require('../utils/permissions');
const { getContractorById, getUsersForContractor } = require('./helpers');
const { queryByIndex } = require('../utils/dynamodb');

/**
 * Get a contractor by ID with permission checks
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} contractorId - Contractor ID to retrieve
 * @returns {Promise<Object>} - The contractor if found and user has permission
 * @throws {Error} - If contractor not found or requester doesn't have permission
 */
async function getContractor(dynamodb, tableName, userGroups, contractorId) {
    // Only admins can view contractor details
    if (!isAdmin(userGroups)) {
        throw new Error("Access denied: You do not have permission to view contractor details");
    }
    
    const contractor = await getContractorById(dynamodb, tableName, contractorId);
    if (!contractor) {
        throw new Error(`Contractor with ID ${contractorId} not found`);
    }
    
    // Fetch the contractor's users
    const users = await getUsersForContractor(dynamodb, tableName, contractorId);
    
    return {
        ...contractor,
        users
    };
}

/**
 * List contractors with pagination
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {number} limit - Maximum number of items to return
 * @param {string} nextToken - Pagination token
 * @returns {Promise<Object>} - Contractors with pagination info
 * @throws {Error} - If user doesn't have permission
 */
async function listContractors(dynamodb, tableName, userGroups, limit = 10, nextToken = null) {
    // Only admins can list contractors
    if (!isAdmin(userGroups)) {
        throw new Error("Access denied: You do not have permission to list contractors");
    }
    
    // Query contractors
    const options = {
        limit,
        nextToken
    };
    
    const result = await queryByIndex(
        dynamodb,
        tableName,
        'type-index',
        'type',
        'CONTRACTOR',
        options
    );
    
    // For each contractor, fetch their users
    const contractorsWithUsers = await Promise.all(
        result.items.map(async (contractor) => {
            const users = await getUsersForContractor(dynamodb, tableName, contractor.id);
            
            return {
                ...contractor,
                users
            };
        })
    );
    
    return {
        items: contractorsWithUsers,
        nextToken: result.nextToken,
        totalCount: result.count
    };
}

module.exports = {
    getContractor,
    listContractors
};
