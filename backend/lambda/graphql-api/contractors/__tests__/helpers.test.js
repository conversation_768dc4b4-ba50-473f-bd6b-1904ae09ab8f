/**
 * Unit tests for contractor helper functions
 */
const { 
    getContractorById, 
    getContractorByNumber, 
    getUsersForContractor, 
    getContractorsForUser, 
    getContractorForActivity 
} = require('../helpers');

// Mock the dynamodb utility functions
jest.mock('../../utils/dynamodb', () => ({
    getItem: jest.fn(),
    queryByIndex: jest.fn()
}));

// Mock the users helper functions
jest.mock('../../users/helpers', () => ({
    getUserByUsername: jest.fn()
}));

const { getItem, queryByIndex } = require('../../utils/dynamodb');
const { getUserByUsername } = require('../../users/helpers');

describe('Contractor Helpers', () => {
    // Mock DynamoDB client
    const mockDynamoDb = {
        send: jest.fn()
    };
    
    // Mock table name
    const tableName = 'test-table';
    
    beforeEach(() => {
        jest.clearAllMocks();
    });
    
    describe('getContractorById', () => {
        test('should call getItem with correct parameters', async () => {
            // Arrange
            const contractorId = 'test-id';
            const mockContractor = { 
                id: contractorId, 
                name: 'Test Contractor' 
            };
            getItem.mockResolvedValue(mockContractor);
            
            // Act
            const result = await getContractorById(mockDynamoDb, tableName, contractorId);
            
            // Assert
            expect(getItem).toHaveBeenCalledWith(
                mockDynamoDb, 
                tableName, 
                `CONTRACTOR#${contractorId}`, 
                `METADATA#${contractorId}`
            );
            expect(result).toEqual(mockContractor);
        });
        
        test('should return undefined when contractor not found', async () => {
            // Arrange
            const contractorId = 'non-existent-id';
            getItem.mockResolvedValue(undefined);
            
            // Act
            const result = await getContractorById(mockDynamoDb, tableName, contractorId);
            
            // Assert
            expect(getItem).toHaveBeenCalledWith(
                mockDynamoDb, 
                tableName, 
                `CONTRACTOR#${contractorId}`, 
                `METADATA#${contractorId}`
            );
            expect(result).toBeUndefined();
        });
    });
    
    describe('getContractorByNumber', () => {
        test('should query by index and find contractor by number', async () => {
            // Arrange
            const contractorNumber = 'C-123';
            const mockContractors = [
                { id: 'id-1', contractorNumber: 'C-123', name: 'Test Contractor 1' },
                { id: 'id-2', contractorNumber: 'C-456', name: 'Test Contractor 2' }
            ];
            queryByIndex.mockResolvedValue({
                items: mockContractors,
                count: 2
            });
            
            // Act
            const result = await getContractorByNumber(mockDynamoDb, tableName, contractorNumber);
            
            // Assert
            expect(queryByIndex).toHaveBeenCalledWith(
                mockDynamoDb,
                tableName,
                'type-index',
                'type',
                'CONTRACTOR'
            );
            expect(result).toEqual(mockContractors[0]);
        });
        
        test('should return undefined when contractor number not found', async () => {
            // Arrange
            const contractorNumber = 'non-existent-number';
            const mockContractors = [
                { id: 'id-1', contractorNumber: 'C-123', name: 'Test Contractor 1' },
                { id: 'id-2', contractorNumber: 'C-456', name: 'Test Contractor 2' }
            ];
            queryByIndex.mockResolvedValue({
                items: mockContractors,
                count: 2
            });
            
            // Act
            const result = await getContractorByNumber(mockDynamoDb, tableName, contractorNumber);
            
            // Assert
            expect(queryByIndex).toHaveBeenCalledWith(
                mockDynamoDb,
                tableName,
                'type-index',
                'type',
                'CONTRACTOR'
            );
            expect(result).toBeUndefined();
        });
        
        test('should return undefined when no contractors exist', async () => {
            // Arrange
            const contractorNumber = 'C-123';
            queryByIndex.mockResolvedValue({
                items: [],
                count: 0
            });
            
            // Act
            const result = await getContractorByNumber(mockDynamoDb, tableName, contractorNumber);
            
            // Assert
            expect(queryByIndex).toHaveBeenCalledWith(
                mockDynamoDb,
                tableName,
                'type-index',
                'type',
                'CONTRACTOR'
            );
            expect(result).toBeUndefined();
        });
    });
    
    describe('getUsersForContractor', () => {
        test('should return users for a contractor', async () => {
            // Arrange
            const contractorId = 'test-id';
            const mockRelationships = [
                { contractorId: 'test-id', username: 'user1' },
                { contractorId: 'test-id', username: 'user2' },
                { contractorId: 'other-id', username: 'user3' }
            ];
            const mockUsers = [
                { username: 'user1', name: 'User One' },
                { username: 'user2', name: 'User Two' }
            ];
            
            queryByIndex.mockResolvedValue({
                items: mockRelationships,
                count: 3
            });
            
            getUserByUsername
                .mockResolvedValueOnce(mockUsers[0])
                .mockResolvedValueOnce(mockUsers[1]);
            
            // Act
            const result = await getUsersForContractor(mockDynamoDb, tableName, contractorId);
            
            // Assert
            expect(queryByIndex).toHaveBeenCalledWith(
                mockDynamoDb,
                tableName,
                'type-index',
                'type',
                'USER_CONTRACTOR_RELATIONSHIP'
            );
            expect(getUserByUsername).toHaveBeenCalledTimes(2);
            expect(getUserByUsername).toHaveBeenCalledWith(mockDynamoDb, tableName, 'user1');
            expect(getUserByUsername).toHaveBeenCalledWith(mockDynamoDb, tableName, 'user2');
            expect(result).toEqual(mockUsers);
        });
        
        test('should filter out undefined users', async () => {
            // Arrange
            const contractorId = 'test-id';
            const mockRelationships = [
                { contractorId: 'test-id', username: 'user1' },
                { contractorId: 'test-id', username: 'user2' }
            ];
            const mockUser = { username: 'user1', name: 'User One' };
            
            queryByIndex.mockResolvedValue({
                items: mockRelationships,
                count: 2
            });
            
            getUserByUsername
                .mockResolvedValueOnce(mockUser)
                .mockResolvedValueOnce(undefined); // User2 not found
            
            // Act
            const result = await getUsersForContractor(mockDynamoDb, tableName, contractorId);
            
            // Assert
            expect(queryByIndex).toHaveBeenCalledWith(
                mockDynamoDb,
                tableName,
                'type-index',
                'type',
                'USER_CONTRACTOR_RELATIONSHIP'
            );
            expect(getUserByUsername).toHaveBeenCalledTimes(2);
            expect(result).toEqual([mockUser]);
        });
        
        test('should return empty array when no relationships found', async () => {
            // Arrange
            const contractorId = 'test-id';
            queryByIndex.mockResolvedValue({
                items: [],
                count: 0
            });
            
            // Act
            const result = await getUsersForContractor(mockDynamoDb, tableName, contractorId);
            
            // Assert
            expect(queryByIndex).toHaveBeenCalledWith(
                mockDynamoDb,
                tableName,
                'type-index',
                'type',
                'USER_CONTRACTOR_RELATIONSHIP'
            );
            expect(getUserByUsername).not.toHaveBeenCalled();
            expect(result).toEqual([]);
        });
    });
    
    describe('getContractorsForUser', () => {
        test('should return contractors for a user', async () => {
            // Arrange
            const username = 'test-user';
            const mockRelationships = [
                { username: 'test-user', contractorId: 'contractor1' },
                { username: 'test-user', contractorId: 'contractor2' },
                { username: 'other-user', contractorId: 'contractor3' }
            ];
            const mockContractors = [
                { id: 'contractor1', name: 'Contractor One' },
                { id: 'contractor2', name: 'Contractor Two' }
            ];
            
            queryByIndex.mockResolvedValue({
                items: mockRelationships,
                count: 3
            });
            
            // Mock getContractorById to be called twice with different contractor IDs
            getItem
                .mockResolvedValueOnce(mockContractors[0])
                .mockResolvedValueOnce(mockContractors[1]);
            
            // Act
            const result = await getContractorsForUser(mockDynamoDb, tableName, username);
            
            // Assert
            expect(queryByIndex).toHaveBeenCalledWith(
                mockDynamoDb,
                tableName,
                'type-index',
                'type',
                'USER_CONTRACTOR_RELATIONSHIP'
            );
            expect(getItem).toHaveBeenCalledTimes(2);
            expect(getItem).toHaveBeenCalledWith(
                mockDynamoDb, 
                tableName, 
                `CONTRACTOR#contractor1`, 
                `METADATA#contractor1`
            );
            expect(getItem).toHaveBeenCalledWith(
                mockDynamoDb, 
                tableName, 
                `CONTRACTOR#contractor2`, 
                `METADATA#contractor2`
            );
            expect(result).toEqual(mockContractors);
        });
        
        test('should filter out undefined contractors', async () => {
            // Arrange
            const username = 'test-user';
            const mockRelationships = [
                { username: 'test-user', contractorId: 'contractor1' },
                { username: 'test-user', contractorId: 'contractor2' }
            ];
            const mockContractor = { id: 'contractor1', name: 'Contractor One' };
            
            queryByIndex.mockResolvedValue({
                items: mockRelationships,
                count: 2
            });
            
            // Mock getContractorById to return one contractor and one undefined
            getItem
                .mockResolvedValueOnce(mockContractor)
                .mockResolvedValueOnce(undefined); // Contractor2 not found
            
            // Act
            const result = await getContractorsForUser(mockDynamoDb, tableName, username);
            
            // Assert
            expect(queryByIndex).toHaveBeenCalledWith(
                mockDynamoDb,
                tableName,
                'type-index',
                'type',
                'USER_CONTRACTOR_RELATIONSHIP'
            );
            expect(getItem).toHaveBeenCalledTimes(2);
            expect(result).toEqual([mockContractor]);
        });
        
        test('should return empty array when no relationships found', async () => {
            // Arrange
            const username = 'test-user';
            queryByIndex.mockResolvedValue({
                items: [],
                count: 0
            });
            
            // Act
            const result = await getContractorsForUser(mockDynamoDb, tableName, username);
            
            // Assert
            expect(queryByIndex).toHaveBeenCalledWith(
                mockDynamoDb,
                tableName,
                'type-index',
                'type',
                'USER_CONTRACTOR_RELATIONSHIP'
            );
            expect(getItem).not.toHaveBeenCalled();
            expect(result).toEqual([]);
        });
    });
    
    describe('getContractorForActivity', () => {
        test('should return contractor for activity with contractorId', async () => {
            // Arrange
            const activity = { 
                id: 'activity-1', 
                contractorId: 'contractor-1' 
            };
            const mockContractor = { 
                id: 'contractor-1', 
                name: 'Test Contractor' 
            };
            
            getItem.mockResolvedValue(mockContractor);
            
            // Act
            const result = await getContractorForActivity(mockDynamoDb, tableName, activity);
            
            // Assert
            expect(getItem).toHaveBeenCalledWith(
                mockDynamoDb, 
                tableName, 
                `CONTRACTOR#contractor-1`, 
                `METADATA#contractor-1`
            );
            expect(result).toEqual(mockContractor);
        });
        
        test('should return null when activity has no contractorId', async () => {
            // Arrange
            const activity = { id: 'activity-1' }; // No contractorId
            
            // Act
            const result = await getContractorForActivity(mockDynamoDb, tableName, activity);
            
            // Assert
            expect(getItem).not.toHaveBeenCalled();
            expect(result).toBeNull();
        });
        
        test('should return result of getContractorById when activity has contractorId', async () => {
            // Arrange
            const activity = { 
                id: 'activity-1', 
                contractorId: 'contractor-1' 
            };
            const mockContractor = null; // Contractor not found
            
            getItem.mockResolvedValue(mockContractor);
            
            // Act
            const result = await getContractorForActivity(mockDynamoDb, tableName, activity);
            
            // Assert
            expect(getItem).toHaveBeenCalledWith(
                mockDynamoDb, 
                tableName, 
                `CONTRACTOR#contractor-1`, 
                `METADATA#contractor-1`
            );
            expect(result).toBeNull();
        });
    });
});
