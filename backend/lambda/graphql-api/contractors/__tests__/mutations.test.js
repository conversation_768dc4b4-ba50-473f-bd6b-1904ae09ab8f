/**
 * Unit tests for contractor mutation operations
 */
const { 
    createContractor, 
    updateContractor, 
    deleteContractor, 
    addUserToContractor, 
    removeUserFromContractor 
} = require('../mutations');

const { isAdmin } = require('../../utils/permissions');
const { getContractorById, getContractorByNumber } = require('../helpers');
const { getUserByUsername } = require('../../users/helpers');
const { buildUpdateExpression, queryByIndex } = require('../../utils/dynamodb');

// Mock dependencies
jest.mock('../../utils/permissions', () => ({
    isAdmin: jest.fn()
}));

jest.mock('../helpers', () => ({
    getContractorById: jest.fn(),
    getContractorByNumber: jest.fn()
}));

jest.mock('../../users/helpers', () => ({
    getUserByUsername: jest.fn()
}));

jest.mock('../../utils/dynamodb', () => ({
    buildUpdateExpression: jest.fn(),
    queryByIndex: jest.fn()
}));

// Mock crypto for UUID generation
jest.mock('crypto', () => ({
    randomUUID: jest.fn(() => 'mocked-uuid')
}));

describe('Contractor Mutations', () => {
    // Mock DynamoDB client
    const mockDynamoDb = {
        send: jest.fn()
    };
    
    // Mock table name
    const tableName = 'test-table';
    
    // Mock user groups
    const adminGroups = ['admin'];
    const nonAdminGroups = ['User'];
    
    beforeEach(() => {
        jest.clearAllMocks();
        
        // Default mock for isAdmin
        isAdmin.mockImplementation(groups => groups.includes('admin'));
    });
    
    describe('createContractor', () => {
        test('should create a contractor when user is admin', async () => {
            // Arrange
            const contractorInput = {
                contractorNumber: 'C-123',
                name: 'Test Contractor',
                description: 'Test description',
                enabled: true
            };
            
            // Mock getContractorByNumber to return null (no existing contractor)
            getContractorByNumber.mockResolvedValue(null);
            
            // Mock DynamoDB PutCommand response
            mockDynamoDb.send.mockResolvedValue({});
            
            // Act
            const result = await createContractor(mockDynamoDb, tableName, adminGroups, contractorInput);
            
            // Assert
            expect(isAdmin).toHaveBeenCalledWith(adminGroups);
            expect(getContractorByNumber).toHaveBeenCalledWith(mockDynamoDb, tableName, 'C-123');
            expect(mockDynamoDb.send).toHaveBeenCalledTimes(1);
            
            // Verify the contractor object structure
            expect(result).toEqual(expect.objectContaining({
                id: 'mocked-uuid',
                contractorNumber: 'C-123',
                name: 'Test Contractor',
                description: 'Test description',
                enabled: true,
                PK: 'CONTRACTOR#mocked-uuid',
                SK: 'METADATA#mocked-uuid',
                type: 'CONTRACTOR',
                createdAt: expect.any(String),
                updatedAt: expect.any(String)
            }));
        });
        
        test('should throw error when user is not admin', async () => {
            // Arrange
            const contractorInput = {
                contractorNumber: 'C-123',
                name: 'Test Contractor'
            };
            
            // Act & Assert
            await expect(createContractor(
                mockDynamoDb, 
                tableName, 
                nonAdminGroups, 
                contractorInput
            )).rejects.toThrow('Access denied: You do not have permission to create contractors');
            
            expect(isAdmin).toHaveBeenCalledWith(nonAdminGroups);
            expect(getContractorByNumber).not.toHaveBeenCalled();
            expect(mockDynamoDb.send).not.toHaveBeenCalled();
        });
        
        test('should throw error when contractor number already exists', async () => {
            // Arrange
            const contractorInput = {
                contractorNumber: 'C-123',
                name: 'Test Contractor'
            };
            
            // Mock getContractorByNumber to return an existing contractor
            const existingContractor = {
                id: 'existing-id',
                contractorNumber: 'C-123',
                name: 'Existing Contractor'
            };
            getContractorByNumber.mockResolvedValue(existingContractor);
            
            // Act & Assert
            await expect(createContractor(
                mockDynamoDb, 
                tableName, 
                adminGroups, 
                contractorInput
            )).rejects.toThrow('Contractor with number C-123 already exists');
            
            expect(isAdmin).toHaveBeenCalledWith(adminGroups);
            expect(getContractorByNumber).toHaveBeenCalledWith(mockDynamoDb, tableName, 'C-123');
            expect(mockDynamoDb.send).not.toHaveBeenCalled();
        });
    });
    
    describe('updateContractor', () => {
        test('should update a contractor when user is admin', async () => {
            // Arrange
            const contractorId = 'test-id';
            const updateInput = {
                name: 'Updated Contractor Name',
                description: 'Updated description'
            };
            
            // Mock existing contractor
            const existingContractor = {
                id: contractorId,
                contractorNumber: 'C-123',
                name: 'Original Name',
                description: 'Original description'
            };
            
            // Mock updated contractor
            const updatedContractor = {
                ...existingContractor,
                ...updateInput,
                updatedAt: expect.any(String)
            };
            
            getContractorById.mockResolvedValue(existingContractor);
            
            // Mock buildUpdateExpression
            const mockUpdateExpression = {
                updateExpression: 'SET #name = :name, #description = :description, #updatedAt = :updatedAt',
                expressionAttributeNames: { '#name': 'name', '#description': 'description', '#updatedAt': 'updatedAt' },
                expressionAttributeValues: { 
                    ':name': 'Updated Contractor Name', 
                    ':description': 'Updated description',
                    ':updatedAt': expect.any(String)
                }
            };
            buildUpdateExpression.mockReturnValue(mockUpdateExpression);
            
            // Mock DynamoDB UpdateCommand response
            mockDynamoDb.send.mockResolvedValue({
                Attributes: updatedContractor
            });
            
            // Act
            const result = await updateContractor(mockDynamoDb, tableName, adminGroups, contractorId, updateInput);
            
            // Assert
            expect(isAdmin).toHaveBeenCalledWith(adminGroups);
            expect(getContractorById).toHaveBeenCalledWith(mockDynamoDb, tableName, contractorId);
            expect(buildUpdateExpression).toHaveBeenCalled();
            expect(mockDynamoDb.send).toHaveBeenCalledTimes(1);
            expect(result).toEqual(updatedContractor);
        });
        
        test('should throw error when user is not admin', async () => {
            // Arrange
            const contractorId = 'test-id';
            const updateInput = {
                name: 'Updated Name'
            };
            
            // Act & Assert
            await expect(updateContractor(
                mockDynamoDb, 
                tableName, 
                nonAdminGroups, 
                contractorId, 
                updateInput
            )).rejects.toThrow('Access denied: You do not have permission to update contractors');
            
            expect(isAdmin).toHaveBeenCalledWith(nonAdminGroups);
            expect(getContractorById).not.toHaveBeenCalled();
            expect(mockDynamoDb.send).not.toHaveBeenCalled();
        });
        
        test('should throw error when contractor not found', async () => {
            // Arrange
            const contractorId = 'non-existent-id';
            const updateInput = {
                name: 'Updated Name'
            };
            
            getContractorById.mockResolvedValue(undefined);
            
            // Act & Assert
            await expect(updateContractor(
                mockDynamoDb, 
                tableName, 
                adminGroups, 
                contractorId, 
                updateInput
            )).rejects.toThrow(`Contractor with ID ${contractorId} not found`);
            
            expect(isAdmin).toHaveBeenCalledWith(adminGroups);
            expect(getContractorById).toHaveBeenCalledWith(mockDynamoDb, tableName, contractorId);
            expect(mockDynamoDb.send).not.toHaveBeenCalled();
        });
        
        test('should throw error when changing to a duplicate contractor number', async () => {
            // Arrange
            const contractorId = 'test-id';
            const updateInput = {
                contractorNumber: 'C-456' // Different from original
            };
            
            // Mock existing contractor
            const existingContractor = {
                id: contractorId,
                contractorNumber: 'C-123',
                name: 'Original Name'
            };
            
            // Mock another contractor with the target number
            const duplicateContractor = {
                id: 'other-id',
                contractorNumber: 'C-456',
                name: 'Other Contractor'
            };
            
            getContractorById.mockResolvedValue(existingContractor);
            getContractorByNumber.mockResolvedValue(duplicateContractor);
            
            // Act & Assert
            await expect(updateContractor(
                mockDynamoDb, 
                tableName, 
                adminGroups, 
                contractorId, 
                updateInput
            )).rejects.toThrow('Contractor with number C-456 already exists');
            
            expect(isAdmin).toHaveBeenCalledWith(adminGroups);
            expect(getContractorById).toHaveBeenCalledWith(mockDynamoDb, tableName, contractorId);
            expect(getContractorByNumber).toHaveBeenCalledWith(mockDynamoDb, tableName, 'C-456');
            expect(mockDynamoDb.send).not.toHaveBeenCalled();
        });
        
    });
    
    describe('deleteContractor', () => {
        test('should delete a contractor when user is admin', async () => {
            // Arrange
            const contractorId = 'test-id';
            
            // Mock existing contractor
            const existingContractor = {
                id: contractorId,
                contractorNumber: 'C-123',
                name: 'Test Contractor'
            };
            
            getContractorById.mockResolvedValue(existingContractor);
            
            // Mock relationships query
            const mockRelationships = [
                { username: 'user1', contractorId: contractorId },
                { username: 'user2', contractorId: contractorId }
            ];
            
            queryByIndex.mockResolvedValue({
                items: mockRelationships,
                count: 2
            });
            
            // Mock DynamoDB DeleteCommand responses
            mockDynamoDb.send.mockResolvedValue({});
            
            // Act
            const result = await deleteContractor(mockDynamoDb, tableName, adminGroups, contractorId);
            
            // Assert
            expect(isAdmin).toHaveBeenCalledWith(adminGroups);
            expect(getContractorById).toHaveBeenCalledWith(mockDynamoDb, tableName, contractorId);
            expect(queryByIndex).toHaveBeenCalledWith(
                mockDynamoDb,
                tableName,
                'type-index',
                'type',
                'USER_CONTRACTOR_RELATIONSHIP'
            );
            
            // Should call send 3 times: once for contractor, twice for relationships
            expect(mockDynamoDb.send).toHaveBeenCalledTimes(3);
            expect(result).toEqual(contractorId);
        });
        
        test('should throw error when user is not admin', async () => {
            // Arrange
            const contractorId = 'test-id';
            
            // Act & Assert
            await expect(deleteContractor(
                mockDynamoDb, 
                tableName, 
                nonAdminGroups, 
                contractorId
            )).rejects.toThrow('Access denied: You do not have permission to delete contractors');
            
            expect(isAdmin).toHaveBeenCalledWith(nonAdminGroups);
            expect(getContractorById).not.toHaveBeenCalled();
            expect(mockDynamoDb.send).not.toHaveBeenCalled();
        });
        
        test('should throw error when contractor not found', async () => {
            // Arrange
            const contractorId = 'non-existent-id';
            
            getContractorById.mockResolvedValue(undefined);
            
            // Act & Assert
            await expect(deleteContractor(
                mockDynamoDb, 
                tableName, 
                adminGroups, 
                contractorId
            )).rejects.toThrow(`Contractor with ID ${contractorId} not found`);
            
            expect(isAdmin).toHaveBeenCalledWith(adminGroups);
            expect(getContractorById).toHaveBeenCalledWith(mockDynamoDb, tableName, contractorId);
            expect(mockDynamoDb.send).not.toHaveBeenCalled();
        });
        
        test('should handle case with no relationships', async () => {
            // Arrange
            const contractorId = 'test-id';
            
            // Mock existing contractor
            const existingContractor = {
                id: contractorId,
                contractorNumber: 'C-123',
                name: 'Test Contractor'
            };
            
            getContractorById.mockResolvedValue(existingContractor);
            
            // Mock empty relationships query
            queryByIndex.mockResolvedValue({
                items: [],
                count: 0
            });
            
            // Mock DynamoDB DeleteCommand response
            mockDynamoDb.send.mockResolvedValue({});
            
            // Act
            const result = await deleteContractor(mockDynamoDb, tableName, adminGroups, contractorId);
            
            // Assert
            expect(getContractorById).toHaveBeenCalledWith(mockDynamoDb, tableName, contractorId);
            expect(queryByIndex).toHaveBeenCalledWith(
                mockDynamoDb,
                tableName,
                'type-index',
                'type',
                'USER_CONTRACTOR_RELATIONSHIP'
            );
            
            // Should call send only once for the contractor
            expect(mockDynamoDb.send).toHaveBeenCalledTimes(1);
            expect(result).toEqual(contractorId);
        });
    });
    
    describe('addUserToContractor', () => {
        test('should add a user to a contractor when user is admin', async () => {
            // Arrange
            const username = 'test-user';
            const contractorId = 'test-id';
            
            // Mock user and contractor
            const mockUser = { username, name: 'Test User' };
            const mockContractor = { id: contractorId, name: 'Test Contractor' };
            
            getUserByUsername.mockResolvedValue(mockUser);
            getContractorById.mockResolvedValue(mockContractor);
            
            // Mock relationships query - no existing relationship
            queryByIndex.mockResolvedValue({
                items: [],
                count: 0
            });
            
            // Mock DynamoDB PutCommand response
            mockDynamoDb.send.mockResolvedValue({});
            
            // Act
            const result = await addUserToContractor(mockDynamoDb, tableName, adminGroups, username, contractorId);
            
            // Assert
            expect(isAdmin).toHaveBeenCalledWith(adminGroups);
            expect(getUserByUsername).toHaveBeenCalledWith(mockDynamoDb, tableName, username);
            expect(getContractorById).toHaveBeenCalledWith(mockDynamoDb, tableName, contractorId);
            expect(queryByIndex).toHaveBeenCalledWith(
                mockDynamoDb,
                tableName,
                'type-index',
                'type',
                'USER_CONTRACTOR_RELATIONSHIP'
            );
            expect(mockDynamoDb.send).toHaveBeenCalledTimes(1);
            expect(result).toEqual(mockUser);
        });
        
        test('should throw error when user is not admin', async () => {
            // Arrange
            const username = 'test-user';
            const contractorId = 'test-id';
            
            // Act & Assert
            await expect(addUserToContractor(
                mockDynamoDb, 
                tableName, 
                nonAdminGroups, 
                username, 
                contractorId
            )).rejects.toThrow('Access denied: You do not have permission to add users to contractors');
            
            expect(isAdmin).toHaveBeenCalledWith(nonAdminGroups);
            expect(getUserByUsername).not.toHaveBeenCalled();
            expect(getContractorById).not.toHaveBeenCalled();
            expect(mockDynamoDb.send).not.toHaveBeenCalled();
        });
        
        test('should throw error when user not found', async () => {
            // Arrange
            const username = 'non-existent-user';
            const contractorId = 'test-id';
            
            getUserByUsername.mockResolvedValue(undefined);
            
            // Act & Assert
            await expect(addUserToContractor(
                mockDynamoDb, 
                tableName, 
                adminGroups, 
                username, 
                contractorId
            )).rejects.toThrow(`User with username ${username} not found`);
            
            expect(isAdmin).toHaveBeenCalledWith(adminGroups);
            expect(getUserByUsername).toHaveBeenCalledWith(mockDynamoDb, tableName, username);
            expect(getContractorById).not.toHaveBeenCalled();
            expect(mockDynamoDb.send).not.toHaveBeenCalled();
        });
        
        test('should throw error when contractor not found', async () => {
            // Arrange
            const username = 'test-user';
            const contractorId = 'non-existent-id';
            
            // Mock user exists but contractor doesn't
            const mockUser = { username, name: 'Test User' };
            getUserByUsername.mockResolvedValue(mockUser);
            getContractorById.mockResolvedValue(undefined);
            
            // Act & Assert
            await expect(addUserToContractor(
                mockDynamoDb, 
                tableName, 
                adminGroups, 
                username, 
                contractorId
            )).rejects.toThrow(`Contractor with ID ${contractorId} not found`);
            
            expect(isAdmin).toHaveBeenCalledWith(adminGroups);
            expect(getUserByUsername).toHaveBeenCalledWith(mockDynamoDb, tableName, username);
            expect(getContractorById).toHaveBeenCalledWith(mockDynamoDb, tableName, contractorId);
            expect(mockDynamoDb.send).not.toHaveBeenCalled();
        });
        
        test('should not create duplicate relationship', async () => {
            // Arrange
            const username = 'test-user';
            const contractorId = 'test-id';
            
            // Mock user and contractor
            const mockUser = { username, name: 'Test User' };
            const mockContractor = { id: contractorId, name: 'Test Contractor' };
            
            getUserByUsername.mockResolvedValue(mockUser);
            getContractorById.mockResolvedValue(mockContractor);
            
            // Mock relationships query - existing relationship found
            const existingRelationship = {
                username,
                contractorId,
                type: 'USER_CONTRACTOR_RELATIONSHIP'
            };
            queryByIndex.mockResolvedValue({
                items: [existingRelationship],
                count: 1
            });
            
            // Act
            const result = await addUserToContractor(mockDynamoDb, tableName, adminGroups, username, contractorId);
            
            // Assert
            expect(getUserByUsername).toHaveBeenCalledWith(mockDynamoDb, tableName, username);
            expect(getContractorById).toHaveBeenCalledWith(mockDynamoDb, tableName, contractorId);
            expect(queryByIndex).toHaveBeenCalledWith(
                mockDynamoDb,
                tableName,
                'type-index',
                'type',
                'USER_CONTRACTOR_RELATIONSHIP'
            );
            // Should not call send since relationship already exists
            expect(mockDynamoDb.send).not.toHaveBeenCalled();
            expect(result).toEqual(mockUser);
        });
    });
    
    describe('removeUserFromContractor', () => {
        test('should remove a user from a contractor when user is admin', async () => {
            // Arrange
            const username = 'test-user';
            const contractorId = 'test-id';
            
            // Mock user and contractor
            const mockUser = { username, name: 'Test User' };
            const mockContractor = { id: contractorId, name: 'Test Contractor' };
            
            getUserByUsername.mockResolvedValue(mockUser);
            getContractorById.mockResolvedValue(mockContractor);
            
            // Mock DynamoDB DeleteCommand response
            mockDynamoDb.send.mockResolvedValue({});
            
            // Act
            const result = await removeUserFromContractor(mockDynamoDb, tableName, adminGroups, username, contractorId);
            
            // Assert
            expect(isAdmin).toHaveBeenCalledWith(adminGroups);
            expect(getUserByUsername).toHaveBeenCalledWith(mockDynamoDb, tableName, username);
            expect(getContractorById).toHaveBeenCalledWith(mockDynamoDb, tableName, contractorId);
            expect(mockDynamoDb.send).toHaveBeenCalledTimes(1);
            expect(result).toEqual(mockUser);
        });
        
        test('should throw error when user is not admin', async () => {
            // Arrange
            const username = 'test-user';
            const contractorId = 'test-id';
            
            // Act & Assert
            await expect(removeUserFromContractor(
                mockDynamoDb, 
                tableName, 
                nonAdminGroups, 
                username, 
                contractorId
            )).rejects.toThrow('Access denied: You do not have permission to remove users from contractors');
            
            expect(isAdmin).toHaveBeenCalledWith(nonAdminGroups);
            expect(getUserByUsername).not.toHaveBeenCalled();
            expect(getContractorById).not.toHaveBeenCalled();
            expect(mockDynamoDb.send).not.toHaveBeenCalled();
        });
        
        test('should throw error when user not found', async () => {
            // Arrange
            const username = 'non-existent-user';
            const contractorId = 'test-id';
            
            getUserByUsername.mockResolvedValue(undefined);
            
            // Act & Assert
            await expect(removeUserFromContractor(
                mockDynamoDb, 
                tableName, 
                adminGroups, 
                username, 
                contractorId
            )).rejects.toThrow(`User with username ${username} not found`);
            
            expect(isAdmin).toHaveBeenCalledWith(adminGroups);
            expect(getUserByUsername).toHaveBeenCalledWith(mockDynamoDb, tableName, username);
            expect(getContractorById).not.toHaveBeenCalled();
            expect(mockDynamoDb.send).not.toHaveBeenCalled();
        });
        
        test('should throw error when contractor not found', async () => {
            // Arrange
            const username = 'test-user';
            const contractorId = 'non-existent-id';
            
            // Mock user exists but contractor doesn't
            const mockUser = { username, name: 'Test User' };
            getUserByUsername.mockResolvedValue(mockUser);
            getContractorById.mockResolvedValue(undefined);
            
            // Act & Assert
            await expect(removeUserFromContractor(
                mockDynamoDb, 
                tableName, 
                adminGroups, 
                username, 
                contractorId
            )).rejects.toThrow(`Contractor with ID ${contractorId} not found`);
            
            expect(isAdmin).toHaveBeenCalledWith(adminGroups);
            expect(getUserByUsername).toHaveBeenCalledWith(mockDynamoDb, tableName, username);
            expect(getContractorById).toHaveBeenCalledWith(mockDynamoDb, tableName, contractorId);
            expect(mockDynamoDb.send).not.toHaveBeenCalled();
        });
    });
});
