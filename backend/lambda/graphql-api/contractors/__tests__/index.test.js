/**
 * Unit tests for contractors module main exports
 */
const contractorsModule = require('../index');

// Mock the imported modules
jest.mock('../queries', () => ({
    getContractor: jest.fn(),
    listContractors: jest.fn()
}));

jest.mock('../mutations', () => ({
    createContractor: jest.fn(),
    updateContractor: jest.fn(),
    deleteContractor: jest.fn(),
    addUserToContractor: jest.fn(),
    removeUserFromContractor: jest.fn()
}));

describe('Contractors Module', () => {
    test('should export all required functions', () => {
        // Assert
        expect(contractorsModule).toHaveProperty('getContractor');
        expect(contractorsModule).toHaveProperty('listContractors');
        expect(contractorsModule).toHaveProperty('createContractor');
        expect(contractorsModule).toHaveProperty('updateContractor');
        expect(contractorsModule).toHaveProperty('deleteContractor');
        expect(contractorsModule).toHaveProperty('addUserToContractor');
        expect(contractorsModule).toHaveProperty('removeUserFromContractor');
    });

    test('should export the correct functions from queries and mutations', () => {
        // Import the original modules
        const { getContractor, listContractors } = require('../queries');
        const { 
            createContractor, 
            updateContractor, 
            deleteContractor, 
            addUserToContractor, 
            removeUserFromContractor 
        } = require('../mutations');
        
        // Assert that the exports match the original functions
        expect(contractorsModule.getContractor).toBe(getContractor);
        expect(contractorsModule.listContractors).toBe(listContractors);
        expect(contractorsModule.createContractor).toBe(createContractor);
        expect(contractorsModule.updateContractor).toBe(updateContractor);
        expect(contractorsModule.deleteContractor).toBe(deleteContractor);
        expect(contractorsModule.addUserToContractor).toBe(addUserToContractor);
        expect(contractorsModule.removeUserFromContractor).toBe(removeUserFromContractor);
    });
});
