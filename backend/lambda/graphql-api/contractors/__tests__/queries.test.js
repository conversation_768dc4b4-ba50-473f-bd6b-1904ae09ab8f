/**
 * Unit tests for contractor query operations
 */
const { getContractor, listContractors } = require('../queries');
const { isAdmin } = require('../../utils/permissions');
const { getContractorById, getUsersForContractor } = require('../helpers');
const { queryByIndex } = require('../../utils/dynamodb');

// Mock dependencies
jest.mock('../../utils/permissions', () => ({
    isAdmin: jest.fn()
}));

jest.mock('../helpers', () => ({
    getContractorById: jest.fn(),
    getUsersForContractor: jest.fn()
}));

jest.mock('../../utils/dynamodb', () => ({
    queryByIndex: jest.fn()
}));

describe('Contractor Queries', () => {
    // Mock DynamoDB client
    const mockDynamoDb = {
        send: jest.fn()
    };
    
    // Mock table name
    const tableName = 'test-table';
    
    // Mock user groups
    const adminGroups = ['admin'];
    const nonAdminGroups = ['User'];
    
    beforeEach(() => {
        jest.clearAllMocks();
    });
    
    describe('getContractor', () => {
        test('should return contractor with users when user is admin', async () => {
            // Arrange
            const contractorId = 'test-id';
            const mockContractor = { 
                id: contractorId, 
                name: 'Test Contractor',
                contractorNumber: 'C-123'
            };
            const mockUsers = [
                { username: 'user1', name: 'User One' },
                { username: 'user2', name: 'User Two' }
            ];
            
            isAdmin.mockReturnValue(true);
            getContractorById.mockResolvedValue(mockContractor);
            getUsersForContractor.mockResolvedValue(mockUsers);
            
            // Act
            const result = await getContractor(mockDynamoDb, tableName, adminGroups, contractorId);
            
            // Assert
            expect(isAdmin).toHaveBeenCalledWith(adminGroups);
            expect(getContractorById).toHaveBeenCalledWith(mockDynamoDb, tableName, contractorId);
            expect(getUsersForContractor).toHaveBeenCalledWith(mockDynamoDb, tableName, contractorId);
            expect(result).toEqual({
                ...mockContractor,
                users: mockUsers
            });
        });
        
        test('should throw error when user is not admin', async () => {
            // Arrange
            const contractorId = 'test-id';
            isAdmin.mockReturnValue(false);
            
            // Act & Assert
            await expect(getContractor(
                mockDynamoDb, 
                tableName, 
                nonAdminGroups, 
                contractorId
            )).rejects.toThrow('Access denied: You do not have permission to view contractor details');
            
            expect(isAdmin).toHaveBeenCalledWith(nonAdminGroups);
            expect(getContractorById).not.toHaveBeenCalled();
            expect(getUsersForContractor).not.toHaveBeenCalled();
        });
        
        test('should throw error when contractor not found', async () => {
            // Arrange
            const contractorId = 'non-existent-id';
            isAdmin.mockReturnValue(true);
            getContractorById.mockResolvedValue(undefined);
            
            // Act & Assert
            await expect(getContractor(
                mockDynamoDb, 
                tableName, 
                adminGroups, 
                contractorId
            )).rejects.toThrow(`Contractor with ID ${contractorId} not found`);
            
            expect(isAdmin).toHaveBeenCalledWith(adminGroups);
            expect(getContractorById).toHaveBeenCalledWith(mockDynamoDb, tableName, contractorId);
            expect(getUsersForContractor).not.toHaveBeenCalled();
        });
    });
    
    describe('listContractors', () => {
        test('should return contractors with users when user is admin', async () => {
            // Arrange
            const mockContractors = [
                { id: 'id-1', contractorNumber: 'C-123', name: 'Test Contractor 1' },
                { id: 'id-2', contractorNumber: 'C-456', name: 'Test Contractor 2' }
            ];
            const mockUsers1 = [{ username: 'user1', name: 'User One' }];
            const mockUsers2 = [{ username: 'user2', name: 'User Two' }];
            
            isAdmin.mockReturnValue(true);
            queryByIndex.mockResolvedValue({
                items: mockContractors,
                count: 2,
                nextToken: null
            });
            getUsersForContractor
                .mockResolvedValueOnce(mockUsers1)
                .mockResolvedValueOnce(mockUsers2);
            
            // Act
            const result = await listContractors(mockDynamoDb, tableName, adminGroups);
            
            // Assert
            expect(isAdmin).toHaveBeenCalledWith(adminGroups);
            expect(queryByIndex).toHaveBeenCalledWith(
                mockDynamoDb,
                tableName,
                'type-index',
                'type',
                'CONTRACTOR',
                expect.objectContaining({ limit: 10 })
            );
            expect(getUsersForContractor).toHaveBeenCalledTimes(2);
            expect(getUsersForContractor).toHaveBeenCalledWith(mockDynamoDb, tableName, 'id-1');
            expect(getUsersForContractor).toHaveBeenCalledWith(mockDynamoDb, tableName, 'id-2');
            
            expect(result).toEqual({
                items: [
                    { ...mockContractors[0], users: mockUsers1 },
                    { ...mockContractors[1], users: mockUsers2 }
                ],
                nextToken: null,
                totalCount: 2
            });
        });
        
        test('should throw error when user is not admin', async () => {
            // Arrange
            isAdmin.mockReturnValue(false);
            
            // Act & Assert
            await expect(listContractors(
                mockDynamoDb, 
                tableName, 
                nonAdminGroups
            )).rejects.toThrow('Access denied: You do not have permission to list contractors');
            
            expect(isAdmin).toHaveBeenCalledWith(nonAdminGroups);
            expect(queryByIndex).not.toHaveBeenCalled();
            expect(getUsersForContractor).not.toHaveBeenCalled();
        });
        
        test('should handle pagination parameters', async () => {
            // Arrange
            const limit = 5;
            const nextToken = JSON.stringify({ lastKey: 'some-key' });
            const mockContractors = [
                { id: 'id-3', contractorNumber: 'C-789', name: 'Test Contractor 3' }
            ];
            
            isAdmin.mockReturnValue(true);
            queryByIndex.mockResolvedValue({
                items: mockContractors,
                count: 1,
                nextToken: JSON.stringify({ lastKey: 'next-key' })
            });
            getUsersForContractor.mockResolvedValue([]);
            
            // Act
            const result = await listContractors(mockDynamoDb, tableName, adminGroups, limit, nextToken);
            
            // Assert
            expect(isAdmin).toHaveBeenCalledWith(adminGroups);
            expect(queryByIndex).toHaveBeenCalledWith(
                mockDynamoDb,
                tableName,
                'type-index',
                'type',
                'CONTRACTOR',
                expect.objectContaining({ 
                    limit: limit,
                    nextToken: nextToken
                })
            );
            
            expect(result).toEqual({
                items: [{ ...mockContractors[0], users: [] }],
                nextToken: JSON.stringify({ lastKey: 'next-key' }),
                totalCount: 1
            });
        });
        
        test('should return empty array when no contractors found', async () => {
            // Arrange
            isAdmin.mockReturnValue(true);
            queryByIndex.mockResolvedValue({
                items: [],
                count: 0,
                nextToken: null
            });
            
            // Act
            const result = await listContractors(mockDynamoDb, tableName, adminGroups);
            
            // Assert
            expect(isAdmin).toHaveBeenCalledWith(adminGroups);
            expect(queryByIndex).toHaveBeenCalledWith(
                mockDynamoDb,
                tableName,
                'type-index',
                'type',
                'CONTRACTOR',
                expect.objectContaining({ limit: 10 })
            );
            expect(getUsersForContractor).not.toHaveBeenCalled();
            
            expect(result).toEqual({
                items: [],
                nextToken: null,
                totalCount: 0
            });
        });
    });
});
