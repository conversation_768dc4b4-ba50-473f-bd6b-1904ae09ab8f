/**
 * Internal helper functions for contractor operations
 */
const { getItem } = require('../utils/dynamodb');

/**
 * Get a contractor by ID
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} contractorId - Contractor ID
 * @returns {Promise<Object>} - The contractor or undefined if not found
 */
async function getContractorById(dynamodb, tableName, contractorId) {
    return await getItem(dynamodb, tableName, `CONTRACTOR#${contractorId}`, `METADATA#${contractorId}`);
}

/**
 * Get a contractor by contractor number
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} contractorNumber - Contractor number
 * @returns {Promise<Object>} - The contractor or undefined if not found
 */
async function getContractorByNumber(dynamodb, tableName, contractorNumber) {
    // This would require a GSI in production
    // For now, we'll scan the table (not efficient for production)
    const { queryByIndex } = require('../utils/dynamodb');
    
    const result = await queryByIndex(
        dynamodb,
        tableName,
        'type-index',
        'type',
        'CONTRACTOR'
    );
    
    return result.items.find(contractor => contractor.contractorNumber === contractorNumber);
}

/**
 * Get users for a contractor
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} contractorId - Contractor ID
 * @returns {Promise<Array<Object>>} - Array of users
 */
async function getUsersForContractor(dynamodb, tableName, contractorId) {
    const { queryByIndex } = require('../utils/dynamodb');
    const { getUserByUsername } = require('../users/helpers');
    
    // Query for user-contractor relationships
    // In production, you'd use a GSI for this
    const result = await queryByIndex(
        dynamodb,
        tableName,
        'type-index',
        'type',
        'USER_CONTRACTOR_RELATIONSHIP'
    );
    
    // Filter relationships for this contractor
    const relationships = result.items.filter(rel => rel.contractorId === contractorId);
    
    // Get the users
    const userPromises = relationships.map(rel => getUserByUsername(dynamodb, tableName, rel.username));
    const users = await Promise.all(userPromises);
    
    // Filter out any undefined results (in case a user was deleted)
    return users.filter(user => user !== undefined);
}

/**
 * Get contractors for a user
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} username - Username
 * @returns {Promise<Array<Object>>} - Array of contractors
 */
async function getContractorsForUser(dynamodb, tableName, username) {
    const { queryByIndex } = require('../utils/dynamodb');
    
    // Query for user-contractor relationships
    // In production, you'd use a GSI for this
    const result = await queryByIndex(
        dynamodb,
        tableName,
        'type-index',
        'type',
        'USER_CONTRACTOR_RELATIONSHIP'
    );
    
    // Filter relationships for this user
    const relationships = result.items.filter(rel => rel.username === username);
    
    // Get the contractors
    const contractorPromises = relationships.map(rel => getContractorById(dynamodb, tableName, rel.contractorId));
    const contractors = await Promise.all(contractorPromises);
    
    // Filter out any undefined results (in case a contractor was deleted)
    return contractors.filter(contractor => contractor !== undefined);
}

/**
 * Get contractor for an activity
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Object} activity - The activity object
 * @returns {Promise<Object>} - The contractor or null if not found
 */
async function getContractorForActivity(dynamodb, tableName, activity) {
    if (!activity.contractorId) {
        return null;
    }
    return await getContractorById(dynamodb, tableName, activity.contractorId);
}

module.exports = {
    getContractorById,
    getContractorByNumber,
    getUsersForContractor,
    getContractorsForUser,
    getContractorForActivity
};
