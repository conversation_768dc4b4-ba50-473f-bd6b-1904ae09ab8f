/**
 * Contractor mutation operations
 */
const { PutCommand, UpdateCommand, DeleteCommand } = require('@aws-sdk/lib-dynamodb');
const { randomUUID } = require('crypto');
const { isAdmin } = require('../utils/permissions');
const { getContractorById, getContractorByNumber } = require('./helpers');
const { getUserByUsername } = require('../users/helpers');
const { buildUpdateExpression } = require('../utils/dynamodb');

/**
 * Create a new contractor
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {Object} input - Contractor input data
 * @returns {Promise<Object>} - The created contractor
 * @throws {Error} - If user doesn't have permission or contractor number already exists
 */
async function createContractor(dynamodb, tableName, userGroups, input) {
    // Only admins can create contractors
    if (!isAdmin(userGroups)) {
        throw new Error("Access denied: You do not have permission to create contractors");
    }
    
    // Check if contractor number already exists
    const existingContractor = await getContractorByNumber(dynamodb, tableName, input.contractorNumber);
    if (existingContractor) {
        throw new Error(`Contractor with number ${input.contractorNumber} already exists`);
    }
    
    const timestamp = new Date().toISOString();
    const contractorId = randomUUID();
    
    const contractor = {
        ...input,
        id: contractorId,
        PK: `CONTRACTOR#${contractorId}`,
        SK: `METADATA#${contractorId}`,
        type: 'CONTRACTOR',
        createdAt: timestamp,
        updatedAt: timestamp
    };
    
    await dynamodb.send(new PutCommand({
        TableName: tableName,
        Item: contractor
    }));
    
    return contractor;
}

/**
 * Update a contractor
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} contractorId - Contractor ID to update
 * @param {Object} input - Updated contractor data
 * @returns {Promise<Object>} - The updated contractor
 * @throws {Error} - If user doesn't have permission or contractor doesn't exist
 */
async function updateContractor(dynamodb, tableName, userGroups, contractorId, input) {
    // Only admins can update contractors
    if (!isAdmin(userGroups)) {
        throw new Error("Access denied: You do not have permission to update contractors");
    }
    
    // Check if contractor exists
    const existingContractor = await getContractorById(dynamodb, tableName, contractorId);
    if (!existingContractor) {
        throw new Error(`Contractor with ID ${contractorId} not found`);
    }
    
    // If contractor number is being changed, check if the new number already exists
    if (input.contractorNumber && input.contractorNumber !== existingContractor.contractorNumber) {
        const contractorWithSameNumber = await getContractorByNumber(dynamodb, tableName, input.contractorNumber);
        if (contractorWithSameNumber && contractorWithSameNumber.id !== contractorId) {
            throw new Error(`Contractor with number ${input.contractorNumber} already exists`);
        }
    }
    
    // Build update expression
    const updateData = {
        ...input,
        updatedAt: new Date().toISOString()
    };
    
    const { updateExpression, expressionAttributeNames, expressionAttributeValues } = buildUpdateExpression(updateData);
    
    const params = {
        TableName: tableName,
        Key: { PK: `CONTRACTOR#${contractorId}`, SK: `METADATA#${contractorId}` },
        UpdateExpression: updateExpression,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributeValues,
        ReturnValues: 'ALL_NEW'
    };
    
    const response = await dynamodb.send(new UpdateCommand(params));
    return response.Attributes;
}

/**
 * Delete a contractor
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} contractorId - Contractor ID to delete
 * @returns {Promise<string>} - The deleted contractor ID
 * @throws {Error} - If user doesn't have permission or contractor doesn't exist
 */
async function deleteContractor(dynamodb, tableName, userGroups, contractorId) {
    // Only admins can delete contractors
    if (!isAdmin(userGroups)) {
        throw new Error("Access denied: You do not have permission to delete contractors");
    }
    
    // Check if contractor exists
    const existingContractor = await getContractorById(dynamodb, tableName, contractorId);
    if (!existingContractor) {
        throw new Error(`Contractor with ID ${contractorId} not found`);
    }
    
    // Delete the contractor
    await dynamodb.send(new DeleteCommand({
        TableName: tableName,
        Key: { PK: `CONTRACTOR#${contractorId}`, SK: `METADATA#${contractorId}` }
    }));
    
    // Also delete all user-contractor relationships for this contractor
    // In a production system, you'd use a transaction or batch operation
    // For now, we'll query and delete individually
    const { queryByIndex } = require('../utils/dynamodb');
    
    const result = await queryByIndex(
        dynamodb,
        tableName,
        'type-index',
        'type',
        'USER_CONTRACTOR_RELATIONSHIP'
    );
    
    const relationships = result.items.filter(rel => rel.contractorId === contractorId);
    
    for (const rel of relationships) {
        const relationshipId = `${rel.username}:${contractorId}`;
        await dynamodb.send(new DeleteCommand({
            TableName: tableName,
            Key: { PK: `USER_CONTRACTOR#${relationshipId}`, SK: `METADATA#${relationshipId}` }
        }));
    }
    
    return contractorId;
}

/**
 * Add a user to a contractor
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} username - Username to add to contractor
 * @param {string} contractorId - Contractor ID to add user to
 * @returns {Promise<Object>} - The updated user
 * @throws {Error} - If user doesn't have permission, user or contractor doesn't exist
 */
async function addUserToContractor(dynamodb, tableName, userGroups, username, contractorId) {
    // Only admins can add users to contractors
    if (!isAdmin(userGroups)) {
        throw new Error("Access denied: You do not have permission to add users to contractors");
    }
    
    // Check if user exists
    const user = await getUserByUsername(dynamodb, tableName, username);
    if (!user) {
        throw new Error(`User with username ${username} not found`);
    }
    
    // Check if contractor exists
    const contractor = await getContractorById(dynamodb, tableName, contractorId);
    if (!contractor) {
        throw new Error(`Contractor with ID ${contractorId} not found`);
    }
    
    // Check if relationship already exists
    const { queryByIndex } = require('../utils/dynamodb');
    
    const result = await queryByIndex(
        dynamodb,
        tableName,
        'type-index',
        'type',
        'USER_CONTRACTOR_RELATIONSHIP'
    );
    
    const existingRelationship = result.items.find(
        rel => rel.username === username && rel.contractorId === contractorId
    );
    
    if (existingRelationship) {
        // User already associated with this contractor
        return user;
    }
    
    // Create the user-contractor relationship
    const relationshipId = `${username}:${contractorId}`;
    await dynamodb.send(new PutCommand({
        TableName: tableName,
        Item: {
            PK: `USER_CONTRACTOR#${relationshipId}`,
            SK: `METADATA#${relationshipId}`,
            username: username,
            contractorId: contractorId,
            type: 'USER_CONTRACTOR_RELATIONSHIP',
            createdAt: new Date().toISOString()
        }
    }));
    
    // Update the user record to include the contractor ID in an array
    // This is optional and depends on your data model
    // For now, we'll just return the user
    return user;
}

/**
 * Remove a user from a contractor
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} username - Username to remove from contractor
 * @param {string} contractorId - Contractor ID to remove user from
 * @returns {Promise<Object>} - The updated user
 * @throws {Error} - If user doesn't have permission, user or contractor doesn't exist
 */
async function removeUserFromContractor(dynamodb, tableName, userGroups, username, contractorId) {
    // Only admins can remove users from contractors
    if (!isAdmin(userGroups)) {
        throw new Error("Access denied: You do not have permission to remove users from contractors");
    }
    
    // Check if user exists
    const user = await getUserByUsername(dynamodb, tableName, username);
    if (!user) {
        throw new Error(`User with username ${username} not found`);
    }
    
    // Check if contractor exists
    const contractor = await getContractorById(dynamodb, tableName, contractorId);
    if (!contractor) {
        throw new Error(`Contractor with ID ${contractorId} not found`);
    }
    
    // Delete the user-contractor relationship
    const relationshipId = `${username}:${contractorId}`;
    await dynamodb.send(new DeleteCommand({
        TableName: tableName,
        Key: { PK: `USER_CONTRACTOR#${relationshipId}`, SK: `METADATA#${relationshipId}` }
    }));
    
    // Return the user
    return user;
}

module.exports = {
    createContractor,
    updateContractor,
    deleteContractor,
    addUserToContractor,
    removeUserFromContractor
};
