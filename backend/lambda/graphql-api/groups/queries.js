/**
 * Group query operations
 */
const { isAdmin } = require('../utils/permissions');
const { getGroupById, getUsersForGroup } = require('./helpers');
const { queryByPk, queryByIndex, batchGetItems } = require('../utils/dynamodb');

/**
 * Get a group by ID with permission checks
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} id - Group ID
 * @returns {Promise<Object>} - The group if found and user has permission
 * @throws {Error} - If group not found or requester doesn't have permission
 */
async function getGroup(dynamodb, tableName, userGroups, id) {
    // Only admins can view group details
    if (!isAdmin(userGroups)) {
        throw new Error("Access denied: You do not have permission to view group details");
    }
    
    const group = await getGroupById(dynamodb, tableName, id);
    if (!group) {
        throw new Error(`Group with ID ${id} not found`);
    }
    
    return group;
}

/**
 * List groups with pagination
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {number} limit - Maximum number of items to return
 * @param {string} nextToken - Pagination token
 * @returns {Promise<Object>} - Groups with pagination info
 * @throws {Error} - If user doesn't have permission
 */
async function listGroups(dynamodb, tableName, userGroups, limit = 10, nextToken = null) {
    // Only admins can list groups
    if (!isAdmin(userGroups)) {
        throw new Error("Access denied: You do not have permission to list groups");
    }
    
    // Query groups by prefix
    const options = {
        limit,
        nextToken,
        filterExpression: 'begins_with(SK, :sk)',
        expressionAttributeValues: { ':sk': 'METADATA#' }
    };
    
    const result = await queryByIndex(
        dynamodb,
        tableName,
        'type-index',
        'type',
        'GROUP',
        options
    );
    
    return {
        items: result.items,
        nextToken: result.nextToken,
        totalCount: result.count
    };
}

/**
 * Get groups for a project based on group type (manager, operator, worker)
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {Object} project - Project object containing group IDs
 * @param {string} fieldName - Field name (managerGroups, operatorGroups, workerGroups)
 * @returns {Promise<Array<Object>>} - Array of group objects
 */
async function getGroups(dynamodb, tableName, userGroups, project, fieldName) {
    
    // Determine which group IDs to fetch based on the field name
    let groupIds = [];
    if (fieldName === 'managerGroups' && project.managerGroups) {
        groupIds = project.managerGroups;
    } else if (fieldName === 'operatorGroups' && project.operatorGroups) {
        groupIds = project.operatorGroups;
    } else if (fieldName === 'workerGroups' && project.workerGroups) {
        groupIds = project.workerGroups;
    }
    
    // If no group IDs, return empty array
    if (!groupIds || groupIds.length === 0) {
        return [];
    }
    
    // Prepare keys for batch get
    const keys = groupIds.map(id => ({
        PK: `GROUP#${id}`,
        SK: `METADATA#${id}`
    }));
    
    // Fetch all groups in a batch
    const groups = await batchGetItems(dynamodb, tableName, keys);
    
    return groups;
}

module.exports = {
    getGroup,
    listGroups,
    getGroups
};
