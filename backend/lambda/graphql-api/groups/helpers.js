/**
 * Internal helper functions for group operations
 */
const { getItem, queryByIndex } = require('../utils/dynamodb');

/**
 * Get a group by ID
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} id - Group ID
 * @returns {Promise<Object>} - The group or undefined if not found
 */
async function getGroupById(dynamodb, tableName, id) {
    return await getItem(dynamodb, tableName, `GROUP#${id}`, `METADATA#${id}`);
}

/**
 * Get users for a group
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} groupId - Group ID
 * @returns {Promise<Array<Object>>} - Array of users in the group
 */
async function getUsersForGroup(dynamodb, tableName, groupId) {
    // Query the user-group relationship index
    const result = await queryByIndex(
        dynamodb,
        tableName,
        "groupId-index",
        "groupId",
        groupId
    );
    
    // If no users, return empty array
    if (result.items.length === 0) return [];
    
    // Extract usernames from the relationship records
    const usernames = result.items.map(item => item.username);
    
    // Get all users in parallel
    const userPromises = usernames.map(username => 
        getItem(dynamodb, tableName, `USER#${username}`, `METADATA#${username}`)
    );
    
    const users = await Promise.all(userPromises);
    
    // Filter out any undefined results (in case a user was deleted)
    return users.filter(user => user !== undefined);
}

module.exports = {
    getGroupById,
    getUsersForGroup
};
