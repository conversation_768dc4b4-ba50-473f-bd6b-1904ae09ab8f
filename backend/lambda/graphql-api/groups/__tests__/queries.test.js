// __tests__/queries.test.js

const { mockClient } = require('aws-sdk-client-mock');
const { DynamoDBDocumentClient, QueryCommand } = require('@aws-sdk/lib-dynamodb');
const { isAdmin } = require('../../utils/permissions');
const helpers = require('../helpers');
const { 
  getGroup,
  listGroups
} = require('../queries');

// Mock dependencies
jest.mock('../../utils/permissions');
jest.mock('../helpers');

describe('Group Queries', () => {
  const ddbMock = mockClient(DynamoDBDocumentClient);

  // Create a wrapper object that has a send method
  const mockDynamoDbClient = {
    send: jest.fn()
  };
  
  beforeEach(() => {
    ddbMock.reset();
    mockDynamoDbClient.send.mockClear();
    jest.clearAllMocks();
  });

  describe('getGroup', () => {
    test('should retrieve group when requester is admin', async () => {
      isAdmin.mockReturnValue(true);
      
      const mockGroup = {
        id: 'group123',
        name: 'Test Group',
        type: 'MANAGER'
      };
      
      const mockUsers = [
        { username: 'user1', email: '<EMAIL>' },
        { username: 'user2', email: '<EMAIL>' }
      ];
      
      helpers.getGroupById.mockResolvedValue(mockGroup);
      helpers.getUsersForGroup.mockResolvedValue(mockUsers);
      
      const result = await getGroup(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        'group123'
      );
      
      // Verify admin permission check
      expect(isAdmin).toHaveBeenCalledWith(['admin']);
      
      // Verify group retrieval
      expect(helpers.getGroupById).toHaveBeenCalledWith(
        mockDynamoDbClient,
        'TestTable',
        'group123'
      );
      
      // Verify users retrieval
      expect(helpers.getUsersForGroup).toHaveBeenCalledWith(
        mockDynamoDbClient,
        'TestTable',
        'group123'
      );
      
      // Verify returned group with users
      expect(result).toEqual({
        ...mockGroup,
        users: mockUsers
      });
    });
    
    test('should throw error when requester is not admin', async () => {
      isAdmin.mockReturnValue(false);
      
      await expect(getGroup(
        mockDynamoDbClient,
        'TestTable',
        ['UserGroup'],
        'group123'
      )).rejects.toThrow('You do not have permission');
      
      // Verify group retrieval was not attempted
      expect(helpers.getGroupById).not.toHaveBeenCalled();
    });
    
    test('should throw error when group not found', async () => {
      isAdmin.mockReturnValue(true);
      helpers.getGroupById.mockResolvedValue(null);
      
      await expect(getGroup(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        'nonexistentgroup'
      )).rejects.toThrow('not found');
      
      // Verify users retrieval was not attempted
      expect(helpers.getUsersForGroup).not.toHaveBeenCalled();
    });
  });

  describe('listGroups', () => {
    test('should list groups when requester is admin', async () => {
      isAdmin.mockReturnValue(true);
      
      const mockGroups = [
        { id: 'group1', name: 'Group 1', type: 'MANAGER' },
        { id: 'group2', name: 'Group 2', type: 'OPERATOR' }
      ];
      
      // Mock DynamoDB response
      mockDynamoDbClient.send.mockResolvedValue({
        Items: mockGroups,
        LastEvaluatedKey: null,
        Count: 2
      });
      
      // Mock users retrieval for each group
      helpers.getUsersForGroup
        // For group1
        .mockResolvedValueOnce([{ username: 'user1' }, { username: 'user2' }])
        // For group2
        .mockResolvedValueOnce([{ username: 'user3' }]);
      
      const result = await listGroups(
        mockDynamoDbClient,
        'TestTable',
        ['admin']
      );
      
      // Verify admin permission check
      expect(isAdmin).toHaveBeenCalledWith(['admin']);
      
      // Verify DynamoDB query
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            TableName: 'TestTable',
            IndexName: 'PK-index'
          })
        })
      );
      
      // Verify users retrieval for each group
      expect(helpers.getUsersForGroup).toHaveBeenCalledTimes(2);
      
      // Verify returned groups with users
      expect(result).toEqual({
        items: [
          {
            ...mockGroups[0],
            users: [{ username: 'user1' }, { username: 'user2' }]
          },
          {
            ...mockGroups[1],
            users: [{ username: 'user3' }]
          }
        ],
        nextToken: null,
        totalCount: 2
      });
    });
    
    test('should handle pagination with nextToken', async () => {
      isAdmin.mockReturnValue(true);
      
      const mockGroups = [
        { id: 'group3', name: 'Group 3', type: 'WORKER' }
      ];
      
      const lastEvaluatedKey = { PK: 'GROUP#group3' };
      const inputNextToken = JSON.stringify({ PK: 'GROUP#group2' });
      
      // Mock DynamoDB response
      mockDynamoDbClient.send.mockResolvedValue({
        Items: mockGroups,
        LastEvaluatedKey: lastEvaluatedKey,
        Count: 10 // Total count
      });
      
      // Mock users retrieval
      helpers.getUsersForGroup
        .mockResolvedValueOnce([{ username: 'user4' }, { username: 'user5' }]);
      
      const result = await listGroups(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        5, // Limit
        inputNextToken
      );
      
      // Verify DynamoDB query with pagination
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            Limit: 5,
            ExclusiveStartKey: JSON.parse(inputNextToken)
          })
        })
      );
      
      // Verify returned groups with pagination info
      expect(result).toEqual({
        items: [
          {
            ...mockGroups[0],
            users: [{ username: 'user4' }, { username: 'user5' }]
          }
        ],
        nextToken: JSON.stringify(lastEvaluatedKey),
        totalCount: 10
      });
    });
    
    test('should throw error when requester is not admin', async () => {
      isAdmin.mockReturnValue(false);
      
      await expect(listGroups(
        mockDynamoDbClient,
        'TestTable',
        ['UserGroup']
      )).rejects.toThrow('You do not have permission');
      
      // Verify DynamoDB query was not attempted
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
    
    test('should handle empty results', async () => {
      isAdmin.mockReturnValue(true);
      
      // Mock empty DynamoDB response
      mockDynamoDbClient.send.mockResolvedValue({
        Items: [],
        LastEvaluatedKey: null,
        Count: 0
      });
      
      const result = await listGroups(
        mockDynamoDbClient,
        'TestTable',
        ['admin']
      );
      
      // Verify no users retrieval was attempted
      expect(helpers.getUsersForGroup).not.toHaveBeenCalled();
      
      // Verify empty result
      expect(result).toEqual({
        items: [],
        nextToken: null,
        totalCount: 0
      });
    });
  });
});
