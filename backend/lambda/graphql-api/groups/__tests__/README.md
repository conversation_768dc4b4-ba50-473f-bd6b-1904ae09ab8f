# Group Operations Integration Tests

This directory contains integration tests for group operations in the GraphQL API. These tests connect to the actual AWS resources using your local AWS SSO credentials.

## Prerequisites

1. AWS CLI installed and configured
2. Valid AWS SSO credentials with access to the DynamoDB table
3. Node.js and npm installed
4. Jest installed (either globally or as a project dependency)

## Files

- `group.integration.test.js`: The Jest test file that tests the `createGroup` and `listGroups` functions with real AWS resources
- `run-integration-test.ps1`: PowerShell script to help run the integration test with AWS SSO credentials

## How to Run the Tests

### Using the PowerShell Script (Recommended)

1. Open PowerShell
2. Navigate to this directory
3. Run the script:

```powershell
.\run-integration-test.ps1
```

The script will:
- Check if you have AWS SSO profiles configured
- Let you select which profile to use
- Verify if your SSO token is valid or help you log in
- Get the DynamoDB table name from CloudFormation stack outputs or ask you to provide it
- Run the integration test with the correct environment variables

### Manual Execution

If you prefer to run the test manually:

1. Make sure you're logged in with AWS SSO:

```powershell
aws sso login --profile your-sso-profile
```

2. Set the AWS profile environment variable:

```powershell
$env:AWS_PROFILE = "your-sso-profile"
```

3. Set the table name environment variable:

```powershell
$env:TABLE_NAME = "your-dynamodb-table-name"
```

4. Navigate to the project root directory and run the test:

```powershell
cd ../../../
npx jest groups/__tests__/group.integration.test.js --verbose
```

## What the Tests Do

The integration tests:

1. Initialize a DynamoDB client using your AWS SSO credentials
2. Test the `createGroup` function:
   - Creating a new group as an admin
   - Verifying that non-admin users are rejected
3. Test the `listGroups` function:
   - Verifying the created group appears in the list
   - Testing pagination functionality

## Troubleshooting

### Permission Issues

If you see errors related to permissions:

1. Make sure your AWS SSO credentials have admin access to the DynamoDB table
2. Check that your SSO session hasn't expired (run `aws sso login` again if needed)
3. Verify that the table name is correct

### AWS SDK Issues

If you encounter AWS SDK errors:

1. Make sure you have the required AWS SDK packages installed:
   ```
   npm install @aws-sdk/client-dynamodb @aws-sdk/lib-dynamodb
   ```

2. Check that your AWS configuration is correct:
   ```
   aws configure list
   ```

### Test Timeout

If the tests time out, you can increase the timeout in the test file:

```javascript
jest.setTimeout(60000); // Increase to 60 seconds
