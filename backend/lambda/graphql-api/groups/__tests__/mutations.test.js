// __tests__/mutations.test.js

const { mockClient } = require('aws-sdk-client-mock');
const { DynamoDBDocumentClient, PutCommand, UpdateCommand, DeleteCommand } = require('@aws-sdk/lib-dynamodb');
const { isAdmin } = require('../../utils/permissions');
const helpers = require('../helpers');
const { 
  createGroup,
  updateGroup,
  deleteGroup
} = require('../mutations');

// Mock dependencies
jest.mock('../../utils/permissions');
jest.mock('../helpers');
jest.mock('crypto', () => ({
  randomUUID: jest.fn().mockReturnValue('mocked-uuid')
}));

describe('Group Mutations', () => {
  const mockDynamoDbClient = {
    send: jest.fn()
  };
  
  beforeEach(() => {
    mockDynamoDbClient.send.mockClear();
    jest.clearAllMocks();
  });

  describe('createGroup', () => {
    test('should create group when user is admin', async () => {
      isAdmin.mockReturnValue(true);
      
      const mockInput = {
        name: 'Test Group',
        type: 'MANAGER'
      };
      
      mockDynamoDbClient.send.mockResolvedValue({});
      
      const result = await createGroup(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        mockInput
      );
      
      // Verify admin permission check
      expect(isAdmin).toHaveBeenCalledWith(['admin']);
      
      // Verify DynamoDB put operation
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: {
            TableName: 'TestTable',
            Item: expect.objectContaining({
              ...mockInput,
              id: 'mocked-uuid',
              PK: 'GROUP#mocked-uuid',
              SK: 'METADATA#mocked-uuid',
              createdAt: expect.any(String),
              updatedAt: expect.any(String)
            })
          }
        })
      );
      
      // Verify returned group
      expect(result).toEqual(expect.objectContaining({
        ...mockInput,
        id: 'mocked-uuid',
        PK: 'GROUP#mocked-uuid',
        SK: 'METADATA#mocked-uuid',
        createdAt: expect.any(String),
        updatedAt: expect.any(String)
      }));
    });
    
    test('should throw error when user is not admin', async () => {
      isAdmin.mockReturnValue(false);
      
      const mockInput = {
        name: 'Test Group',
        type: 'MANAGER'
      };
      
      await expect(createGroup(
        mockDynamoDbClient,
        'TestTable',
        ['UserGroup'],
        mockInput
      )).rejects.toThrow('You do not have permission');
      
      // Verify DynamoDB was not called
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
  });
  
  describe('updateGroup', () => {
    test('should update group when user is admin', async () => {
      isAdmin.mockReturnValue(true);
      
      // Mock that group exists
      const existingGroup = {
        id: 'group123',
        name: 'Old Name',
        type: 'MANAGER',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z'
      };
      
      helpers.getGroupById.mockResolvedValue(existingGroup);
      
      const mockInput = {
        name: 'New Name'
      };
      
      const mockUpdatedGroup = {
        ...existingGroup,
        name: 'New Name',
        updatedAt: '2023-01-02T00:00:00.000Z'
      };
      
      mockDynamoDbClient.send.mockResolvedValue({
        Attributes: mockUpdatedGroup
      });
      
      const result = await updateGroup(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        'group123',
        mockInput
      );
      
      // Verify admin permission check
      expect(isAdmin).toHaveBeenCalledWith(['admin']);
      
      // Verify group existence check
      expect(helpers.getGroupById).toHaveBeenCalledWith(
        mockDynamoDbClient,
        'TestTable',
        'group123'
      );
      
      // Verify DynamoDB update operation
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            TableName: 'TestTable',
            Key: { PK: 'GROUP#group123', SK: 'METADATA#group123' },
            UpdateExpression: expect.stringContaining('name'),
            ExpressionAttributeValues: expect.objectContaining({
              ':name': 'New Name',
              ':updatedAt': expect.any(String)
            })
          })
        })
      );
      
      // Verify returned group
      expect(result).toEqual(mockUpdatedGroup);
    });
    
    test('should throw error when user is not admin', async () => {
      isAdmin.mockReturnValue(false);
      
      await expect(updateGroup(
        mockDynamoDbClient,
        'TestTable',
        ['UserGroup'],
        'group123',
        { name: 'New Name' }
      )).rejects.toThrow('You do not have permission');
      
      // Verify DynamoDB was not called
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
    
    test('should throw error when group does not exist', async () => {
      isAdmin.mockReturnValue(true);
      
      // Mock that group doesn't exist
      helpers.getGroupById.mockResolvedValue(null);
      
      await expect(updateGroup(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        'nonexistentgroup',
        { name: 'New Name' }
      )).rejects.toThrow('not found');
      
      // Verify DynamoDB was not called
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
  });
  
  describe('deleteGroup', () => {
    test('should delete group when user is admin', async () => {
      isAdmin.mockReturnValue(true);
      
      // Mock that group exists
      helpers.getGroupById.mockResolvedValue({
        id: 'group123',
        name: 'Test Group',
        type: 'MANAGER'
      });
      
      mockDynamoDbClient.send.mockResolvedValue({});
      
      const result = await deleteGroup(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        'group123'
      );
      
      // Verify admin permission check
      expect(isAdmin).toHaveBeenCalledWith(['admin']);
      
      // Verify group existence check
      expect(helpers.getGroupById).toHaveBeenCalledWith(
        mockDynamoDbClient,
        'TestTable',
        'group123'
      );
      
      // Verify DynamoDB delete operation
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: {
            TableName: 'TestTable',
            Key: { PK: 'GROUP#group123', SK: 'METADATA#group123' }
          }
        })
      );
      
      // Verify returned group ID
      expect(result).toBe('group123');
    });
    
    test('should throw error when user is not admin', async () => {
      isAdmin.mockReturnValue(false);
      
      await expect(deleteGroup(
        mockDynamoDbClient,
        'TestTable',
        ['UserGroup'],
        'group123'
      )).rejects.toThrow('You do not have permission');
      
      // Verify DynamoDB was not called
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
    
    test('should throw error when group does not exist', async () => {
      isAdmin.mockReturnValue(true);
      
      // Mock that group doesn't exist
      helpers.getGroupById.mockResolvedValue(null);
      
      await expect(deleteGroup(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        'nonexistentgroup'
      )).rejects.toThrow('not found');
      
      // Verify DynamoDB was not called
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
  });
});
