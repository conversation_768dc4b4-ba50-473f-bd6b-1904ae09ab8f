# PowerShell script to run integration tests with AWS SSO credentials

# Check if AWS SSO credentials are available
Write-Host "Checking AWS SSO credentials..."
$awsConfig = Get-Content -Path "$env:USERPROFILE\.aws\config" -ErrorAction SilentlyContinue
if (-not $awsConfig) {
    Write-Host "AWS config not found. Please configure AWS CLI and login with AWS SSO." -ForegroundColor Red
    exit 1
}

# Check if AWS SSO token is valid
$ssoProfiles = $awsConfig | Select-String -Pattern "^\[profile.*sso"
if (-not $ssoProfiles) {
    Write-Host "No SSO profiles found in AWS config. Please configure AWS SSO." -ForegroundColor Red
    exit 1
}

# Get available SSO profiles
$profiles = @()
foreach ($line in $ssoProfiles) {
    if ($line -match "^\[profile (.*)\]") {
        $profiles += $Matches[1]
    }
}

Write-Host "Found the following SSO profiles:" -ForegroundColor Green
for ($i = 0; $i -lt $profiles.Count; $i++) {
    Write-Host "[$i] $($profiles[$i])"
}

# Ask user to select a profile
$selectedIndex = Read-Host "Select a profile by number (default: 0)"
if ([string]::IsNullOrWhiteSpace($selectedIndex)) {
    $selectedIndex = 0
}
$selectedProfile = $profiles[$selectedIndex]

Write-Host "Using profile: $selectedProfile" -ForegroundColor Green

# Check if the token is valid
Write-Host "Checking if SSO token is valid..."
$tokenValid = $false
try {
    $env:AWS_PROFILE = $selectedProfile
    $callerIdentity = aws sts get-caller-identity --output json | ConvertFrom-Json
    if ($callerIdentity.Account) {
        $tokenValid = $true
        Write-Host "SSO token is valid. Authenticated as: $($callerIdentity.Arn)" -ForegroundColor Green
    }
}
catch {
    Write-Host "SSO token is invalid or expired." -ForegroundColor Yellow
}

# If token is not valid, login
if (-not $tokenValid) {
    Write-Host "Logging in with AWS SSO..."
    aws sso login --profile $selectedProfile
    
    # Check if login was successful
    try {
        $callerIdentity = aws sts get-caller-identity --output json | ConvertFrom-Json
        if ($callerIdentity.Account) {
            Write-Host "Successfully logged in as: $($callerIdentity.Arn)" -ForegroundColor Green
        }
        else {
            Write-Host "Failed to login with AWS SSO." -ForegroundColor Red
            exit 1
        }
    }
    catch {
        Write-Host "Failed to login with AWS SSO: $_" -ForegroundColor Red
        exit 1
    }
}

# Get the DynamoDB table name from the stack outputs
Write-Host "Getting DynamoDB table name from CloudFormation stack..."
$stackName = Read-Host "Enter the CloudFormation stack name (e.g., 'YourAppName-GraphQlApiStack')"

try {
    $stackOutputs = aws cloudformation describe-stacks --stack-name $stackName --query "Stacks[0].Outputs" --output json | ConvertFrom-Json
    $tableName = ($stackOutputs | Where-Object { $_.OutputKey -eq "TableName" }).OutputValue
    
    if (-not $tableName) {
        Write-Host "TableName not found in stack outputs. Please enter it manually." -ForegroundColor Yellow
        $tableName = Read-Host "Enter the DynamoDB table name"
    }
    else {
        Write-Host "Found table name: $tableName" -ForegroundColor Green
    }
}
catch {
    Write-Host "Failed to get stack outputs: $_" -ForegroundColor Yellow
    Write-Host "Please enter the DynamoDB table name manually."
    $tableName = Read-Host "Enter the DynamoDB table name"
}

# Set environment variables for the test
$env:TABLE_NAME = $tableName

# Navigate to the project root directory
$currentDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectRoot = Split-Path -Parent (Split-Path -Parent (Split-Path -Parent $currentDir))
Set-Location $projectRoot

# Run the integration test
Write-Host "Running group integration test..." -ForegroundColor Green
npx jest groups/__tests__/group.integration.test.js --verbose

# Reset location
Set-Location $currentDir
