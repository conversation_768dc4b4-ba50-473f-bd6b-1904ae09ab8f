// __tests__/index.test.js

const groupModule = require('../index');
const queries = require('../queries');
const mutations = require('../mutations');

describe('Group Module Exports', () => {
  test('should export all query functions', () => {
    // Check that all query functions are exported
    expect(groupModule.getGroup).toBe(queries.getGroup);
    expect(groupModule.listGroups).toBe(queries.listGroups);
  });

  test('should export all mutation functions', () => {
    // Check that all mutation functions are exported
    expect(groupModule.createGroup).toBe(mutations.createGroup);
    expect(groupModule.updateGroup).toBe(mutations.updateGroup);
    expect(groupModule.deleteGroup).toBe(mutations.deleteGroup);
  });

  test('should have all expected exports', () => {
    // Check that the module exports exactly the expected functions
    const expectedExports = [
      'getGroup',
      'listGroups',
      'createGroup',
      'updateGroup',
      'deleteGroup'
    ];

    const actualExports = Object.keys(groupModule);
    
    // Check that all expected exports are present
    expectedExports.forEach(exportName => {
      expect(actualExports).toContain(exportName);
    });
    
    // Check that there are no unexpected exports
    expect(actualExports.length).toBe(expectedExports.length);
    
    // Alternative way to check the same thing
    expect(actualExports.sort()).toEqual(expectedExports.sort());
  });
});
