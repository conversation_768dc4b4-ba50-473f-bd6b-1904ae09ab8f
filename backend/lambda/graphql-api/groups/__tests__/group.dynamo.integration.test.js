// group.integration.test.js - Integration tests for group operations

const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient } = require('@aws-sdk/lib-dynamodb');
const { listGroups, getGroup } = require('../queries');
const { createGroup } = require('../mutations');

/**
 * This integration test uses local AWS SSO credentials to test group operations.
 * 
 * Prerequisites:
 * 1. Valid AWS SSO credentials configured in your environment
 * 2. Access to the DynamoDB table used by the application
 * 
 * To run this test:
 * 1. Make sure you're logged in with AWS SSO
 * 2. Run: jest groups/__tests__/group.integration.test.js
 */

// Set a longer timeout for integration tests
jest.setTimeout(30000);

describe('Group Creation - Integration Test', () => {
  // Initialize DynamoDB client with local AWS SSO credentials
  const client = new DynamoDBClient({});
  const dynamodb = DynamoDBDocumentClient.from(client);
  
  // Get table name from environment or use a test table
  const tableName = process.env.TABLE_NAME || 'TAExGraphQlApiStack-TAExTable948F8D9B-PNHWWR1O612L';
  
  // Admin user groups for testing
  const adminGroups = ['admin'];
  const nonAdminGroups = ['User'];
  
  // Generate a unique group name for testing
  const testGroupName = `test-group-${Date.now()}`;
  
  test('should create a new group when authenticated as admin', async () => {
    try {
      // Create group input
      const groupInput = {
        name: testGroupName,
        description: 'Test group created by integration test',
        groupType: 'MANAGER', // Assuming group types like MANAGER, OPERATOR, WORKER
        enabled: true
      };
      
      // Call the createGroup function with actual DynamoDB client
      const result = await createGroup(
        dynamodb,
        tableName,
        adminGroups,
        groupInput
      );
      
      // Verify the structure of the response
      expect(result).toHaveProperty('id');
      expect(result).toHaveProperty('name', testGroupName);
      expect(result).toHaveProperty('description', 'Test group created by integration test');
      expect(result).toHaveProperty('groupType', 'MANAGER');
      expect(result).toHaveProperty('enabled', true);
      expect(result).toHaveProperty('PK', expect.stringMatching(/^GROUP#/));
      expect(result).toHaveProperty('SK', expect.stringMatching(/^METADATA#/));
      expect(result).toHaveProperty('createdAt');
      expect(result).toHaveProperty('updatedAt');
      
      console.log(`Successfully created group: ${testGroupName} with ID: ${result.id}`);
    } catch (error) {
      // If the error is about permissions, it might be that the SSO credentials
      // don't have admin access, which is expected in some environments
      if (error.message.includes('Access denied') || error.message.includes('permission')) {
        console.warn('Test skipped due to permission issues. Make sure your SSO credentials have admin access.');
        return;
      }
      
      // For other errors, fail the test
      throw error;
    }
  });
  
  test('should reject group creation for non-admin users', async () => {
    // Create group input with a different name
    const groupInput = {
      name: `${testGroupName}-2`,
      description: 'Another test group',
      groupType: 'OPERATOR',
      enabled: true
    };
    
    // Expect the function to throw an error about permissions
    await expect(createGroup(
      dynamodb,
      tableName,
      nonAdminGroups,
      groupInput
    )).rejects.toThrow('Access denied: You do not have permission to create groups');
  });
  
  test('should verify created group appears in list', async () => {
    try {
      // List all groups
      const result = await listGroups(
        dynamodb,
        tableName,
        adminGroups
      );

      console.log(result)
      
      // Verify the structure of the response
      expect(result).toHaveProperty('items');
      expect(result).toHaveProperty('nextToken');
      expect(result).toHaveProperty('totalCount');
      
      // Verify that items is an array
      expect(Array.isArray(result.items)).toBe(true);
      
      // Find our test group in the list
      const createdGroup = result.items.find(group => group.name === testGroupName);
      expect(createdGroup).toBeDefined();
      expect(createdGroup).toHaveProperty('name', testGroupName);
      expect(createdGroup).toHaveProperty('description', 'Test group created by integration test');
      expect(createdGroup).toHaveProperty('groupType', 'MANAGER');
      expect(createdGroup).toHaveProperty('enabled', true);
      
      console.log(`Found ${result.totalCount} groups in the database, including our test group`);
    } catch (error) {
      // If the error is about permissions, it might be that the SSO credentials
      // don't have admin access, which is expected in some environments
      if (error.message.includes('Access denied') || error.message.includes('permission')) {
        console.warn('Test skipped due to permission issues. Make sure your SSO credentials have admin access.');
        return;
      }
      
      // For other errors, fail the test
      throw error;
    }
  });
  
  test('should handle pagination in group listing', async () => {
    try {
      // First request with small limit
      const firstPage = await listGroups(
        dynamodb,
        tableName,
        adminGroups,
        2 // Small limit to force pagination
      );
      
      // If there are more than 2 groups, we should have a nextToken
      if (firstPage.totalCount > 2) {
        expect(firstPage.nextToken).toBeTruthy();
        
        // Get the second page using the nextToken
        const secondPage = await listGroups(
          dynamodb,
          tableName,
          adminGroups,
          2, // Same limit
          firstPage.nextToken
        );
        
        // Verify we got different groups
        expect(secondPage.items).not.toEqual(firstPage.items);
        
        // Verify the total count is the same
        expect(secondPage.totalCount).toEqual(firstPage.totalCount);
      } else {
        console.log('Not enough groups to test pagination');
      }
    } catch (error) {
      // Handle permission errors as in the previous test
      if (error.message.includes('Access denied') || error.message.includes('permission')) {
        console.warn('Test skipped due to permission issues. Make sure your SSO credentials have admin access.');
        return;
      }
      
      throw error;
    }
  });
});

describe('Group Listing - Integration Test', () => {
  // Initialize DynamoDB client with local AWS SSO credentials
  const client = new DynamoDBClient({});
  const dynamodb = DynamoDBDocumentClient.from(client);
  
  // Get table name from environment or use a test table
  const tableName = process.env.TABLE_NAME || 'TAExGraphQlApiStack-TAExTable948F8D9B-PNHWWR1O612L';
  
  // Admin user groups for testing
  const adminGroups = ['admin'];
  const nonAdminGroups = ['User'];
  
  test('should list all groups when authenticated as admin', async () => {
    try {
      // Call the listGroups function with actual DynamoDB client
      const result = await listGroups(
        dynamodb,
        tableName,
        adminGroups
      );
      
      // Verify the structure of the response
      expect(result).toHaveProperty('items');
      expect(result).toHaveProperty('nextToken');
      expect(result).toHaveProperty('totalCount');
      
      // Verify that items is an array
      expect(Array.isArray(result.items)).toBe(true);
      
      // Verify that each item has the expected properties
      if (result.items.length > 0) {
        const firstGroup = result.items[0];
        expect(firstGroup).toHaveProperty('id');
        expect(firstGroup).toHaveProperty('name');
        expect(firstGroup).toHaveProperty('description');
        expect(firstGroup).toHaveProperty('groupType');
        expect(firstGroup).toHaveProperty('enabled');
        expect(firstGroup).toHaveProperty('PK');
        expect(firstGroup).toHaveProperty('SK');
        
        console.log(`Successfully listed ${result.totalCount} groups`);
      } else {
        console.log('No groups found in the database');
      }
    } catch (error) {
      // If the error is about permissions, it might be that the SSO credentials
      // don't have admin access, which is expected in some environments
      if (error.message.includes('Access denied') || error.message.includes('permission')) {
        console.warn('Test skipped due to permission issues. Make sure your SSO credentials have admin access.');
        return;
      }
      
      // For other errors, fail the test
      throw error;
    }
  });
  
  test('should reject group listing for non-admin users', async () => {
    // Expect the function to throw an error about permissions
    await expect(listGroups(
      dynamodb,
      tableName,
      nonAdminGroups
    )).rejects.toThrow('Access denied: You do not have permission to list groups');
  });
  
  test('should respect the limit parameter', async () => {
    try {
      // Request with a specific limit
      const limit = 3;
      const result = await listGroups(
        dynamodb,
        tableName,
        adminGroups,
        limit
      );
      
      // Verify that we don't get more items than the limit
      expect(result.items.length).toBeLessThanOrEqual(limit);
      
      // If there are more total items than the limit, we should have a nextToken
      if (result.totalCount > limit) {
        expect(result.nextToken).toBeTruthy();
      }
      
      console.log(`Successfully limited results to ${result.items.length} groups out of ${result.totalCount} total`);
    } catch (error) {
      // Handle permission errors as in the previous test
      if (error.message.includes('Access denied') || error.message.includes('permission')) {
        console.warn('Test skipped due to permission issues. Make sure your SSO credentials have admin access.');
        return;
      }
      
      throw error;
    }
  });
});

describe('Group Retrieval - Integration Test', () => {
  // Initialize DynamoDB client with local AWS SSO credentials
  const client = new DynamoDBClient({});
  const dynamodb = DynamoDBDocumentClient.from(client);
  
  // Get table name from environment or use a test table
  const tableName = process.env.TABLE_NAME || 'TAExGraphQlApiStack-TAExTable948F8D9B-PNHWWR1O612L';
  
  // Admin user groups for testing
  const adminGroups = ['admin'];
  const nonAdminGroups = ['User'];
  
  // Create a test group to retrieve
  let testGroupId;
  let testGroupName;
  
  beforeAll(async () => {
    // Create a test group to use for retrieval tests
    testGroupName = `test-group-${Date.now()}`;
    const groupInput = {
      name: testGroupName,
      description: 'Test group for getGroup integration test',
      groupType: 'MANAGER',
      enabled: true
    };
    
    try {
      // Create the group and store its ID
      const result = await createGroup(
        dynamodb,
        tableName,
        adminGroups,
        groupInput
      );
      testGroupId = result.id;
      console.log(`Created test group with ID: ${testGroupId} for getGroup tests`);
    } catch (error) {
      console.warn(`Failed to create test group: ${error.message}`);
      // If we can't create a group, we'll try to find an existing one
      try {
        const groups = await listGroups(dynamodb, tableName, adminGroups);
        if (groups.items.length > 0) {
          testGroupId = groups.items[0].id;
          testGroupName = groups.items[0].name;
          console.log(`Using existing group with ID: ${testGroupId} for getGroup tests`);
        }
      } catch (listError) {
        console.error(`Failed to list groups: ${listError.message}`);
      }
    }
  });
  
  test('should retrieve a group by ID when authenticated as admin', async () => {
    // Skip test if we couldn't set up a test group
    if (!testGroupId) {
      console.warn('Skipping test because no test group is available');
      return;
    }
    
    try {
      // Call the getGroup function with actual DynamoDB client
      const result = await getGroup(
        dynamodb,
        tableName,
        adminGroups,
        testGroupId
      );
      
      // Verify the structure of the response
      expect(result).toHaveProperty('id', testGroupId);
      expect(result).toHaveProperty('name');
      expect(result).toHaveProperty('description');
      expect(result).toHaveProperty('groupType');
      expect(result).toHaveProperty('enabled');
      expect(result).toHaveProperty('PK', `GROUP#${testGroupId}`);
      expect(result).toHaveProperty('SK', `METADATA#${testGroupId}`);
      expect(result).toHaveProperty('createdAt');
      expect(result).toHaveProperty('updatedAt');
      
      console.log(`Successfully retrieved group: ${result.name} with ID: ${result.id}`);
    } catch (error) {
      // If the error is about permissions, it might be that the SSO credentials
      // don't have admin access, which is expected in some environments
      if (error.message.includes('Access denied') || error.message.includes('permission')) {
        console.warn('Test skipped due to permission issues. Make sure your SSO credentials have admin access.');
        return;
      }
      
      // For other errors, fail the test
      throw error;
    }
  });
  
  test('should reject group retrieval for non-admin users', async () => {
    // Skip test if we couldn't set up a test group
    if (!testGroupId) {
      console.warn('Skipping test because no test group is available');
      return;
    }
    
    // Expect the function to throw an error about permissions
    await expect(getGroup(
      dynamodb,
      tableName,
      nonAdminGroups,
      testGroupId
    )).rejects.toThrow('Access denied: You do not have permission to view group details');
  });
  
  test('should throw error when group ID does not exist', async () => {
    const nonExistentId = 'non-existent-id';
    
    // Expect the function to throw an error about the group not being found
    await expect(getGroup(
      dynamodb,
      tableName,
      adminGroups,
      nonExistentId
    )).rejects.toThrow(`Group with ID ${nonExistentId} not found`);
  });
});
