/**
 * Group mutation operations
 */
const { PutCommand, UpdateCommand, DeleteCommand } = require('@aws-sdk/lib-dynamodb');
const { randomUUID } = require('crypto');
const { isAdmin } = require('../utils/permissions');
const { getGroupById } = require('./helpers');
const { buildUpdateExpression } = require('../utils/dynamodb');

/**
 * Create a new group
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {Object} input - Group input data
 * @returns {Promise<Object>} - The created group
 * @throws {Error} - If user doesn't have permission
 */
async function createGroup(dynamodb, tableName, userGroups, input) {
    // Only admins can create groups
    if (!isAdmin(userGroups)) {
        throw new Error("Access denied: You do not have permission to create groups");
    }
    
    const id = randomUUID();
    const timestamp = new Date().toISOString();
    
    const group = {
        ...input,
        id,
        PK: `GROUP#${id}`,
        SK: `METADATA#${id}`,
        type: 'GROUP',
        createdAt: timestamp,
        updatedAt: timestamp
    };
    
    await dynamodb.send(new PutCommand({
        TableName: tableName,
        Item: group
    }));
    
    return group;
}

/**
 * Update a group
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} id - Group ID
 * @param {Object} input - Group update data
 * @returns {Promise<Object>} - The updated group
 * @throws {Error} - If user doesn't have permission or group doesn't exist
 */
async function updateGroup(dynamodb, tableName, userGroups, id, input) {
    // Only admins can update groups
    if (!isAdmin(userGroups)) {
        throw new Error("Access denied: You do not have permission to update groups");
    }
    
    // Check if group exists
    const existingGroup = await getGroupById(dynamodb, tableName, id);
    if (!existingGroup) {
        throw new Error(`Group with ID ${id} not found`);
    }
    
    // Add updatedAt timestamp to the input
    const updatedInput = {
        ...input,
        updatedAt: new Date().toISOString()
    };
    
    // Build the update expression
    const { expression, attributeNames, attributeValues } = buildUpdateExpression(updatedInput);
    
    // Prepare the DynamoDB update operation
    const params = {
        TableName: tableName,
        Key: { PK: `GROUP#${id}`, SK: `METADATA#${id}` },
        UpdateExpression: expression,
        ExpressionAttributeNames: attributeNames,
        ExpressionAttributeValues: attributeValues,
        ReturnValues: "ALL_NEW"
    };
    
    // Execute the update and return the updated item
    const response = await dynamodb.send(new UpdateCommand(params));
    return response.Attributes;
}

/**
 * Delete a group
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} id - Group ID
 * @returns {Promise<string>} - The deleted group ID
 * @throws {Error} - If user doesn't have permission or group doesn't exist
 */
async function deleteGroup(dynamodb, tableName, userGroups, id) {
    // Only admins can delete groups
    if (!isAdmin(userGroups)) {
        throw new Error("Access denied: You do not have permission to delete groups");
    }
    
    // Check if group exists
    const existingGroup = await getGroupById(dynamodb, tableName, id);
    if (!existingGroup) {
        throw new Error(`Group with ID ${id} not found`);
    }
    
    // Delete the group
    await dynamodb.send(new DeleteCommand({
        TableName: tableName,
        Key: { PK: `GROUP#${id}`, SK: `METADATA#${id}` }
    }));
    
    // Note: In a production system, we would also need to:
    // 1. Remove this group from all users who have it
    // 2. Delete all user-group relationship records for this group
    // This would require additional queries and batch operations
    
    return id;
}

module.exports = {
    createGroup,
    updateGroup,
    deleteGroup
};
