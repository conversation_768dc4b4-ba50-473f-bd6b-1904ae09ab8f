/**
 * GraphQL API Lambda Handler
 *
 * This is the main entry point for the GraphQL API Lambda function.
 * It handles all GraphQL queries and mutations by routing them to the appropriate resolver functions.
 */
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, QueryCommand } = require('@aws-sdk/lib-dynamodb');

// Initialize DynamoDB client
const client = new DynamoDBClient({});
const dynamodb = DynamoDBDocumentClient.from(client);
const tableName = process.env.TABLE_NAME;

// Import resolver functions
const {
    getActivitiesForProject,
    getActivity,
    addActivity,
    updateActivity,
    updateActivities,
    deleteActivity,
    getSyncStatus
} = require('./activities/index');

const {
    getProject,
    listProjects,
    addProject,
    updateProject,
    deleteProject
} = require('./projects/index');

const {
    getCountByStatusForProject,
    getCompletedActivitiesByDay,
    getPlannedActivitiesByDay,
    getCountOfUnableToWorkActivities,
    getActivityCountGroupedByStatusAndDiscipline
} = require('./dashboard/index');

// Import users resolver functions
const {
    getUser,
    listUsers,
    createUser,
    addUserToGroup,
    deleteUser,
    removeUserFromGroup,
    updateUser
} = require('./users/index');

// Import groups resolver functions
const {
    getGroup,
    listGroups,
    getGroups,
    createGroup,
    updateGroup,
    deleteGroup
} = require('./groups/index');

// Import contractors resolver functions
const {
    getContractor,
    listContractors,
    createContractor,
    updateContractor,
    deleteContractor,
    addUserToContractor,
    removeUserFromContractor
} = require('./contractors/index');

// Import preSignedS3Url resolver functions
const {
    getUploadUrl,
    getDownloadUrl,
    getDeleteUrl
} = require('./preSignedS3Url/index');

// Import syncjobs resolver functions
const {
    listSyncJobs
} = require('./syncjobs/index');

// Import helper functions
const { getContractorsForUser } = require('./contractors/helpers');
const { getUsersForContractor } = require('./contractors/helpers');

/**
 * Main Lambda handler function
 * @param {Object} event - Lambda event object
 * @returns {Promise<Object>} - GraphQL response
 */
exports.handler = async function(event) {
    console.log('Received event:', JSON.stringify(event, null, 2));

    // Extract common parameters
    const { arguments: args = {}, identity = {} } = event;
    const appGroups = identity.groups || [];
    const { fieldName, parentTypeName } = event.info;
    const source = event.source;
    
    // Initialize userGroups with application-level groups from identity token
    let userGroups = [...appGroups];
    
    // Check if user is not an admin and fetch object-level groups
    if (identity.username && !appGroups.includes('admin')) {
        try {
            // Create QueryCommand to get user's object-level groups
            const queryCommand = new QueryCommand({
                TableName: tableName,
                KeyConditionExpression: 'PK = :pk',
                ExpressionAttributeValues: {
                    ':pk': `USER#${identity.username}`
                }
            });
            
            // Execute the query
            const userResult = await dynamodb.send(queryCommand);
            
            // If user found, extract object-level groups from the three group types
            if (userResult.Items && userResult.Items.length > 0) {
                const userItem = userResult.Items[0];
                
                // Extract group IDs from each group type
                const managerGroupIds = Array.isArray(userItem.managerGroupIds) ? userItem.managerGroupIds : [];
                const operatorGroupIds = Array.isArray(userItem.operatorGroupIds) ? userItem.operatorGroupIds : [];
                const workerGroupIds = Array.isArray(userItem.workerGroupIds) ? userItem.workerGroupIds : [];
                
                // Combine all group IDs with application-level groups
                userGroups = [...userGroups, ...managerGroupIds, ...operatorGroupIds, ...workerGroupIds];
                
                console.log('Combined user groups:', userGroups);
            }
        } catch (error) {
            console.error('Error fetching user object-level groups:', error);
            // Continue with application-level groups only
        }
    }

    try {
        // Handle field resolvers for parent types
        if (parentTypeName && parentTypeName !== 'Mutation' && parentTypeName !== 'Query') {
            return await resolveFieldForParentType(parentTypeName, fieldName, source, args, userGroups, identity.username);
        }

        // Handle top-level queries and mutations
        return await resolveOperation(event, args, userGroups);
    } catch (error) {
        console.error('Error:', error);

        // Preserve access denied errors
        if (error.message.includes("Access denied")) {
            throw error;
        }

        // Wrap other errors with more context
        throw new Error(`Operation failed: ${error.message}`);
    }
};

/**
 * Resolve field for a parent type (field resolver)
 * @param {string} parentTypeName - Parent type name
 * @param {string} fieldName - Field name
 * @param {Object} source - Source object
 * @param {Object} args - Field arguments
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} username - Username from identity
 * @returns {Promise<Object>} - Resolved field value
 */
async function resolveFieldForParentType(parentTypeName, fieldName, source, args, userGroups, username) {
    switch (parentTypeName) {
        case "Project":
            if (fieldName === "activities") {
                // Extract filter parameters from args - only include non-null values
                const filters = {};
                if (args.status !== null && args.status !== undefined) filters.status = args.status;
                if (args.unableToWork !== null && args.unableToWork !== undefined) filters.unableToWork = args.unableToWork;
                if (args.plannedStartDateFrom !== null && args.plannedStartDateFrom !== undefined) filters.plannedStartDateFrom = args.plannedStartDateFrom;
                if (args.plannedStartDateTo !== null && args.plannedStartDateTo !== undefined) filters.plannedStartDateTo = args.plannedStartDateTo;
                if (args.plannedFinishDateFrom !== null && args.plannedFinishDateFrom !== undefined) filters.plannedFinishDateFrom = args.plannedFinishDateFrom;
                if (args.plannedFinishDateTo !== null && args.plannedFinishDateTo !== undefined) filters.plannedFinishDateTo = args.plannedFinishDateTo;
                
                console.log('Filtered filters:', filters);
                return await getActivitiesForProject(dynamodb, tableName, userGroups, source.id, args.limit, args.nextToken, username, filters);
            }
            if (fieldName === "managerGroups" || fieldName === "operatorGroups" || fieldName === "workerGroups") {
                return await getGroups(dynamodb, tableName, userGroups, source, fieldName);
            }
            break;
        case "Activity":
            if (fieldName === "contractor") {
                const { getContractorForActivity } = require('./contractors/helpers');
                return await getContractorForActivity(dynamodb, tableName, source);
            }
            if (fieldName === "predecessorActivities") {
                const { getPredecessorActivities } = require('./activities/queries');
                return await getPredecessorActivities(dynamodb, tableName, source);
            }
            if (fieldName === "successorActivities") {
                const { getSuccessorActivities } = require('./activities/queries');
                return await getSuccessorActivities(dynamodb, tableName, source);
            }
            if (fieldName === "syncStatus") {
                return await getSyncStatus(dynamodb, tableName, source);
            }
            break;
        case "Group":
            if (fieldName === "users") {
                return await listUsers(dynamodb, tableName, userGroups, { groupId: source.id }, args.limit, args.nextToken);
            }
            break;
        case "User":
            if (fieldName === "groups") {
                return await listGroups(dynamodb, tableName, userGroups, { userId: source.id }, args.limit, args.nextToken);
            }
            if (fieldName === "contractors") {
                return await getContractorsForUser(dynamodb, tableName, source.username);
            }
            break;
        case "Contractor":
            if (fieldName === "users") {
                return await getUsersForContractor(dynamodb, tableName, source.id);
            }
            break;
    }

    throw new Error(`Unhandled field ${fieldName} for parent type ${parentTypeName}`);
}

/**
 * Resolve a top-level query or mutation
 * @param {Object} event - Lambda event object
 * @param {Object} args - Operation arguments
 * @param {Array<string>} userGroups - User groups for authorization
 * @returns {Promise<Object>} - Operation result
 */
async function resolveOperation(event, args, userGroups) {
    const fieldName = event.info.fieldName;

    switch (fieldName) {
        // Activity operations
        case 'getActivity':
            return await getActivity(dynamodb, tableName, userGroups, args.id, event.identity.username);
        case 'addActivity':
            return await addActivity(dynamodb, tableName, userGroups, args.input);
        case 'updateActivity':
            return await updateActivity(dynamodb, tableName, userGroups, event);
        case 'updateActivities':
            return await updateActivities(dynamodb, tableName, userGroups, event);
        case 'deleteActivity':
            return await deleteActivity(dynamodb, tableName, userGroups, args.id);

        // Project operations
        case 'getProject':
            return await getProject(dynamodb, tableName, userGroups, args.id);
        case 'listProjects':
            return await listProjects(dynamodb, tableName, userGroups, args.limit, args.nextToken);
        case 'addProject':
            return await addProject(dynamodb, tableName, userGroups, args.input);
        case 'updateProject':
            return await updateProject(dynamodb, tableName, userGroups, args.id, args.input);
        case 'deleteProject':
            return await deleteProject(dynamodb, tableName, userGroups, args.id);

        // Dashboard operations
        case 'getCountByStatus':
            return await getCountByStatusForProject(args.projectId, userGroups);
        case 'getCompletedActivitiesByDay':
            return await getCompletedActivitiesByDay(args.projectId, userGroups);
        case 'getPlannedActivitiesByDay':
            return await getPlannedActivitiesByDay(args.projectId, userGroups);
        case 'getCountOfUnableToWorkActivities':
            return await getCountOfUnableToWorkActivities(args.projectId, userGroups);
        case 'getActivityCountGroupedByStatusAndDiscipline':
            return await getActivityCountGroupedByStatusAndDiscipline(args.projectId, userGroups);

        // User operations
        case 'getUser':
            return await getUser(dynamodb, tableName, userGroups, args.username, event.identity.username || 'unknown');
        case 'getUserByEmail':
            return await getUserByEmail(dynamodb, tableName, userGroups, args.email);
        case 'listUsers':
            return await listUsers(dynamodb, tableName, userGroups, args.filter, args.limit, args.nextToken);
        case 'createUser':
            return await createUser(dynamodb, tableName, userGroups, args.input);
        case 'updateUser':
            return await updateUser(dynamodb, tableName, userGroups, args.username, args.input);
        case 'deleteUser':
            return await deleteUser(dynamodb, tableName, userGroups, args.username);

        // Group operations
        case 'getGroup':
            return await getGroup(dynamodb, tableName, userGroups, args.id);
        case 'listGroups':
            return await listGroups(dynamodb, tableName, userGroups, args.limit, args.nextToken);
        case 'createGroup':
            return await createGroup(dynamodb, tableName, userGroups, args.input);
        case 'updateGroup':
            return await updateGroup(dynamodb, tableName, userGroups, args.id, args.input);
        case 'deleteGroup':
            return await deleteGroup(dynamodb, tableName, userGroups, args.id);
        case 'addUserToGroup':
            return await addUserToGroup(dynamodb, tableName, userGroups, args.username, args.groupId);
        case 'removeUserFromGroup':
            return await removeUserFromGroup(dynamodb, tableName, userGroups, args.username, args.groupId);
            
        // Contractor operations
        case 'getContractor':
            return await getContractor(dynamodb, tableName, userGroups, args.id);
        case 'listContractors':
            return await listContractors(dynamodb, tableName, userGroups, args.limit, args.nextToken);
        case 'createContractor':
            return await createContractor(dynamodb, tableName, userGroups, args.input);
        case 'updateContractor':
            return await updateContractor(dynamodb, tableName, userGroups, args.id, args.input);
        case 'deleteContractor':
            return await deleteContractor(dynamodb, tableName, userGroups, args.id);
        case 'addUserToContractor':
            return await addUserToContractor(dynamodb, tableName, userGroups, args.username, args.contractorId);
        case 'removeUserFromContractor':
            return await removeUserFromContractor(dynamodb, tableName, userGroups, args.username, args.contractorId);
            
        // PreSignedS3Url operations
        case 'getUploadUrl':
            return await getUploadUrl(dynamodb, tableName, userGroups, args.fileName, args.activityId);
        case 'getDownloadUrl':
            return await getDownloadUrl(dynamodb, tableName, userGroups, args.key, args.activityId);
        case 'getDeleteUrl':
            return await getDeleteUrl(dynamodb, tableName, userGroups, args.key, args.activityId);
            
        // SyncJob operations
        case 'listSyncJobs':
            return await listSyncJobs(dynamodb, tableName, userGroups, args);

        default:
            throw new Error(`Unknown field name: ${fieldName}`);
    }
}
