// __tests__/getCountByStatusForProject.integration.test.js

const { getCountByStatusForProject } = require('../queries');

describe('getCountByStatusForProject Integration Tests', () => {
  // Setup environment before tests
  beforeAll(() => {
    process.env.TABLE_NAME = 'TAExGraphQlApiStack-TAExTable948F8D9B-PNHWWR1O612L';
  });

  // Clean up after each test
  afterEach(() => {

  });

  // Test case 1: Basic functionality with single page of results
  test('should count items by status correctly', async () => {

    const event = {'arguments': {'projectId': '783'}};

    const statusItems = await getCountByStatusForProject(event);
    console.log(statusItems)

    expect(statusItems).toEqual({
      PENDING: 2,
      COMPLETED: 1,
      FAILED: 1
    });

    
  });

  
});
