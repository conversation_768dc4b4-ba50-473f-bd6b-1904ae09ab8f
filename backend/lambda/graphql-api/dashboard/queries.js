const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, QueryCommand } = require('@aws-sdk/lib-dynamodb');
const { isAdmin, hasRoles } = require('../utils/permissions');
const { getProjectById } = require('../activities/helpers');

// Initialize the DynamoDB client with marshal options for better data handling
const client = new DynamoDBClient({
    region: process.env.AWS_REGION || 'eu-central-1' // Use environment variable with fallback
});
const dynamodb = DynamoDBDocumentClient.from(client, {
    marshallOptions: { removeUndefinedValues: true }
});

/**
 * Check if user has permission to access project data
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} projectId - Project ID
 * @throws {Error} - If user doesn't have permission
 */
async function checkProjectAccess(userGroups, projectId) {
    const tableName = getTableName();
    
    // Check if user is admin
    if (isAdmin(userGroups)) {
        return; // Admins have access to all projects
    }

    // Get project details
    const project = await getProjectById(dynamodb, tableName, projectId);
    if (!project) {
        throw new Error("Project not found");
    }

    // Check if user is in manager or operator groups
    const isManager = hasRoles(userGroups, project.managerGroups);
    const isOperator = hasRoles(userGroups, project.operatorGroups);

    // If not authorized, throw error
    if (!isManager && !isOperator) {
        throw new Error("Access denied: You do not have permission to access dashboard data for this project");
    }
}

/**
 * Get the DynamoDB table name from environment variables
 * @return {string} Table name
 * @throws {Error} If TABLE_NAME is not set
 */
function getTableName() {
    const tableName = process.env.TABLE_NAME;
    if (!tableName) throw new Error('TABLE_NAME environment variable is not set');
    return tableName;
}

/**
 * Create base query parameters for project-related queries
 * @param {string} projectId - Project ID to query
 * @param {Object} options - Additional query options
 * @return {Object} DynamoDB query parameters
 */
function createProjectQueryParams(projectId, options = {}) {
    const params = {
        TableName: getTableName(),
        IndexName: "projectObjectId-index",
        KeyConditionExpression: "#projectObjectId = :projectId",
        ExpressionAttributeNames: {
            "#projectObjectId": "projectObjectId"
        },
        ExpressionAttributeValues: {
            ":projectId": projectId
        }
    };

    // Add projection if specified
    if (options.projection) {
        params.ProjectionExpression = options.projection;
    }

    // Add filter expression if specified
    if (options.filterExpression) {
        params.FilterExpression = options.filterExpression;
    }

    // Merge additional expression attribute names
    if (options.expressionAttributeNames) {
        params.ExpressionAttributeNames = {
            ...params.ExpressionAttributeNames,
            ...options.expressionAttributeNames
        };
    }

    // Merge additional expression attribute values
    if (options.expressionAttributeValues) {
        params.ExpressionAttributeValues = {
            ...params.ExpressionAttributeValues,
            ...options.expressionAttributeValues
        };
    }

    return params;
}

/**
 * Execute a query for a project with common error handling and validation
 * @param {string} projectId - Project ID to query
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {Object} queryParams - DynamoDB query parameters
 * @param {Function} [processResults] - Optional function to process results
 * @return {Array|Object} Query results or processed results
 */
async function executeProjectQuery(projectId, userGroups, queryParams, processResults = null) {
    // Validate inputs
    if (!projectId) throw new Error('projectId is required');

    // Check if user has access to this project
    await checkProjectAccess(userGroups, projectId);

    try {
        const response = await dynamodb.send(new QueryCommand(queryParams));
        const items = response.Items || [];
        
        // Process results if a processing function is provided
        return processResults ? processResults(items) : items;
    } catch (error) {
        console.error('Error executing project query:', error);
        throw error;
    }
}

/**
 * Group items by a date field and count occurrences
 * @param {Array} items - Array of items to group
 * @param {string} dateField - Field containing the date to group by
 * @return {Array} Array of {date, count} objects
 */
function groupByDate(items, dateField) {
    if (!items || !Array.isArray(items)) return [];
    
    // Group by day
    const countsByDay = {};

    items.forEach(item => {
        if (item[dateField]) {
            // Extract just the date part (YYYY-MM-DD)
            const dateOnly = item[dateField].split('T')[0];
            countsByDay[dateOnly] = (countsByDay[dateOnly] || 0) + 1;
        }
    });

    // Convert to array of {date, count} objects
    return Object.entries(countsByDay).map(([date, count]) => ({
        date,
        count
    }));
}

/**
 * Count items by their status field
 * @param {Array} items - Array of items to count
 * @return {Array} Array of {status, count} objects
 */
function countByStatus(items) {
    if (!items || !Array.isArray(items)) return [];

    // First build counts as an object
    const countsObj = items.reduce((counts, item) => {
        const status = item.status || 'unknown';
        counts[status] = (counts[status] || 0) + 1;
        return counts;
    }, {});

    // Convert to array of {status, count} objects
    return Object.entries(countsObj).map(([status, count]) => ({
        status,
        count
    }));
}

/**
 * Count items by their status and discipline fields
 * @param {Array} items - Array of items to count
 * @return {Array} Array of {status, discipline, count} objects
 */
function countByStatusAndDiscipline(items) {
    if (!items || !Array.isArray(items)) return [];

    // First build counts as an object with status-discipline keys
    const countsMap = new Map();
    
    items.forEach(item => {
        const status = item.status || 'unknown';
        const discipline = item.discipline || 'unknown';
        
        const key = `${status}:${discipline}`;
        countsMap.set(key, (countsMap.get(key) || 0) + 1);
    });
    
    // Convert to array of {status, discipline, count} objects
    const result = [];
    countsMap.forEach((count, key) => {
        const [status, discipline] = key.split(':');
        result.push({
            status,
            discipline,
            count
        });
    });
    
    return result;
}

/**
 * Get count of items by status for a specific project
 * @param {string} projectId - Project ID to query
 * @param {Array<string>} userGroups - User groups for authorization
 * @return {Object} Counts by status
 */
async function getCountByStatusForProject(projectId, userGroups) {
    const queryParams = createProjectQueryParams(projectId, {
        projection: "#status",
        expressionAttributeNames: {
            "#status": "status"
        }
    });

    return executeProjectQuery(projectId, userGroups, queryParams, countByStatus);
}

/**
 * Get count of activities grouped by status and discipline for a specific project
 * @param {string} projectId - Project ID to query
 * @param {Array<string>} userGroups - User groups for authorization
 * @return {Array} Array of {status, discipline, count} objects
 */
async function getActivityCountGroupedByStatusAndDiscipline(projectId, userGroups) {
    const queryParams = createProjectQueryParams(projectId, {
        projection: "#status, #discipline",
        expressionAttributeNames: {
            "#status": "status",
            "#discipline": "discipline"
        }
    });

    return executeProjectQuery(projectId, userGroups, queryParams, countByStatusAndDiscipline);
}

/**
 * Get count of completed activities grouped by day using actualFinishDate
 * @param {string} projectId - Project ID to query
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {number} [maxItems=1000] - Maximum items to retrieve (kept for API compatibility)
 * @return {Array} Array of {date, count} objects
 */
async function getCompletedActivitiesByDay(projectId, userGroups, maxItems = 1000) {
    const queryParams = createProjectQueryParams(projectId, {
        filterExpression: "#status = :status AND attribute_exists(#actualFinishDate)",
        projection: "#actualFinishDate",
        expressionAttributeNames: {
            "#status": "status",
            "#actualFinishDate": "actualFinishDate"
        },
        expressionAttributeValues: {
            ":status": "COMPLETED"
        }
    });

    return executeProjectQuery(
        projectId, 
        userGroups, 
        queryParams, 
        items => groupByDate(items, 'actualFinishDate')
    );
}

/**
 * Get count of all activities grouped by day using plannedFinishDate
 * @param {string} projectId - Project ID to query
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {number} [maxItems=1000] - Maximum items to retrieve (kept for API compatibility)
 * @return {Array} Array of {date, count} objects
 */
async function getPlannedActivitiesByDay(projectId, userGroups, maxItems = 1000) {
    const queryParams = createProjectQueryParams(projectId, {
        filterExpression: "attribute_exists(#plannedFinishDate)",
        projection: "#plannedFinishDate",
        expressionAttributeNames: {
            "#plannedFinishDate": "plannedFinishDate"
        }
    });

    return executeProjectQuery(
        projectId, 
        userGroups, 
        queryParams, 
        items => groupByDate(items, 'plannedFinishDate')
    );
}

/**
 * Get count of activities where unableToWork is true for a specific project
 * @param {string} projectId - Project ID to query
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {number} [maxItems=1000] - Maximum items to retrieve (kept for API compatibility)
 * @return {number} Count of unable to work activities
 */
async function getCountOfUnableToWorkActivities(projectId, userGroups, maxItems = 1000) {
    const queryParams = createProjectQueryParams(projectId, {
        filterExpression: "#unableToWork = :unableToWork",
        projection: "#unableToWork",
        expressionAttributeNames: {
            "#unableToWork": "unableToWork"
        },
        expressionAttributeValues: {
            ":unableToWork": true
        }
    });

    return executeProjectQuery(
        projectId, 
        userGroups, 
        queryParams, 
        items => items.length
    );
}

module.exports = {
    getCountByStatusForProject,
    getCompletedActivitiesByDay,
    getPlannedActivitiesByDay,
    getCountOfUnableToWorkActivities,
    getActivityCountGroupedByStatusAndDiscipline
};
