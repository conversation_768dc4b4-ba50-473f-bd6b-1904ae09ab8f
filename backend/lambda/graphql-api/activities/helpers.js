/**
 * Internal helper functions for activity operations
 */
const { getItem } = require('../utils/dynamodb');
const { isAdmin, hasRoles } = require('../utils/permissions');

/**
 * Get an activity by its ID
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} id - Activity ID
 * @returns {Promise<Object>} - The activity or undefined if not found
 */
async function getActivityById(dynamodb, tableName, id) {
    return await getItem(dynamodb, tableName, `ACTIVITY#${id}`, `METADATA#${id}`);
}

/**
 * Get sync status for an activity
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} id - Activity ID
 * @returns {Promise<Object>} - The sync status or undefined if not found
 */
async function getActivitySyncStatus(dynamodb, tableName, id) {
    return await getItem(dynamodb, tableName, `ACTIVITY#${id}`, `SYNC`);
}

/**
 * Get a project by its ID
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} projectId - Project ID
 * @returns {Promise<Object>} - The project or undefined if not found
 */
async function getProjectById(dynamodb, tableName, projectId) {
    return await getItem(dynamodb, tableName, `PROJECT#${projectId}`, `METADATA#${projectId}`);
}

/**
 * Get a work package by its ID
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} workPackageId - Work Package ID
 * @returns {Promise<Object>} - The work package or undefined if not found
 */
async function getWorkPackageById(dynamodb, tableName, workPackageId) {
    return await getItem(dynamodb, tableName, `WORKPACKAGE#${workPackageId}`, `METADATA#${workPackageId}`);
}

/**
 * Check user permissions for a project and return user roles
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {Object} project - The project object
 * @returns {Object} - Object containing user role flags
 * @throws {Error} - If user has no access to the project
 */
function checkProjectPermissions(userGroups, project) {
    if (!project) {
        throw new Error("Project not found");
    }
    
    // Check user permissions
    const isUserAdmin = isAdmin(userGroups);
    const isManager = hasRoles(userGroups, project.managerGroups);
    const isOperator = hasRoles(userGroups, project.operatorGroups);
    const isWorker = hasRoles(userGroups, project.workerGroups);
    
    // If user has no access to this project
    if (!(isUserAdmin || isManager || isOperator || isWorker)) {
        throw new Error("Access denied: You do not have permission to access activities for this project");
    }
    
    // Check if user is only a worker (and not admin/manager/operator)
    const isOnlyWorker = isWorker && !(isUserAdmin || isManager || isOperator);
    
    return {
        isUserAdmin,
        isManager,
        isOperator,
        isWorker,
        isOnlyWorker
    };
}

/**
 * Build base query parameters for activities by project
 * @param {string} tableName - DynamoDB table name
 * @param {string} projectId - Project ID
 * @returns {Object} - Base query parameters
 */
function buildBaseQueryParams(tableName, projectId) {
    return {
        TableName: tableName,
        IndexName: "projectObjectId-index",
        KeyConditionExpression: "#projectObjectId = :projectId",
        ExpressionAttributeNames: {
            "#projectObjectId": "projectObjectId"
        },
        ExpressionAttributeValues: { 
            ":projectId": projectId 
        }
    };
}

/**
 * Build filter expressions for worker-specific filtering
 * @param {Array<string>} userDisciplines - User's disciplines
 * @param {Array<string>} userEquipments - User's equipment types
 * @param {Array<string>} contractorNumbers - User's contractor numbers
 * @param {Object} params - Query parameters to modify
 * @returns {Array<string>} - Array of filter expressions
 */
function buildWorkerFilterExpressions(userDisciplines, userEquipments, contractorNumbers, params) {
    const filterExpressions = [];
    
    // Add discipline filter if user has disciplines
    if (userDisciplines.length > 0) {
        params.ExpressionAttributeNames["#discipline"] = "discipline";
        
        // Create a list of discipline condition expressions
        const disciplineConditions = userDisciplines.map((discipline, index) => {
            const placeholder = `:discipline${index}`;
            params.ExpressionAttributeValues[placeholder] = discipline;
            return `#discipline = ${placeholder}`;
        });
        
        // Join conditions with OR
        filterExpressions.push(`(${disciplineConditions.join(" OR ")})`);
    }
    
    // Add equipment filter if user has equipments
    if (userEquipments.length > 0) {
        params.ExpressionAttributeNames["#equipment"] = "equipment";
        
        // Create a list of equipment condition expressions
        const equipmentConditions = userEquipments.map((equipment, index) => {
            const placeholder = `:equipment${index}`;
            params.ExpressionAttributeValues[placeholder] = equipment;
            return `#equipment = ${placeholder}`;
        });
        
        // Join conditions with OR
        filterExpressions.push(`(${equipmentConditions.join(" OR ")})`);
    }
    
    // Add contractor filter if user has contractors
    if (contractorNumbers.length > 0) {
        params.ExpressionAttributeNames["#contractor"] = "contractor";
        
        // Create a list of contractor condition expressions
        const contractorConditions = contractorNumbers.map((contractorNumber, index) => {
            const placeholder = `:contractor${index}`;
            params.ExpressionAttributeValues[placeholder] = contractorNumber;
            return `#contractor = ${placeholder}`;
        });
        
        // Join conditions with OR
        filterExpressions.push(`(${contractorConditions.join(" OR ")})`);
    }
    
    return filterExpressions;
}

/**
 * Apply worker-specific filters to query parameters
 * @param {Object} params - Query parameters to modify
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} username - Username from identity
 * @returns {Promise<void>} - Resolves when filters are applied
 */
async function applyWorkerFilters(params, dynamodb, tableName, username) {
    try {
        // Get the user's full information including disciplines and equipments
        const { getUserByUsername } = require('../users/helpers');
        
        const user = await getUserByUsername(dynamodb, tableName, username);
        if (!user) {
            throw new Error("User not found");
        }
        
        // Get user's disciplines and equipmentTypes
        const userDisciplines = user.disciplines || [];
        const userEquipments = user.equipments || [];
        
        // Get user's contractors
        const { getContractorsForUser } = require('../contractors/helpers');
        const contractors = await getContractorsForUser(dynamodb, tableName, username);
        
        // Get contractor numbers from user's contractors
        const contractorNumbers = contractors && contractors.length > 0 
            ? contractors.map(contractor => contractor.contractorNumber)
            : [];
        
        // Build filter expressions
        const filterExpressions = buildWorkerFilterExpressions(
            userDisciplines, 
            userEquipments, 
            contractorNumbers, 
            params
        );
        
        // Combine all filter expressions with AND
        if (filterExpressions.length > 0) {
            params.FilterExpression = filterExpressions.join(" AND ");
            console.log("Worker-specific filter applied:", params.FilterExpression);
        } else {
            console.log("Worker has no disciplines, equipments, or contractors - no additional filtering applied");
        }
    } catch (error) {
        console.error("Error applying worker-specific filters:", error);
        // Continue without worker-specific filtering if there's an error
    }
}

/**
 * Handle pagination token in query parameters
 * @param {Object} params - Query parameters to modify
 * @param {string} nextToken - Pagination token
 * @throws {Error} - If pagination token format is invalid
 */
function handlePaginationToken(params, nextToken) {
    if (nextToken) {
        try {
            params.ExclusiveStartKey = typeof nextToken === 'string' ? JSON.parse(nextToken) : nextToken;
        } catch (error) {
            console.error('Error parsing nextToken:', error);
            throw new Error('Invalid pagination token format');
        }
    }
}

/**
 * Format query results with pagination info
 * @param {Object} data - Query result data
 * @param {number} totalCount - Total count of items
 * @returns {Object} - Formatted result with items, nextToken, and totalCount
 */
function formatQueryResults(data, totalCount) {
    const result = {
        items: data.Items || [],
        nextToken: data.LastEvaluatedKey ? JSON.stringify(data.LastEvaluatedKey) : null
    };
    
    return {
        items: result.items,
        nextToken: result.nextToken,
        totalCount: totalCount
    };
}

module.exports = {
    getActivityById,
    getActivitySyncStatus,
    getProjectById,
    getWorkPackageById,
    checkProjectPermissions,
    buildBaseQueryParams,
    buildWorkerFilterExpressions,
    applyWorkerFilters,
    handlePaginationToken,
    formatQueryResults
};
