/**
 * Activity query operations
 */
const { isAdmin, hasRoles, userHasAccessToActivity } = require('../utils/permissions');
const { getActivityById, getProjectById } = require('./helpers');
const { queryByIndex, countByIndex, queryByPk, batchGetItems } = require('../utils/dynamodb');

/**
 * Get activities for a project with pagination
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} projectId - Project ID
 * @param {number} limit - Maximum number of items to return
 * @param {string} nextToken - Pagination token
 * @param {string} username - Username from identity
 * @returns {Promise<Object>} - Activities with pagination info
 */
async function getActivitiesForProject(dynamodb, tableName, userGroups, projectId, limit = 10, nextToken = null, username) {
    // Note: this function is called as a field-resolver of project. Thus no additional authorization is performed
    const { QueryCommand } = require('@aws-sdk/lib-dynamodb');
    const { 
        getProjectById, 
        checkProjectPermissions, 
        buildBaseQueryParams, 
        applyWorkerFilters, 
        handlePaginationToken, 
        formatQueryResults 
    } = require('./helpers');
    
    // 1. Get project and check permissions
    const project = await getProjectById(dynamodb, tableName, projectId);
    const userRoles = checkProjectPermissions(userGroups, project);
    
    // 2. Build base query params for activities by project ID
    const params = buildBaseQueryParams(tableName, projectId);
    
    // 3. Apply worker-specific filters if needed
    if (userRoles.isOnlyWorker && username) {
        await applyWorkerFilters(params, dynamodb, tableName, username);
    }
    
    // 4. Handle pagination token
    handlePaginationToken(params, nextToken);
    
    // 5. Log query parameters for debugging
    console.log("Query params:", JSON.stringify(params, null, 2));
    
    // 6. Execute the query
    const data = await dynamodb.send(new QueryCommand(params));
    
    // 7. Get total count using the standard count function
    const totalCount = await countByIndex(
        dynamodb, 
        tableName, 
        "projectObjectId-index", 
        "projectObjectId", 
        projectId
    );
    
    // 8. Format and return the results
    return formatQueryResults(data, totalCount);
}

/**
 * Get an activity by ID with permission checks
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} id - Activity ID
 * @param {string} username - Username from identity
 * @returns {Promise<Object>} - The activity if user has access
 * @throws {Error} - If activity not found or user doesn't have permission
 */
async function getActivity(dynamodb, tableName, userGroups, id, username) {
    // Get the activity
    const activity = await getActivityById(dynamodb, tableName, id);
    if (!activity) {
        throw new Error("Activity not found");
    }
    
    // Check if user has access to the activity
    return await userHasAccessToActivity(dynamodb, tableName, userGroups, activity, username);
}

/**
 * Internal helper function to get related activities (predecessors or successors)
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Object} activity - The activity object
 * @param {string} indexType - Type of index to query ('predecessor' or 'successor')
 * @param {string} relatedField - Field name for the related activity ID
 * @returns {Promise<Array<Object>>} - List of related activities
 * @private
 */
async function _getRelatedActivities(dynamodb, tableName, activity, indexType, relatedField) {
  const activityId = activity.id;
  
  if (!activityId) {
    console.warn('Activity ID not found in source object');
    return [];
  }
  
  try {
    // Query the appropriate index to find related activities
    const result = await queryByIndex(
      dynamodb,
      tableName,
      `${indexType}ActivityObjectId-index`,
      `${indexType}ActivityObjectId`,
      activityId
    );
    
    if (!result.items || result.items.length === 0) {
      return [];
    }
    
    // Extract related activity IDs and prepare keys for batch get
    const activityKeys = result.items
      .filter(relationship => relationship[`${relatedField}ActivityObjectId`])
      .map(relationship => ({
        PK: `ACTIVITY#${relationship[`${relatedField}ActivityObjectId`]}`,
        SK: `METADATA#${relationship[`${relatedField}ActivityObjectId`]}`
      }));
    
    if (activityKeys.length === 0) {
      return [];
    }
    
    // Fetch all activities in a single batch operation
    const activities = await batchGetItems(dynamodb, tableName, activityKeys);
    return activities;
  } catch (error) {
    console.error(`Error fetching ${indexType} activities:`, error);
    return [];
  }
}

/**
 * Get predecessor activities for an activity
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Object} activity - The activity object
 * @returns {Promise<Array<Object>>} - List of predecessor activities
 */
async function getPredecessorActivities(dynamodb, tableName, activity) {
  return _getRelatedActivities(dynamodb, tableName, activity, 'predecessor', 'successor');
}

/**
 * Get successor activities for an activity
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Object} activity - The activity object
 * @returns {Promise<Array<Object>>} - List of successor activities
 */
async function getSuccessorActivities(dynamodb, tableName, activity) {
  return _getRelatedActivities(dynamodb, tableName, activity, 'successor', 'predecessor');
}

/**
 * Get sync status for an activity
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Object} activity - The activity object
 * @returns {Promise<Object>} - The sync status or null if not found
 */
async function getSyncStatus(dynamodb, tableName, activity) {
  const { getActivitySyncStatus } = require('./helpers');
  
  if (!activity || !activity.id) {
    console.warn('Activity ID not found in source object');
    return null;
  }
  
  try {
    const syncStatus = await getActivitySyncStatus(dynamodb, tableName, activity.id);
    
    if (!syncStatus) {
      return null;
    }
    
    // Format the sync status to match the GraphQL schema
    return {
      targetSystem: syncStatus.targetSystem,
      lastSyncedAt: syncStatus.lastSyncedAt,
      syncStatus: syncStatus.syncStatus
    };
  } catch (error) {
    console.error(`Error fetching sync status for activity ${activity.id}:`, error);
    return null;
  }
}

module.exports = {
    getActivitiesForProject,
    getActivity,
    getPredecessorActivities,
    getSuccessorActivities,
    getSyncStatus
};
