/**
 * Activity mutation operations
 */
const { UpdateCommand, PutCommand, DeleteCommand } = require('@aws-sdk/lib-dynamodb');
const { SQSClient, SendMessageCommand } = require('@aws-sdk/client-sqs');
const { randomUUID } = require('crypto');
const { isAdmin, hasRoles } = require('../utils/permissions');
const { getActivityById, getProjectById } = require('./helpers');
const { createLogEvent, handleProgressUpdate, handleStatusUpdate } = require('./utils');
const { buildUpdateExpression } = require('../utils/dynamodb');
const { getUserByUsername } = require('../users/helpers');
const { workerHasAccessToActivity } = require('../utils/permissions');

/**
 * Status mapping from GraphQL enum values to P6 string values
 * P6 expects status values as human-readable strings with spaces and proper casing,
 * while our GraphQL schema uses uppercase with underscores.
 */
const statusMapping = {
  'NOT_STARTED': 'Not Started',
  'IN_PROGRESS': 'In Progress',
  'COMPLETED': 'Completed'
};

/**
 * StatusCode mapping from GraphQL enum values to P6 string values
 * Similar to status mapping, P6 expects status code values in a specific format.
 */
const statusCodeMapping = {
  'PLANNED': 'Planned',
  'ACTIVE': 'Active',
  'INACTIVE': 'Inactive',
  'WHATIF': 'What-If',
  'REQUESTED': 'Requested',
  'TEMPLATE': 'Templated'
};

// Initialize SQS client
const sqs = new SQSClient();

/**
 * Add a new activity
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {Object} input - Activity input data
 * @returns {Promise<Object>} - The created activity
 * @throws {Error} - If user doesn't have permission
 */
async function addActivity(dynamodb, tableName, userGroups, input) {
    // Only admins can add activities
    if (!isAdmin(userGroups)) {
        throw new Error("Access denied: You do not have permission to add activities");
    }
    
    const id = randomUUID();
    const timestamp = new Date().toISOString();
    
    const activity = { 
        ...input, 
        id, 
        PK: `ACTIVITY#${id}`, 
        SK: `METADATA#${id}`,
        createdAt: timestamp,
        updatedAt: timestamp
    };
    
    await dynamodb.send(new PutCommand({ 
        TableName: tableName, 
        Item: activity 
    }));
    
    return activity; 
}

/**
 * Update an activity with permission checks
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} id - Activity ID
 * @param {Object} input - Activity update data
 * @param {string} username - Username of the user making the update
 * @returns {Promise<Object>} - The updated activity
 * @throws {Error} - If activity not found or user doesn't have permission
 */
async function updateActivityInput(dynamodb, tableName, userGroups, id, input, username) {
    // Get the current activity
    const currentActivity = await getActivityById(dynamodb, tableName, id);
    if (!currentActivity) {
        throw new Error('Activity not found');
    }

    // If user is not admin, perform permission checks
    if (!isAdmin(userGroups)) {
        // Get the project to check permissions
        const project = await getProjectById(dynamodb, tableName, currentActivity.projectObjectId);
        if (!project) {
            throw new Error('Project not found');
        }

        // Check user permissions
        const isManager = hasRoles(userGroups, project.managerGroups);
        const isWorker = hasRoles(userGroups, project.workerGroups);
        
        // User must be either a manager or worker to proceed
        if (!isManager && !isWorker) {
            throw new Error('Access denied: You do not have permission to update this activity');
        }
        
        // If user is a worker, perform additional checks
        if (isWorker) {
            // Get the user's full information including disciplines and equipmentTypes
            const user = await getUserByUsername(dynamodb, tableName, username);
            if (!user) {
                throw new Error("User not found");
            }
            
            // Check if worker has access to this activity based on discipline, equipment, and contractor
            if (!(await workerHasAccessToActivity(user, currentActivity, dynamodb, tableName))) {
                throw new Error("Access denied: You do not have permission to update this activity");
            }
            
            // Check if the activity is not already completed
            if (currentActivity.percentComplete === 1) {
                throw new Error('Workers cannot modify completed activities');
            }
        }
    }

    // Add updatedAt timestamp to the input
    const updatedInput = {
        ...input,
        updatedAt: new Date().toISOString()
    };

    // Build the update expression
    const updateExpressions = [];
    const { expression, attributeNames, attributeValues } = buildUpdateExpression(updatedInput);
    
    // Handle special cases
    handleProgressUpdate(updatedInput, currentActivity, updateExpressions, attributeNames, attributeValues);
    handleStatusUpdate(updatedInput, updateExpressions, attributeNames, attributeValues);
    createLogEvent(username, updatedInput, updateExpressions, attributeNames, attributeValues, currentActivity);

    // Prepare the DynamoDB update operation
    const params = {
        TableName: tableName,
        Key: { PK: `ACTIVITY#${id}`, SK: `METADATA#${id}` },
        UpdateExpression: [expression, ...updateExpressions].join(", "),
        ExpressionAttributeNames: attributeNames,
        ExpressionAttributeValues: attributeValues,
        ReturnValues: "ALL_NEW",
    };

    // Execute the update
    const response = await dynamodb.send(new UpdateCommand(params));
    const updatedActivity = response.Attributes;
    
    // Send a message to the SQS queue for P6 integration
    try {
        console.log('sending message to p6-backend')
        const queueUrl = process.env.ACTIVITY_UPDATE_QUEUE_URL;
        if (queueUrl && updatedActivity.id) {
            // Prepare the message with the necessary data for P6 update
            const message = {
                action: 'updateActivity',
                activityId: updatedActivity.id,
                updates: {
                    // Map the relevant fields from the updated activity to P6 fields
                    PercentComplete: updatedActivity.percentComplete,
                    // Apply status mapping if status exists
                    ...(updatedActivity.status && { 
                        Status: statusMapping[updatedActivity.status] || updatedActivity.status 
                    }),
                    // Apply statusCode mapping if statusCode exists
                    ...(updatedActivity.statusCode && { 
                        StatusCode: statusCodeMapping[updatedActivity.statusCode] || updatedActivity.statusCode 
                    })
                }
            };
            

            console.log('Message to be sent to P6:', JSON.stringify(message, null, 2))
            
            // Filter out undefined values
            Object.keys(message.updates).forEach(key => {
                if (message.updates[key] === undefined) {
                    delete message.updates[key];
                }
            });
            
            // Only send the message if there are updates to send
            if (Object.keys(message.updates).length > 0) {
                await sqs.send(new SendMessageCommand({
                    QueueUrl: queueUrl,
                    MessageBody: JSON.stringify(message)
                }));
                console.log(`Activity update message sent to queue for activity ${id} with mapped status values`);
            }
        }
    } catch (error) {
        console.error('Error sending message to SQS:', error);
        // Don't throw the error, as we still want to return the updated activity
    }
    
    return updatedActivity;
}

/**
 * Update an activity (handler for GraphQL resolver)
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {Object} event - GraphQL event object
 * @returns {Promise<Object>} - The updated activity
 */
async function updateActivity(dynamodb, tableName, userGroups, event) {
    return await updateActivityInput(
        dynamodb, 
        tableName, 
        userGroups, 
        event.arguments.id, 
        event.arguments.input,
        event.identity.username || 'unknown'
    );
}

/**
 * Update multiple activities in batch
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {Object} event - GraphQL event object
 * @returns {Promise<Array<Object>>} - The updated activities
 */
async function updateActivities(dynamodb, tableName, userGroups, event) {
    const inputs = event.arguments.input;
    const username = event.identity.username || 'unknown';
    
    // Process updates in parallel
    const promises = inputs.map(item => 
        updateActivityInput(dynamodb, tableName, userGroups, item.id, item, username)
    );
    
    return await Promise.all(promises);
}

/**
 * Delete an activity
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} id - Activity ID
 * @returns {Promise<string>} - The deleted activity ID
 * @throws {Error} - If user doesn't have permission
 */
async function deleteActivity(dynamodb, tableName, userGroups, id) {
    // Only admins can delete activities
    if (!isAdmin(userGroups)) {
        throw new Error("Access denied: You do not have permission to delete activities");
    }
    
    await dynamodb.send(new DeleteCommand({
        TableName: tableName,
        Key: { PK: `ACTIVITY#${id}`, SK: `METADATA#${id}` },
    }));
    
    return id;
}

module.exports = {
    addActivity,
    updateActivityInput,
    updateActivity,
    updateActivities,
    deleteActivity
};
