/**
 * Utility functions for DynamoDB operations and activity logging
 */

function createLogEvent(username, inputFields, updateExpression, expressionAttributeNames, expressionAttributeValues, currentActivity) {
    let eventMessage = `Updated activity with fields: ${Object.keys(inputFields).join(", ")}`;

    if (inputFields.progress !== undefined) {
        const currentProgress = inputFields.progress;
        const previousProgress = currentActivity?.progress || 0;
        eventMessage = `Progress updated from ${previousProgress}% to ${currentProgress}%`;
    }

    const logEvent = {
        timestamp: new Date().toISOString(),
        event: eventMessage,
        changedBy: username,
    };

    updateExpression.push("#logs = list_append(if_not_exists(#logs, :emptyList), :newLog)");
    expressionAttributeNames["#logs"] = "logEvents";
    expressionAttributeValues[":newLog"] = [logEvent];
    expressionAttributeValues[":emptyList"] = [];
}

function handleProgressUpdate(input, currentActivity, updateExpression, expressionAttributeNames, expressionAttributeValues) {
    if (input.percentComplete) {
        updateExpression.push('#lastPercentComplete = :lastPercentComplete');
        expressionAttributeNames['#lastPercentComplete'] = 'lastPercentComplete';
        expressionAttributeValues[':lastPercentComplete'] = currentActivity?.percentComplete || 0;
    }
}

function handleStatusUpdate(input, updateExpression, expressionAttributeNames, expressionAttributeValues) {
    // If percentComplete is being updated to 1, set status to COMPLETED
    if (input.percentComplete === 1) {
        // Add status update to the update expressions
        updateExpression.push("#status = :status");
        expressionAttributeNames["#status"] = "status";
        expressionAttributeValues[":status"] = "COMPLETED";
    }

    //TODO: Only set to COMPLETE if project is configured not for Review
}

function buildUpdateExpression(input) {
    const updateExpressions = Object.keys(input).map(key => `#${key} = :${key}`);
    return {
        expression: `SET ${updateExpressions.join(", ")}`,
        attributeNames: Object.fromEntries(Object.keys(input).map(key => [`#${key}`, key])),
        attributeValues: Object.fromEntries(Object.keys(input).map(key => [`:${key}`, input[key]])),
    };
}

module.exports = {
    createLogEvent,
    handleProgressUpdate,
    handleStatusUpdate,
    buildUpdateExpression
};
