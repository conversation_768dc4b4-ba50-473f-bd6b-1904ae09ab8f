// __tests__/index.test.js

const activitiesModule = require('../index');
const queriesModule = require('../queries');
const mutationsModule = require('../mutations');

// Mock the dependent modules
jest.mock('../queries');
jest.mock('../mutations');

describe('Activities Module Index', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  test('should export all required functions', () => {
    // Verify that all expected functions are exported
    const expectedExports = [
      'getActivitiesForProject', 
      'getActivitiesForWorkPackage', 
      'getActivity',
      'addActivity',
      'updateActivity',
      'updateActivities',
      'deleteActivity'
    ];
    
    const actualExports = Object.keys(activitiesModule);
    
    expect(actualExports).toEqual(expect.arrayContaining(expectedExports));
    expect(actualExports.length).toBe(expectedExports.length);
  });

  describe('Query Functions', () => {
    test('getActivitiesForProject should forward to queries module', async () => {
      // Setup mock implementation
      const mockResult = { items: [], totalCount: 0 };
      queriesModule.getActivitiesForProject.mockResolvedValue(mockResult);
      
      // Call the function through the main index
      const result = await activitiesModule.getActivitiesForProject(
        'dynamoDB', 'tableName', ['AdminGroup'], 'project123', 10, 'nextToken'
      );
      
      // Verify correct forwarding
      expect(queriesModule.getActivitiesForProject).toHaveBeenCalledWith(
        'dynamoDB', 'tableName', ['AdminGroup'], 'project123', 10, 'nextToken'
      );
      expect(result).toBe(mockResult);
    });
    
    test('getActivitiesForWorkPackage should forward to queries module', async () => {
      // Setup mock implementation
      const mockResult = { items: [], totalCount: 0 };
      queriesModule.getActivitiesForWorkPackage.mockResolvedValue(mockResult);
      
      // Call the function
      const result = await activitiesModule.getActivitiesForWorkPackage(
        'dynamoDB', 'tableName', ['UserGroup'], 'wp123', 50, null
      );
      
      // Verify correct forwarding
      expect(queriesModule.getActivitiesForWorkPackage).toHaveBeenCalledWith(
        'dynamoDB', 'tableName', ['UserGroup'], 'wp123', 50, null
      );
      expect(result).toBe(mockResult);
    });
    
    test('getActivity should forward to queries module', async () => {
      // Setup mock implementation
      const mockActivity = { id: 'act123', title: 'Test Activity' };
      queriesModule.getActivity.mockResolvedValue(mockActivity);
      
      // Call the function
      const result = await activitiesModule.getActivity(
        'dynamoDB', 'tableName', ['AdminGroup'], 'act123'
      );
      
      // Verify correct forwarding
      expect(queriesModule.getActivity).toHaveBeenCalledWith(
        'dynamoDB', 'tableName', ['AdminGroup'], 'act123'
      );
      expect(result).toBe(mockActivity);
    });
  });

  describe('Mutation Functions', () => {
    test('addActivity should forward to mutations module', async () => {
      // Setup mock implementation
      const mockActivity = { id: 'act123', title: 'New Activity' };
      mutationsModule.addActivity.mockResolvedValue(mockActivity);
      
      const input = { title: 'New Activity', projectId: 'proj123' };
      
      // Call the function
      const result = await activitiesModule.addActivity(
        'dynamoDB', 'tableName', ['AdminGroup'], input
      );
      
      // Verify correct forwarding
      expect(mutationsModule.addActivity).toHaveBeenCalledWith(
        'dynamoDB', 'tableName', ['AdminGroup'], input
      );
      expect(result).toBe(mockActivity);
    });
    
    test('updateActivity should forward to mutations module', async () => {
      // Setup mock implementation
      const mockActivity = { id: 'act123', title: 'Updated Activity' };
      mutationsModule.updateActivity.mockResolvedValue(mockActivity);
      
      const event = {
        args: {
          id: 'act123',
          input: { title: 'Updated Activity' }
        }
      };
      
      // Call the function
      const result = await activitiesModule.updateActivity(
        'dynamoDB', 'tableName', ['AdminGroup'], event
      );
      
      // Verify correct forwarding
      expect(mutationsModule.updateActivity).toHaveBeenCalledWith(
        'dynamoDB', 'tableName', ['AdminGroup'], event
      );
      expect(result).toBe(mockActivity);
    });
    
    test('updateActivities should forward to mutations module', async () => {
      // Setup mock implementation
      const mockResult = [
        { id: 'act1', title: 'Updated 1' },
        { id: 'act2', title: 'Updated 2' }
      ];
      mutationsModule.updateActivities.mockResolvedValue(mockResult);
      
      const event = {
        arguments: {
          input: [
            { id: 'act1', title: 'Updated 1' },
            { id: 'act2', title: 'Updated 2' }
          ]
        }
      };
      
      // Call the function
      const result = await activitiesModule.updateActivities(
        'dynamoDB', 'tableName', ['AdminGroup'], event
      );
      
      // Verify correct forwarding
      expect(mutationsModule.updateActivities).toHaveBeenCalledWith(
        'dynamoDB', 'tableName', ['AdminGroup'], event
      );
      expect(result).toBe(mockResult);
    });
    
    test('deleteActivity should forward to mutations module', async () => {
      // Setup mock implementation
      mutationsModule.deleteActivity.mockResolvedValue('act123');
      
      // Call the function
      const result = await activitiesModule.deleteActivity(
        'dynamoDB', 'tableName', ['AdminGroup'], 'act123'
      );
      
      // Verify correct forwarding
      expect(mutationsModule.deleteActivity).toHaveBeenCalledWith(
        'dynamoDB', 'tableName', ['AdminGroup'], 'act123'
      );
      expect(result).toBe('act123');
    });
  });

  test('should handle errors properly', async () => {
    // Setup a mock that throws an error
    mutationsModule.updateActivity.mockRejectedValue(new Error('Test error'));
    
    // Call the function and expect it to reject
    await expect(activitiesModule.updateActivity(
      'dynamoDB', 'tableName', ['AdminGroup'], { args: { id: 'act123' } }
    )).rejects.toThrow('Test error');
  });
});
