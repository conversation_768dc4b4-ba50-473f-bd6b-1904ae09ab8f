// __tests__/mutations.test.js

const { mockClient } = require('aws-sdk-client-mock');
const { DynamoDBDocumentClient, PutCommand, UpdateCommand, DeleteCommand } = require('@aws-sdk/lib-dynamodb');
const { isAdmin, hasRoles } = require('../../utils/permissions');
const helpers = require('../helpers');
const utils = require('../utils');
const { 
  addActivity,
  updateActivityInput,
  updateActivity,
  updateActivities,
  deleteActivity
} = require('../mutations');

// Mock dependencies
jest.mock('../../utils/permissions');
jest.mock('../helpers');
jest.mock('../utils');
jest.mock('crypto', () => ({
  randomUUID: jest.fn().mockReturnValue('mocked-uuid')
}));

describe('Activity Mutations', () => {
  const mockDynamoDbClient = {
    send: jest.fn()
  };
  
  beforeEach(() => {
    mockDynamoDbClient.send.mockClear();
    jest.clearAllMocks();
  });

  describe('addActivity', () => {
    test('should add activity when user is admin', async () => {
      isAdmin.mockReturnValue(true);
      
      const mockInput = {
        title: 'New Activity',
        description: 'Activity description',
        projectObjectId: 'proj123'
      };
      
      mockDynamoDbClient.send.mockResolvedValue({});
      
      const result = await addActivity(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        mockInput
      );
      
      // Verify admin permission check
      expect(isAdmin).toHaveBeenCalledWith(['admin']);
      
      // Verify DynamoDB put operation
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: {
            TableName: 'TestTable',
            Item: {
              ...mockInput,
              id: 'mocked-uuid',
              PK: 'ACTIVITY#mocked-uuid',
              SK: 'METADATA#mocked-uuid'
            }
          }
        })
      );
      
      // Verify returned activity
      expect(result).toEqual({
        ...mockInput,
        id: 'mocked-uuid',
        PK: 'ACTIVITY#mocked-uuid',
        SK: 'METADATA#mocked-uuid'
      });
    });
    
    test('should throw error when user is not admin', async () => {
      isAdmin.mockReturnValue(false);
      
      const mockInput = {
        title: 'New Activity',
        description: 'Activity description',
        projectObjectId: 'proj123'
      };
      
      await expect(addActivity(
        mockDynamoDbClient,
        'TestTable',
        ['UserGroup'],
        mockInput
      )).rejects.toThrow('You do not have permission');
      
      // Verify DynamoDB was not called
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
  });
  
  describe('updateActivityInput', () => {
    test('should allow admin to update activity', async () => {
      isAdmin.mockReturnValue(true);
      
      const mockActivity = {
        id: 'act123',
        title: 'Original Title',
        description: 'Original description',
        projectObjectId: 'proj123'
      };
      
      const mockInput = {
        title: 'Updated Title',
        description: 'Updated description'
      };
      
      helpers.getActivityById.mockResolvedValue(mockActivity);
      
      utils.buildUpdateExpression.mockReturnValue({
        expression: 'SET #title = :title, #description = :description',
        attributeNames: { '#title': 'title', '#description': 'description' },
        attributeValues: { ':title': 'Updated Title', ':description': 'Updated description' }
      });
      
      const mockUpdatedActivity = {
        ...mockActivity,
        ...mockInput
      };
      
      mockDynamoDbClient.send.mockResolvedValue({ Attributes: mockUpdatedActivity });
      
      const result = await updateActivityInput(
        mockDynamoDbClient,
        'TestTable',
        ['AdminGroup'],
        'act123',
        mockInput,
        'admin-user'
      );
      
      // Verify getActivityById was called
      expect(helpers.getActivityById).toHaveBeenCalledWith(
        mockDynamoDbClient,
        'TestTable',
        'act123'
      );
      
      // Verify update command was sent
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            TableName: 'TestTable',
            Key: { PK: 'ACTIVITY#act123', SK: 'METADATA#act123' }
          })
        })
      );
      
      expect(result).toEqual(mockUpdatedActivity);
    });
    
    test('should allow manager to update activity', async () => {
      isAdmin.mockReturnValue(false);
      
      const mockActivity = {
        id: 'act123',
        title: 'Original Title',
        projectObjectId: 'proj123',
        percentComplete: 0.5
      };
      
      const mockProject = {
        id: 'proj123',
        managerGroups: ['ProjectManager'],
        workerGroups: ['Worker']
      };
      
      const mockInput = {
        title: 'Updated Title'
      };
      
      helpers.getActivityById.mockResolvedValue(mockActivity);
      helpers.getProjectById.mockResolvedValue(mockProject);
      
      hasRoles.mockImplementation((groups, targetGroups) => {
        if (targetGroups === mockProject.managerGroups) return true;
        return false;
      });
      
      utils.buildUpdateExpression.mockReturnValue({
        expression: 'SET #title = :title',
        attributeNames: { '#title': 'title' },
        attributeValues: { ':title': 'Updated Title' }
      });
      
      const mockUpdatedActivity = {
        ...mockActivity,
        title: 'Updated Title'
      };
      
      mockDynamoDbClient.send.mockResolvedValue({ Attributes: mockUpdatedActivity });
      
      const result = await updateActivityInput(
        mockDynamoDbClient,
        'TestTable',
        ['ProjectManager'],
        'act123',
        mockInput,
        'manager-user'
      );
      
      // Verify permission checks
      expect(isAdmin).toHaveBeenCalledWith(['ProjectManager']);
      expect(hasRoles).toHaveBeenCalledWith(['ProjectManager'], mockProject.managerGroups);
      
      expect(result).toEqual(mockUpdatedActivity);
    });
    
    test('should allow workers to update non-completed activities', async () => {
      isAdmin.mockReturnValue(false);
      
      const mockActivity = {
        id: 'act123',
        title: 'Original Title',
        projectObjectId: 'proj123',
        percentComplete: 0.5 // Not completed
      };
      
      const mockProject = {
        id: 'proj123',
        managerGroups: ['ProjectManager'],
        workerGroups: ['Worker']
      };
      
      const mockInput = {
        description: 'Worker update'
      };
      
      helpers.getActivityById.mockResolvedValue(mockActivity);
      helpers.getProjectById.mockResolvedValue(mockProject);
      
      hasRoles.mockImplementation((groups, targetGroups) => {
        if (targetGroups === mockProject.workerGroups) return true;
        return false;
      });
      
      utils.buildUpdateExpression.mockReturnValue({
        expression: 'SET #description = :description',
        attributeNames: { '#description': 'description' },
        attributeValues: { ':description': 'Worker update' }
      });
      
      const mockUpdatedActivity = {
        ...mockActivity,
        description: 'Worker update'
      };
      
      mockDynamoDbClient.send.mockResolvedValue({ Attributes: mockUpdatedActivity });
      
      const result = await updateActivityInput(
        mockDynamoDbClient,
        'TestTable',
        ['Worker'],
        'act123',
        mockInput,
        'worker-user'
      );
      
      expect(result).toEqual(mockUpdatedActivity);
    });
    
    test('should prevent workers from updating completed activities', async () => {
      isAdmin.mockReturnValue(false);
      
      const mockActivity = {
        id: 'act123',
        title: 'Original Title',
        projectObjectId: 'proj123',
        percentComplete: 1 // Completed activity
      };
      
      const mockProject = {
        id: 'proj123',
        managerGroups: ['ProjectManager'],
        workerGroups: ['Worker']
      };
      
      const mockInput = {
        description: 'Worker update'
      };
      
      helpers.getActivityById.mockResolvedValue(mockActivity);
      helpers.getProjectById.mockResolvedValue(mockProject);
      
      hasRoles.mockImplementation((groups, targetGroups) => {
        if (targetGroups === mockProject.workerGroups) return true;
        return false;
      });
      
      await expect(updateActivityInput(
        mockDynamoDbClient,
        'TestTable',
        ['Worker'],
        'act123',
        mockInput,
        'worker-user'
      )).rejects.toThrow('Workers cannot modify completed activities');
      
      // Verify update was not attempted
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
    
    test('should throw error when activity not found', async () => {
      isAdmin.mockReturnValue(false);
      helpers.getActivityById.mockResolvedValue(null);
      
      await expect(updateActivityInput(
        mockDynamoDbClient,
        'TestTable',
        ['UserGroup'],
        'nonexistent',
        { title: 'Update' },
        'user'
      )).rejects.toThrow('Activity not found');
    });
    
    test('should throw error when project not found', async () => {
      isAdmin.mockReturnValue(false);
      
      const mockActivity = {
        id: 'act123',
        title: 'Original Title',
        projectObjectId: 'proj123'
      };
      
      helpers.getActivityById.mockResolvedValue(mockActivity);
      helpers.getProjectById.mockResolvedValue(null);
      
      await expect(updateActivityInput(
        mockDynamoDbClient,
        'TestTable',
        ['UserGroup'],
        'act123',
        { title: 'Update' },
        'user'
      )).rejects.toThrow('Project not found');
    });
    
    test('should throw error when user has no permission', async () => {
      isAdmin.mockReturnValue(false);
      
      const mockActivity = {
        id: 'act123',
        title: 'Original Title',
        projectObjectId: 'proj123'
      };
      
      const mockProject = {
        id: 'proj123',
        managerGroups: ['ProjectManager'],
        workerGroups: ['Worker']
      };
      
      helpers.getActivityById.mockResolvedValue(mockActivity);
      helpers.getProjectById.mockResolvedValue(mockProject);
      
      // User is neither manager nor worker
      hasRoles.mockReturnValue(false);
      
      await expect(updateActivityInput(
        mockDynamoDbClient,
        'TestTable',
        ['Unauthorized'],
        'act123',
        { title: 'Update' },
        'user'
      )).rejects.toThrow('You do not have permission to update this activity');
    });
  });
  
  describe('updateActivity', () => {
    // test('should call updateActivityInput with correct parameters', async () => {
    //   const mockEvent = {
    //     args: {
    //       id: 'act123',
    //       input: { title: 'Updated Title' },
    //       username: 'test-user'
    //     }
    //   };
      
    //   const mockUpdatedActivity = {
    //     id: 'act123',
    //     title: 'Updated Title'
    //   };
      
    //   // Mock the updateActivityInput function directly
    //   const originalUpdateActivityInput = require('../mutations').updateActivityInput;
    //   const mockUpdateActivityInput = jest.fn().mockResolvedValue(mockUpdatedActivity);
    //   require('../mutations').updateActivityInput = mockUpdateActivityInput;
      
    //   const result = await updateActivity(
    //     mockDynamoDbClient,
    //     'TestTable',
    //     ['UserGroup'],
    //     mockEvent
    //   );
      
    //   expect(mockUpdateActivityInput).toHaveBeenCalledWith(
    //     mockDynamoDbClient,
    //     'TestTable',
    //     ['UserGroup'],
    //     'act123',
    //     { title: 'Updated Title' },
    //     'test-user'
    //   );
      
    //   expect(result).toEqual(mockUpdatedActivity);
      
    //   // Restore the original function
    //   require('../mutations').updateActivityInput = originalUpdateActivityInput;
    // });
  });
  
  describe('updateActivities', () => {
    // test('should update multiple activities', async () => {
    //   const mockEvent = {
    //     arguments: {
    //       input: [
    //         { id: 'act1', title: 'Updated Title 1' },
    //         { id: 'act2', description: 'Updated Description 2' }
    //       ],
    //       username: 'test-user'
    //     }
    //   };
      
    //   const mockUpdatedActivities = [
    //     { id: 'act1', title: 'Updated Title 1' },
    //     { id: 'act2', description: 'Updated Description 2' }
    //   ];
      
    //   // Mock the updateActivityInput function
    //   const originalUpdateActivityInput = require('../mutations').updateActivityInput;
    //   const mockUpdateActivityInput = jest.fn()
    //     .mockResolvedValueOnce(mockUpdatedActivities[0])
    //     .mockResolvedValueOnce(mockUpdatedActivities[1]);
    //   require('../mutations').updateActivityInput = mockUpdateActivityInput;
      
    //   const result = await updateActivities(
    //     mockDynamoDbClient,
    //     'TestTable',
    //     ['admin'],
    //     mockEvent
    //   );
      
    //   // Verify updateActivityInput was called for each item
    //   expect(mockUpdateActivityInput).toHaveBeenCalledTimes(2);
    //   expect(mockUpdateActivityInput).toHaveBeenCalledWith(
    //     mockDynamoDbClient,
    //     'TestTable',
    //     ['UserGroup'],
    //     'act1',
    //     { title: 'Updated Title 1' },
    //     'test-user'
    //   );
    //   expect(mockUpdateActivityInput).toHaveBeenCalledWith(
    //     mockDynamoDbClient,
    //     'TestTable',
    //     ['UserGroup'],
    //     'act2',
    //     { description: 'Updated Description 2' },
    //     'test-user'
    //   );
      
    //   // Verify all results are returned
    //   expect(result).toEqual(mockUpdatedActivities);
      
    //   // Restore the original function
    //   require('../mutations').updateActivityInput = originalUpdateActivityInput;
    // });
  });
  
  describe('deleteActivity', () => {
    test('should delete activity when user is admin', async () => {
      isAdmin.mockReturnValue(true);
      mockDynamoDbClient.send.mockResolvedValue({});
      
      const result = await deleteActivity(
        mockDynamoDbClient,
        'TestTable',
        ['AdminGroup'],
        'act123'
      );
      
      // Verify admin check
      expect(isAdmin).toHaveBeenCalledWith(['AdminGroup']);
      
      // Verify delete operation
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: {
            TableName: 'TestTable',
            Key: { PK: 'ACTIVITY#act123', SK: 'METADATA#act123' }
          }
        })
      );
      
      // Verify ID is returned
      expect(result).toBe('act123');
    });
    
    test('should throw error when user is not admin', async () => {
      isAdmin.mockReturnValue(false);
      
      await expect(deleteActivity(
        mockDynamoDbClient,
        'TestTable',
        ['UserGroup'],
        'act123'
      )).rejects.toThrow('You do not have permission');
      
      // Verify delete was not attempted
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
  });
});
