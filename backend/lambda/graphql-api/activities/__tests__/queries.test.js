// __tests__/queries.test.js

const { mockClient } = require('aws-sdk-client-mock');
const { DynamoDBDocumentClient, QueryCommand } = require('@aws-sdk/lib-dynamodb');
const { isAdmin, hasRoles } = require('../../utils/permissions');
const helpers = require('../helpers');
const { 
  getActivitiesForProject,
  getActivitiesForWorkPackage,
  getActivity
} = require('../queries');

// Mock dependencies
jest.mock('../../utils/permissions');
jest.mock('../helpers');
jest.mock('../../utils/dynamodb');
jest.mock('../../contractors/helpers');
jest.mock('../../users/helpers');

describe('Activity Queries', () => {
  const ddbMock = mockClient(DynamoDBDocumentClient);

  // Create a wrapper object that has a send method
  const mockDynamoDbClient = {
    send: jest.fn()
  };
  
  beforeEach(() => {
    ddbMock.reset();
    mockDynamoDbClient.send.mockClear();
    jest.clearAllMocks();
    
    // Default mock for getProjectById
    helpers.getProjectById.mockResolvedValue({
      id: 'proj123',
      managerGroups: ['ProjectManager'],
      operatorGroups: ['Operator'],
      workerGroups: ['Worker']
    });
  });

  describe('getActivitiesForProject', () => {
    test('should retrieve activities for a project without pagination', async () => {
        const mockActivities = [
          { id: 'act1', title: 'Activity 1', projectObjectId: 'proj123' },
          { id: 'act2', title: 'Activity 2', projectObjectId: 'proj123' }
        ];
  
        // Mock DynamoDB responses
        mockDynamoDbClient.send
                .mockResolvedValueOnce({ Items: mockActivities, LastEvaluatedKey: null })
                .mockResolvedValueOnce({ Count: 2 });
        
        // Mock role checks
        isAdmin.mockReturnValue(false);
        hasRoles.mockImplementation((groups, targetGroups) => {
          if (Array.isArray(targetGroups) && targetGroups.includes('ProjectManager')) return true;
          return false;
        });
  
        // Pass our wrapper mock client
        const result = await getActivitiesForProject(
          mockDynamoDbClient,  // Use the wrapper with send method
          'TestTable',
          ['ProjectManager'],
          'proj123'
        );
  
        // Rest of the test remains the same
        expect(result).toEqual({
          items: mockActivities,
          nextToken: null,
          totalCount: 2
        });
        
        // Verify the query was made with the correct parameters
        expect(mockDynamoDbClient.send).toHaveBeenCalledWith(expect.objectContaining({
          input: expect.objectContaining({
            TableName: 'TestTable',
            IndexName: 'projectObjectId-index',
            KeyConditionExpression: '#projectObjectId = :projectId'
          })
        }));
      });

    test('should handle pagination with nextToken', async () => {
      const mockActivities = [
        { id: 'act3', title: 'Activity 3', projectObjectId: 'proj123' }
      ];

      const lastEvaluatedKey = { id: 'act3' };
      const inputNextToken = JSON.stringify({ id: 'act2' }); // Previous page token

      mockDynamoDbClient.send
      .mockResolvedValueOnce({ Items: mockActivities, LastEvaluatedKey: lastEvaluatedKey })
      .mockResolvedValueOnce({ Count: 10 });
      
      // Mock role checks
      isAdmin.mockReturnValue(false);
      hasRoles.mockImplementation((groups, targetGroups) => {
        if (Array.isArray(targetGroups) && targetGroups.includes('ProjectManager')) return true;
        return false;
      });

      const result = await getActivitiesForProject(
        mockDynamoDbClient,
        'TestTable',
        ['ProjectManager'],
        'proj123',
        5, // Limit
        inputNextToken
      );

      // Verify the response contains the new nextToken
      expect(result).toEqual({
        items: mockActivities,
        nextToken: JSON.stringify(lastEvaluatedKey),
        totalCount: 10
      });
      
      // Verify the query included the ExclusiveStartKey
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(expect.objectContaining({
        input: expect.objectContaining({
          ExclusiveStartKey: { id: 'act2' }
        })
      }));
    });

    test('should handle empty results', async () => {
        mockDynamoDbClient.send
          .mockResolvedValueOnce({ Items: [], LastEvaluatedKey: null })
          .mockResolvedValueOnce({ Count: 0 });
        
        // Mock role checks
        isAdmin.mockReturnValue(true);

        const result = await getActivitiesForProject(
          mockDynamoDbClient,
          'TestTable',
          ['AdminGroup'],
          'empty-project'
        );
  
        expect(result).toEqual({
          items: [],
          nextToken: null,
          totalCount: 0
        });
      });
      
    test('should apply worker filtering with single discipline, equipment type, and contractor', async () => {
      // Mock user data
      const mockUser = {
        id: 'user123',
        username: 'worker1',
        disciplines: ['ELECTRICAL'],
        equipmentTypes: ['PUMP']
      };
      
      // Mock contractors
      const mockContractors = [
        { id: 'contractor1', name: 'Contractor A' }
      ];
      
      // Mock activities
      const mockActivities = [
        { 
          id: 'act1', 
          title: 'Activity 1', 
          projectObjectId: 'proj123',
          discipline: 'ELECTRICAL',
          equipmentType: 'PUMP',
          contractorId: 'contractor1'
        }
      ];
      
      // Set up mocks
      const { getUserByUsername } = require('../../users/helpers');
      const { getContractorsForUser } = require('../../contractors/helpers');
      
      getUserByUsername.mockResolvedValue(mockUser);
      getContractorsForUser.mockResolvedValue(mockContractors);
      
      // Mock role checks
      isAdmin.mockReturnValue(false);
      hasRoles.mockImplementation((groups, targetGroups) => {
        if (Array.isArray(targetGroups) && targetGroups.includes('Worker')) return true;
        return false;
      });
      
      // Mock DynamoDB responses
      mockDynamoDbClient.send
        .mockResolvedValueOnce({ Items: mockActivities, LastEvaluatedKey: null })
        .mockResolvedValueOnce({ Count: 1 });
      
      // Call the function
      const result = await getActivitiesForProject(
        mockDynamoDbClient,
        'TestTable',
        ['Worker'],
        'proj123',
        10,
        null,
        'worker1'
      );
      
      // Verify the result
      expect(result).toEqual({
        items: mockActivities,
        nextToken: null,
        totalCount: 1
      });
      
      // Verify the query was made with the correct filters
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(expect.objectContaining({
        input: expect.objectContaining({
          FilterExpression: expect.stringContaining('#discipline = :discipline'),
          ExpressionAttributeNames: expect.objectContaining({
            '#discipline': 'discipline',
            '#equipmentType': 'equipmentType',
            '#contractorId': 'contractorId'
          }),
          ExpressionAttributeValues: expect.objectContaining({
            ':discipline': 'ELECTRICAL',
            ':equipmentType': 'PUMP',
            ':contractorId': 'contractor1'
          })
        })
      }));
    });
    
    test('should apply worker filtering with multiple disciplines, equipment types, and contractors', async () => {
      // Mock user data
      const mockUser = {
        id: 'user123',
        username: 'worker1',
        disciplines: ['ELECTRICAL', 'MECHANICAL', 'CIVIL'],
        equipmentTypes: ['PUMP', 'VALVE', 'MOTOR']
      };
      
      // Mock contractors
      const mockContractors = [
        { id: 'contractor1', name: 'Contractor A' },
        { id: 'contractor2', name: 'Contractor B' },
        { id: 'contractor3', name: 'Contractor C' }
      ];
      
      // Mock activities
      const mockActivities = [
        { 
          id: 'act1', 
          title: 'Activity 1', 
          projectObjectId: 'proj123',
          discipline: 'ELECTRICAL',
          equipmentType: 'PUMP',
          contractorId: 'contractor1'
        },
        { 
          id: 'act2', 
          title: 'Activity 2', 
          projectObjectId: 'proj123',
          discipline: 'MECHANICAL',
          equipmentType: 'VALVE',
          contractorId: 'contractor2'
        }
      ];
      
      // Set up mocks
      const { getUserByUsername } = require('../../users/helpers');
      const { getContractorsForUser } = require('../../contractors/helpers');
      
      getUserByUsername.mockResolvedValue(mockUser);
      getContractorsForUser.mockResolvedValue(mockContractors);
      
      // Mock role checks
      isAdmin.mockReturnValue(false);
      hasRoles.mockImplementation((groups, targetGroups) => {
        if (Array.isArray(targetGroups) && targetGroups.includes('Worker')) return true;
        return false;
      });
      
      // Mock DynamoDB responses
      mockDynamoDbClient.send
        .mockResolvedValueOnce({ Items: mockActivities, LastEvaluatedKey: null })
        .mockResolvedValueOnce({ Count: 2 });
      
      // Call the function
      const result = await getActivitiesForProject(
        mockDynamoDbClient,
        'TestTable',
        ['Worker'],
        'proj123',
        10,
        null,
        'worker1'
      );
      
      // Verify the result
      expect(result).toEqual({
        items: mockActivities,
        nextToken: null,
        totalCount: 2
      });
      
      // Verify the query was made with the correct filters for multiple values
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(expect.objectContaining({
        input: expect.objectContaining({
          FilterExpression: expect.stringContaining('#discipline IN (:discipline0, :discipline1, :discipline2)'),
          ExpressionAttributeNames: expect.objectContaining({
            '#discipline': 'discipline',
            '#equipmentType': 'equipmentType',
            '#contractorId': 'contractorId'
          }),
          ExpressionAttributeValues: expect.objectContaining({
            ':discipline0': 'ELECTRICAL',
            ':discipline1': 'MECHANICAL',
            ':discipline2': 'CIVIL',
            ':equipmentType0': 'PUMP',
            ':equipmentType1': 'VALVE',
            ':equipmentType2': 'MOTOR',
            ':contractorId0': 'contractor1',
            ':contractorId1': 'contractor2',
            ':contractorId2': 'contractor3'
          })
        })
      }));
    });
    
    test('should handle worker with empty disciplines and equipment types', async () => {
      // Mock user data with empty arrays
      const mockUser = {
        id: 'user123',
        username: 'worker1',
        disciplines: [],
        equipmentTypes: []
      };
      
      // Mock contractors
      const mockContractors = [
        { id: 'contractor1', name: 'Contractor A' }
      ];
      
      // Mock activities
      const mockActivities = [
        { 
          id: 'act1', 
          title: 'Activity 1', 
          projectObjectId: 'proj123',
          discipline: 'ELECTRICAL',
          equipmentType: 'PUMP',
          contractorId: 'contractor1'
        }
      ];
      
      // Set up mocks
      const { getUserByUsername } = require('../../users/helpers');
      const { getContractorsForUser } = require('../../contractors/helpers');
      
      getUserByUsername.mockResolvedValue(mockUser);
      getContractorsForUser.mockResolvedValue(mockContractors);
      
      // Mock role checks
      isAdmin.mockReturnValue(false);
      hasRoles.mockImplementation((groups, targetGroups) => {
        if (Array.isArray(targetGroups) && targetGroups.includes('Worker')) return true;
        return false;
      });
      
      // Mock DynamoDB responses
      mockDynamoDbClient.send
        .mockResolvedValueOnce({ Items: mockActivities, LastEvaluatedKey: null })
        .mockResolvedValueOnce({ Count: 1 });
      
      // Call the function
      const result = await getActivitiesForProject(
        mockDynamoDbClient,
        'TestTable',
        ['Worker'],
        'proj123',
        10,
        null,
        'worker1'
      );
      
      // Verify the result
      expect(result).toEqual({
        items: mockActivities,
        nextToken: null,
        totalCount: 1
      });
      
      // Verify the query was made with only contractor filter (no discipline or equipment filters)
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(expect.objectContaining({
        input: expect.objectContaining({
          FilterExpression: expect.stringContaining('#contractorId = :contractorId'),
          ExpressionAttributeNames: expect.objectContaining({
            '#contractorId': 'contractorId'
          }),
          ExpressionAttributeValues: expect.objectContaining({
            ':contractorId': 'contractor1'
          })
        })
      }));
      
      // Verify discipline and equipment filters were not included
      const callInput = mockDynamoDbClient.send.mock.calls[0][0].input;
      expect(callInput.ExpressionAttributeNames).not.toHaveProperty('#discipline');
      expect(callInput.ExpressionAttributeNames).not.toHaveProperty('#equipmentType');
    });
    
    test('should return empty result for worker with no contractors', async () => {
      // Mock user data
      const mockUser = {
        id: 'user123',
        username: 'worker1',
        disciplines: ['ELECTRICAL'],
        equipmentTypes: ['PUMP']
      };
      
      // Mock empty contractors array
      const mockContractors = [];
      
      // Set up mocks
      const { getUserByUsername } = require('../../users/helpers');
      const { getContractorsForUser } = require('../../contractors/helpers');
      
      getUserByUsername.mockResolvedValue(mockUser);
      getContractorsForUser.mockResolvedValue(mockContractors);
      
      // Mock role checks
      isAdmin.mockReturnValue(false);
      hasRoles.mockImplementation((groups, targetGroups) => {
        if (Array.isArray(targetGroups) && targetGroups.includes('Worker')) return true;
        return false;
      });
      
      // Call the function
      const result = await getActivitiesForProject(
        mockDynamoDbClient,
        'TestTable',
        ['Worker'],
        'proj123',
        10,
        null,
        'worker1'
      );
      
      // Verify the result is empty without querying DynamoDB
      expect(result).toEqual({
        items: [],
        nextToken: null,
        totalCount: 0
      });
      
      // Verify no DynamoDB query was made
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
    
    test('should not apply worker filtering for user with multiple roles', async () => {
      // Mock activities
      const mockActivities = [
        { id: 'act1', title: 'Activity 1', projectObjectId: 'proj123' },
        { id: 'act2', title: 'Activity 2', projectObjectId: 'proj123' }
      ];
      
      // Mock role checks - user is both worker and manager
      isAdmin.mockReturnValue(false);
      hasRoles.mockImplementation((groups, targetGroups) => {
        // User is both in worker and manager groups
        return true;
      });
      
      // Mock DynamoDB responses
      mockDynamoDbClient.send
        .mockResolvedValueOnce({ Items: mockActivities, LastEvaluatedKey: null })
        .mockResolvedValueOnce({ Count: 2 });
      
      // Call the function
      const result = await getActivitiesForProject(
        mockDynamoDbClient,
        'TestTable',
        ['Worker', 'ProjectManager'],
        'proj123',
        10,
        null,
        'worker1'
      );
      
      // Verify the result
      expect(result).toEqual({
        items: mockActivities,
        nextToken: null,
        totalCount: 2
      });
      
      // Verify the query was made without worker-specific filters
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(expect.objectContaining({
        input: expect.objectContaining({
          TableName: 'TestTable',
          IndexName: 'projectObjectId-index',
          KeyConditionExpression: '#projectObjectId = :projectId'
        })
      }));
      
      // Verify no worker-specific filters were added
      const callInput = mockDynamoDbClient.send.mock.calls[0][0].input;
      expect(callInput.FilterExpression).toBeUndefined();
    });
    
    test('should throw error when worker username is not provided', async () => {
      // Mock role checks
      isAdmin.mockReturnValue(false);
      hasRoles.mockImplementation((groups, targetGroups) => {
        if (Array.isArray(targetGroups) && targetGroups.includes('Worker')) return true;
        return false;
      });
      
      // Call the function without username
      await expect(getActivitiesForProject(
        mockDynamoDbClient,
        'TestTable',
        ['Worker'],
        'proj123',
        10,
        null,
        null // No username provided
      )).rejects.toThrow('Username not provided');
      
      // Verify no DynamoDB query was made
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
    
    test('should throw error when worker user is not found', async () => {
      // Set up mocks
      const { getUserByUsername } = require('../../users/helpers');
      getUserByUsername.mockResolvedValue(null); // User not found
      
      // Mock role checks
      isAdmin.mockReturnValue(false);
      hasRoles.mockImplementation((groups, targetGroups) => {
        if (Array.isArray(targetGroups) && targetGroups.includes('Worker')) return true;
        return false;
      });
      
      // Call the function
      await expect(getActivitiesForProject(
        mockDynamoDbClient,
        'TestTable',
        ['Worker'],
        'proj123',
        10,
        null,
        'nonexistent-user'
      )).rejects.toThrow('User not found');
      
      // Verify no DynamoDB query was made
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
  });

    describe('getActivity', () => {
        test('should allow admin to get activity directly', async () => {
          isAdmin.mockReturnValue(true);
          const mockActivity = { 
            id: 'act123', 
            title: 'Test Activity',
            projectId: 'proj123' 
          };
    
          helpers.getActivityById.mockResolvedValue(mockActivity);
    
          const result = await getActivity(
            mockDynamoDbClient,
            'TestTable',
            ['AdminGroup'],
            'act123'
          );
    
          expect(isAdmin).toHaveBeenCalledWith(['AdminGroup']);
          expect(helpers.getActivityById).toHaveBeenCalledWith(
            mockDynamoDbClient,
            'TestTable',
            'act123'
          );
          expect(helpers.getProjectById).not.toHaveBeenCalled();
          expect(result).toEqual(mockActivity);
        });
    
        test('should throw error when activity not found', async () => {
          isAdmin.mockReturnValue(false);
          helpers.getActivityById.mockResolvedValue(null);
    
          await expect(getActivity(
            mockDynamoDbClient,
            'TestTable',
            ['UserGroup'],
            'nonexistent'
          )).rejects.toThrow('Activity not found');
        });
    
        test('should throw error when project not found', async () => {
          isAdmin.mockReturnValue(false);
          const mockActivity = { 
            id: 'act123',
            projectId: 'proj456'
          };
          
          helpers.getActivityById.mockResolvedValue(mockActivity);
          helpers.getProjectById.mockResolvedValue(null);
    
          await expect(getActivity(
            mockDynamoDbClient,
            'TestTable',
            ['UserGroup'],
            'act123'
          )).rejects.toThrow('Project not found');
        });
    
        test('should allow access for user in manager role', async () => {
          isAdmin.mockReturnValue(false);
          const mockActivity = { 
            id: 'act123', 
            projectId: 'proj123' 
          };
          const mockProject = {
            id: 'proj123',
            managerGroups: ['ProjectManager'],
            operatorGroups: ['Operator'],
            workerGroups: ['Worker']
          };
    
          helpers.getActivityById.mockResolvedValue(mockActivity);
          helpers.getProjectById.mockResolvedValue(mockProject);
          
          hasRoles.mockImplementation((groups, targetGroups) => {
            if (Array.isArray(targetGroups) && targetGroups.includes('ProjectManager')) return true;
            return false;
          });
    
          const result = await getActivity(
            mockDynamoDbClient,
            'TestTable',
            ['ProjectManager'],
            'act123'
          );
    
          expect(result).toEqual(mockActivity);
        });
    
        test('should allow access for user in worker role', async () => {
          isAdmin.mockReturnValue(false);
          const mockActivity = { 
            id: 'act123', 
            projectId: 'proj123' 
          };
          const mockProject = {
            id: 'proj123',
            managerGroups: ['ProjectManager'],
            operatorGroups: ['Operator'],
            workerGroups: ['Worker']
          };
    
          helpers.getActivityById.mockResolvedValue(mockActivity);
          helpers.getProjectById.mockResolvedValue(mockProject);
          
          hasRoles.mockImplementation((groups, targetGroups) => {
            if (Array.isArray(targetGroups) && targetGroups.includes('Worker')) return true;
            return false;
          });
    
          const result = await getActivity(
            mockDynamoDbClient,
            'TestTable',
            ['Worker'],
            'act123'
          );
    
          expect(result).toEqual(mockActivity);
        });
    });
});
