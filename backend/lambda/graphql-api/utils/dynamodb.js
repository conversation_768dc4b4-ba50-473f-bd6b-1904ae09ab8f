/**
 * Common utility functions for DynamoDB operations
 */
const { GetCommand, Query<PERSON>ommand, ScanCommand, BatchGetCommand } = require('@aws-sdk/lib-dynamodb');

/**
 * Get an item by its primary key
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} pk - Partition key value
 * @param {string} sk - Sort key value
 * @returns {Promise<Object>} - The item or undefined if not found
 */
async function getItem(dynamodb, tableName, pk, sk) {
    const params = {
        TableName: tableName,
        Key: { PK: pk, SK: sk }
    };
    const response = await dynamodb.send(new GetCommand(params));
    return response.Item;
}

/**
 * Query items by partition key
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} pk - Partition key value
 * @param {Object} options - Additional query options
 * @returns {Promise<Object>} - Query results with items and pagination token
 */
async function queryByPk(dynamodb, tableName, pk, options = {}) {
    const { limit = 50, nextToken, filterExpression, expressionAttributeNames, expressionAttributeValues } = options;
    
    const params = {
        TableName: tableName,
        KeyConditionExpression: 'PK = :pk',
        ExpressionAttributeValues: { ':pk': pk, ...expressionAttributeValues },
        Limit: limit
    };

    if (filterExpression) {
        params.FilterExpression = filterExpression;
    }

    if (expressionAttributeNames) {
        params.ExpressionAttributeNames = expressionAttributeNames;
    }

    if (nextToken) {
        try {
            // Handle both string and object formats of nextToken
            params.ExclusiveStartKey = typeof nextToken === 'string' ? JSON.parse(nextToken) : nextToken;
        } catch (error) {
            console.error('Error parsing nextToken:', error);
            throw new Error('Invalid pagination token format');
        }
    }

    const data = await dynamodb.send(new QueryCommand(params));
    
    return {
        items: data.Items || [],
        nextToken: data.LastEvaluatedKey ? JSON.stringify(data.LastEvaluatedKey) : null,
        count: data.Count || 0
    };
}

/**
 * Query items by index
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} indexName - Index name
 * @param {string} keyName - Index key name
 * @param {string} keyValue - Index key value
 * @param {Object} options - Additional query options
 * @returns {Promise<Object>} - Query results with items and pagination token
 */
async function queryByIndex(dynamodb, tableName, indexName, keyName, keyValue, options = {}) {
    const { limit = 50, nextToken, filterExpression, expressionAttributeNames, expressionAttributeValues = {} } = options;

    const params = {
        TableName: tableName,
        IndexName: indexName,
        KeyConditionExpression: `#t = :keyValue`,
        ExpressionAttributeNames: {
            '#t': keyName
        },
        ExpressionAttributeValues: { ':keyValue': keyValue, ...expressionAttributeValues },
        Limit: limit
    };

    if (filterExpression) {
        params.FilterExpression = filterExpression;
    }

    if (expressionAttributeNames) {
        params.ExpressionAttributeNames = expressionAttributeNames;
    }

    if (nextToken) {
        try {
            // Handle both string and object formats of nextToken
            params.ExclusiveStartKey = typeof nextToken === 'string' ? JSON.parse(nextToken) : nextToken;
        } catch (error) {
            console.error('Error parsing nextToken:', error);
            throw new Error('Invalid pagination token format');
        }
    }

    const data = await dynamodb.send(new QueryCommand(params));
    
    return {
        items: data.Items || [],
        nextToken: data.LastEvaluatedKey ? JSON.stringify(data.LastEvaluatedKey) : null,
        count: data.Count || 0
    };
}

/**
 * Count items by index
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} indexName - Index name
 * @param {string} keyName - Index key name
 * @param {string} keyValue - Index key value
 * @returns {Promise<number>} - Count of items
 */
async function countByIndex(dynamodb, tableName, indexName, keyName, keyValue) {
    const params = {
        TableName: tableName,
        IndexName: indexName,
        KeyConditionExpression: `${keyName} = :keyValue`,
        ExpressionAttributeValues: { ':keyValue': keyValue },
        Select: 'COUNT'
    };

    const data = await dynamodb.send(new QueryCommand(params));
    return data.Count || 0;
}

/**
 * Build an update expression for DynamoDB
 * @param {Object} input - Input object with fields to update
 * @returns {Object} - Update expression, attribute names, and attribute values
 */
function buildUpdateExpression(input) {
    if (!input || Object.keys(input).length === 0) {
        return { expression: '', attributeNames: {}, attributeValues: {} };
    }

    // Filter out undefined values
    const definedFields = Object.entries(input)
        .filter(([_, value]) => value !== undefined)
        .map(([key, _]) => key);
    
    if (definedFields.length === 0) {
        return { expression: '', attributeNames: {}, attributeValues: {} };
    }

    const updateExpressions = definedFields.map(key => `#${key} = :${key}`);
    
    return {
        expression: `SET ${updateExpressions.join(', ')}`,
        attributeNames: Object.fromEntries(definedFields.map(key => [`#${key}`, key])),
        attributeValues: Object.fromEntries(definedFields.map(key => [`:${key}`, input[key]]))
    };
}

/**
 * Batch get items by their keys
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<Object>} keys - Array of key objects with PK and SK
 * @returns {Promise<Array<Object>>} - Array of items
 */
async function batchGetItems(dynamodb, tableName, keys) {
    if (!keys || keys.length === 0) {
        return [];
    }
    
    // DynamoDB BatchGet has a limit of 100 items per request
    const batchSize = 100;
    const results = [];
    
    // Process in batches of 100
    for (let i = 0; i < keys.length; i += batchSize) {
        const batchKeys = keys.slice(i, i + batchSize);
        
        const params = {
            RequestItems: {
                [tableName]: {
                    Keys: batchKeys
                }
            }
        };
        
        try {
            const data = await dynamodb.send(new BatchGetCommand(params));
            
            if (data.Responses && data.Responses[tableName]) {
                results.push(...data.Responses[tableName]);
            }
            
            // Handle unprocessed keys if any
            if (data.UnprocessedKeys && 
                data.UnprocessedKeys[tableName] && 
                data.UnprocessedKeys[tableName].Keys && 
                data.UnprocessedKeys[tableName].Keys.length > 0) {
                
                console.warn(`Some keys were unprocessed in batch get: ${data.UnprocessedKeys[tableName].Keys.length}`);
                
                // You could implement retry logic here for unprocessed keys
                // For simplicity, we'll just log a warning
            }
        } catch (error) {
            console.error('Error in batch get:', error);
            throw error;
        }
    }
    
    return results;
}

module.exports = {
    getItem,
    queryByPk,
    queryByIndex,
    countByIndex,
    buildUpdateExpression,
    batchGetItems
};
