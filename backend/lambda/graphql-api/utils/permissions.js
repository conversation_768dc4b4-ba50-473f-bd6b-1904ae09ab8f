const UserRoleGroup = {
    ADMIN: 'admin',
    EXECUTOR: 'executor',
    MANAGER: 'manager',
    OPERATOR: 'operator'
};

function hasRole(userGroups, role) {
    return userGroups.includes(role);
}

function hasRoles(userGroups, roles) {
    if (!userGroups || !roles) return false;
    return roles.some(role => hasRole(userGroups, role));
}

function isAdmin(userGroups) {
    return hasRole(userGroups, UserRoleGroup.ADMIN);
}

function hasAllowedGroupAccess(userGroups, allowedGroups) {
    if(allowedGroups) return allowedGroups.some(role => hasRole(userGroups, role));
    return true
}

function hasAccess(userGroups, allowedGroups) {
    return isAdmin(userGroups) || hasAllowedGroupAccess(userGroups, allowedGroups);
}

function checkBelongCompany(userGroups, companyCode) {
    if (companyCode) {
        return userGroups.includes(`Company_${companyCode}`)
    }
    return true
}

function checkBelongCompanyAndDiscipline(userGroups, companyCode, disciplineCode) {
    let result = true;
    if(companyCode) {
        result = result && userGroups.includes(`Company_${companyCode}`)
    }
    if (disciplineCode) {
        result = result && userGroups.includes(`Discipline_${disciplineCode}`)
    }
    return result;
}

function extractCompanyNames(userGroups) {
    return userGroups
        .filter(item => item.startsWith("Company_"))
        .map(item => item.replace("Company_", ""));
}

function extractDisciplineNames(userGroups) {
    return userGroups
        .filter(item => item.startsWith("Discipline_"))
        .map(item => item.replace("Discipline_", ""));
}

/**
 * Helper function to check if a worker has access to an activity based on discipline, equipment, and contractor
 * @param {Object} user - The user object
 * @param {Object} activity - The activity object
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @returns {Promise<boolean>} - True if the worker has access, false otherwise
 */
async function workerHasAccessToActivity(user, activity, dynamodb, tableName) {
    // Only allow access if activity has both discipline and equipmentType set
    if (!activity.discipline || !activity.equipment) {
        return false;
    }
    
    // Get user's disciplines and equipmentTypes
    const userDisciplines = user.disciplines || [];
    const userEquipments = user.equipments || [];
    
    // Check if user's disciplines include the activity's discipline
    const disciplineMatch = userDisciplines.length === 0 || 
        userDisciplines.includes(activity.discipline);
    
    // Check if user's equipmentTypes include the activity's equipmentType
    const equipmentMatch = userEquipments.length === 0 || 
        userEquipments.includes(activity.equipment);
    
    // If discipline and equipment don't match, no need to check contractors
    if (!disciplineMatch || !equipmentMatch) {
        return false;
    }
    
    // Get user's contractors
    const { getContractorsForUser } = require('../contractors/helpers');
    const contractors = await getContractorsForUser(dynamodb, tableName, user.username);
    
    // If user has no contractors, deny access
    if (!contractors || contractors.length === 0) {
        return false;
    }
    
    // Get contractor numbers from user's contractors
    const contractorNumbers = contractors.map(contractor => contractor.contractorNumber);
    
    // Check if activity's contractor is in user's contractors
    const contractorMatch = activity.contractor && contractorNumbers.includes(activity.contractor);
    
    return contractorMatch;
}

/**
 * Check if a user has access to an activity
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {Object} activity - The activity object
 * @param {string} username - Username from identity
 * @returns {Promise<Object>} - The activity if user has access
 * @throws {Error} - If user doesn't have permission
 */
async function userHasAccessToActivity(dynamodb, tableName, userGroups, activity, username) {
    // Admin check
    if (isAdmin(userGroups)) {
        activity.viewerCanEdit = true;
        return activity;
    }
    
    // Get project for role checks
    const { getProjectById } = require('../activities/helpers');
    const project = await getProjectById(dynamodb, tableName, activity.projectObjectId);
    if (!project) {
        throw new Error("Project not found");
    }
    
    // Check if user is manager, worker or operator
    const isManager = hasRoles(userGroups, project.managerGroups);
    const isOperator = hasRoles(userGroups, project.operatorGroups);
    const isWorker = hasRoles(userGroups, project.workerGroups);
    
    if (isManager) {
        activity.viewerCanEdit = true;
        return activity;
    }

    if (isOperator) {
        activity.viewerCanEdit = true;
        return activity;
    }
    
    if (isWorker) {
        // Get user info
        const { getUserByUsername } = require('../users/helpers');
        
        if (!username) {
            throw new Error("Username not provided");
        }
        
        const user = await getUserByUsername(dynamodb, tableName, username);
        if (!user) {
            throw new Error("User not found");
        }
        
        // Check worker access
        if (await workerHasAccessToActivity(user, activity, dynamodb, tableName)) {
            activity.viewerCanEdit = true;
            return activity;
        }
        
        throw new Error("Access denied: You do not have permission to access this activity");
    }
    
    // No access
    throw new Error("Access denied: You do not have permission to access this activity");
}

// Permission helpers with the new name
const isAdminWorkerOperatorOrManager = {
    admin: (userGroups) => userGroups.includes("admin"),
    
    forProject: (project, userGroups) => {
      if (!project) return false;
      
      // Admins can see all projects regardless of isVisible flag
      if (isAdminWorkerOperatorOrManager.admin(userGroups)) {
        return true;
      }
      
      // Non-admins can only see projects where:
      // 1. They belong to the project's groups, AND
      // 2. The project has isVisible set to true
      const userBelongsToProjectGroups = (
        project.managerGroups?.some(group => userGroups.includes(group)) ||
        project.operatorGroups?.some(group => userGroups.includes(group)) ||
        project.workerGroups?.some(group => userGroups.includes(group))
      );
      
      return userBelongsToProjectGroups && project.isVisible === true;
    }
  };

module.exports = {
    UserRoleGroup,
    hasRole,
    hasRoles,
    isAdmin,
    hasAllowedGroupAccess,
    hasAccess,
    checkBelongCompany,
    checkBelongCompanyAndDiscipline,
    extractCompanyNames,
    extractDisciplineNames,
    workerHasAccessToActivity,
    userHasAccessToActivity,
    isAdminWorkerOperatorOrManager
};
