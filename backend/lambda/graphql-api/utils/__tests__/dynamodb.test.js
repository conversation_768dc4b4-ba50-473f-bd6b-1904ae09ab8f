// __tests__/dynamodb.test.js

const { GetCommand, QueryCommand, ScanCommand, BatchGetCommand } = require('@aws-sdk/lib-dynamodb');
const {
  getItem,
  queryByPk,
  queryByIndex,
  countByIndex,
  buildUpdateExpression
} = require('../dynamodb');

describe('DynamoDB Utility Functions', () => {
  // Mock DynamoDB client
  const mockDynamoDbClient = {
    send: jest.fn()
  };
  
  beforeEach(() => {
    mockDynamoDbClient.send.mockClear();
    jest.clearAllMocks();
  });

  describe('getItem', () => {
    test('should get an item by primary key', async () => {
      // Mock response
      const mockItem = { 
        PK: 'ACTIVITY#123', 
        SK: 'METADATA#123',
        id: '123',
        title: 'Test Activity'
      };
      
      mockDynamoDbClient.send.mockResolvedValue({ Item: mockItem });
      
      // Call the function
      const result = await getItem(
        mockDynamoDbClient,
        'TestTable',
        'ACTIVITY#123',
        'METADATA#123'
      );
      
      // Verify DynamoDB GetCommand was called with correct parameters
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: {
            TableName: 'TestTable',
            Key: { PK: 'ACTIVITY#123', SK: 'METADATA#123' }
          }
        })
      );
      
      // Verify the result
      expect(result).toEqual(mockItem);
    });
    
    test('should return undefined when item not found', async () => {
      // Mock empty response
      mockDynamoDbClient.send.mockResolvedValue({ });
      
      // Call the function
      const result = await getItem(
        mockDynamoDbClient,
        'TestTable',
        'ACTIVITY#nonexistent',
        'METADATA#nonexistent'
      );
      
      // Verify DynamoDB GetCommand was called
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: {
            TableName: 'TestTable',
            Key: { PK: 'ACTIVITY#nonexistent', SK: 'METADATA#nonexistent' }
          }
        })
      );
      
      // Verify the result is undefined
      expect(result).toBeUndefined();
    });
    
    test('should handle DynamoDB errors', async () => {
      // Mock error response
      const mockError = new Error('DynamoDB error');
      mockDynamoDbClient.send.mockRejectedValue(mockError);
      
      // Call the function and expect it to throw
      await expect(getItem(
        mockDynamoDbClient,
        'TestTable',
        'ACTIVITY#123',
        'METADATA#123'
      )).rejects.toThrow('DynamoDB error');
      
      // Verify DynamoDB GetCommand was called
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: {
            TableName: 'TestTable',
            Key: { PK: 'ACTIVITY#123', SK: 'METADATA#123' }
          }
        })
      );
    });
  });

  describe('queryByPk', () => {
    test('should query items by partition key with default options', async () => {
      // Mock response
      const mockItems = [
        { PK: 'PROJECT#123', SK: 'ACTIVITY#1', title: 'Activity 1' },
        { PK: 'PROJECT#123', SK: 'ACTIVITY#2', title: 'Activity 2' }
      ];
      
      mockDynamoDbClient.send.mockResolvedValue({ 
        Items: mockItems,
        Count: 2
      });
      
      // Call the function
      const result = await queryByPk(
        mockDynamoDbClient,
        'TestTable',
        'PROJECT#123'
      );
      
      // Verify DynamoDB QueryCommand was called with correct parameters
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: {
            TableName: 'TestTable',
            KeyConditionExpression: 'PK = :pk',
            ExpressionAttributeValues: { ':pk': 'PROJECT#123' },
            Limit: 50
          }
        })
      );
      
      // Verify the result
      expect(result).toEqual({
        items: mockItems,
        nextToken: null,
        count: 2
      });
    });
    
    test('should query items with pagination token', async () => {
      // Mock response with LastEvaluatedKey
      const mockItems = [
        { PK: 'PROJECT#123', SK: 'ACTIVITY#3', title: 'Activity 3' },
        { PK: 'PROJECT#123', SK: 'ACTIVITY#4', title: 'Activity 4' }
      ];
      
      const mockLastEvaluatedKey = { PK: 'PROJECT#123', SK: 'ACTIVITY#4' };
      
      mockDynamoDbClient.send.mockResolvedValue({ 
        Items: mockItems,
        Count: 2,
        LastEvaluatedKey: mockLastEvaluatedKey
      });
      
      // Mock next token (would be from previous query)
      const mockNextToken = JSON.stringify({ PK: 'PROJECT#123', SK: 'ACTIVITY#2' });
      
      // Call the function with next token
      const result = await queryByPk(
        mockDynamoDbClient,
        'TestTable',
        'PROJECT#123',
        { nextToken: mockNextToken }
      );
      
      // Verify DynamoDB QueryCommand was called with ExclusiveStartKey
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            ExclusiveStartKey: JSON.parse(mockNextToken)
          })
        })
      );
      
      // Verify the result includes the next token
      expect(result).toEqual({
        items: mockItems,
        nextToken: JSON.stringify(mockLastEvaluatedKey),
        count: 2
      });
    });
    
    test('should query items with filter expression', async () => {
      // Mock response
      const mockItems = [
        { PK: 'PROJECT#123', SK: 'ACTIVITY#1', status: 'ACTIVE' }
      ];
      
      mockDynamoDbClient.send.mockResolvedValue({ 
        Items: mockItems,
        Count: 1
      });
      
      // Call the function with filter expression
      const result = await queryByPk(
        mockDynamoDbClient,
        'TestTable',
        'PROJECT#123',
        { 
          filterExpression: 'status = :status',
          expressionAttributeValues: { ':status': 'ACTIVE' },
          limit: 10
        }
      );
      
      // Verify DynamoDB QueryCommand was called with filter expression
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            FilterExpression: 'status = :status',
            ExpressionAttributeValues: { ':pk': 'PROJECT#123', ':status': 'ACTIVE' },
            Limit: 10
          })
        })
      );
      
      // Verify the result
      expect(result).toEqual({
        items: mockItems,
        nextToken: null,
        count: 1
      });
    });
    
    test('should handle empty results', async () => {
      // Mock empty response
      mockDynamoDbClient.send.mockResolvedValue({});
      
      // Call the function
      const result = await queryByPk(
        mockDynamoDbClient,
        'TestTable',
        'PROJECT#nonexistent'
      );
      
      // Verify the result has empty items array
      expect(result).toEqual({
        items: [],
        nextToken: null,
        count: 0
      });
    });
  });

  describe('queryByIndex', () => {
    test('should query items by index with default options', async () => {
      // Mock response
      const mockItems = [
        { PK: 'ACTIVITY#1', SK: 'METADATA#1', GSI1PK: 'USER#123', title: 'Activity 1' },
        { PK: 'ACTIVITY#2', SK: 'METADATA#2', GSI1PK: 'USER#123', title: 'Activity 2' }
      ];
      
      mockDynamoDbClient.send.mockResolvedValue({ 
        Items: mockItems,
        Count: 2
      });
      
      // Call the function
      const result = await queryByIndex(
        mockDynamoDbClient,
        'TestTable',
        'GSI1',
        'GSI1PK',
        'USER#123'
      );
      
      // Verify DynamoDB QueryCommand was called with correct parameters
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: {
            TableName: 'TestTable',
            IndexName: 'GSI1',
            KeyConditionExpression: 'GSI1PK = :keyValue',
            ExpressionAttributeValues: { ':keyValue': 'USER#123' },
            Limit: 50
          }
        })
      );
      
      // Verify the result
      expect(result).toEqual({
        items: mockItems,
        nextToken: null,
        count: 2
      });
    });
    
    test('should query items by index with pagination token', async () => {
      // Mock response with LastEvaluatedKey
      const mockItems = [
        { PK: 'ACTIVITY#3', SK: 'METADATA#3', GSI1PK: 'USER#123', title: 'Activity 3' }
      ];
      
      const mockLastEvaluatedKey = { 
        PK: 'ACTIVITY#3', 
        SK: 'METADATA#3', 
        GSI1PK: 'USER#123' 
      };
      
      mockDynamoDbClient.send.mockResolvedValue({ 
        Items: mockItems,
        Count: 1,
        LastEvaluatedKey: mockLastEvaluatedKey
      });
      
      // Mock next token (would be from previous query)
      const mockNextToken = JSON.stringify({ 
        PK: 'ACTIVITY#2', 
        SK: 'METADATA#2', 
        GSI1PK: 'USER#123' 
      });
      
      // Call the function with next token
      const result = await queryByIndex(
        mockDynamoDbClient,
        'TestTable',
        'GSI1',
        'GSI1PK',
        'USER#123',
        { nextToken: mockNextToken }
      );
      
      // Verify DynamoDB QueryCommand was called with ExclusiveStartKey
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            ExclusiveStartKey: JSON.parse(mockNextToken)
          })
        })
      );
      
      // Verify the result includes the next token
      expect(result).toEqual({
        items: mockItems,
        nextToken: JSON.stringify(mockLastEvaluatedKey),
        count: 1
      });
    });
    
    test('should query items by index with filter expression', async () => {
      // Mock response
      const mockItems = [
        { PK: 'ACTIVITY#1', SK: 'METADATA#1', GSI1PK: 'USER#123', status: 'ACTIVE' }
      ];
      
      mockDynamoDbClient.send.mockResolvedValue({ 
        Items: mockItems,
        Count: 1
      });
      
      // Call the function with filter expression
      const result = await queryByIndex(
        mockDynamoDbClient,
        'TestTable',
        'GSI1',
        'GSI1PK',
        'USER#123',
        { 
          filterExpression: 'status = :status',
          expressionAttributeNames: { '#s': 'status' },
          expressionAttributeValues: { ':status': 'ACTIVE' },
          limit: 10
        }
      );
      
      // Verify DynamoDB QueryCommand was called with filter expression
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            FilterExpression: 'status = :status',
            ExpressionAttributeNames: { '#s': 'status' },
            ExpressionAttributeValues: { ':keyValue': 'USER#123', ':status': 'ACTIVE' },
            Limit: 10
          })
        })
      );
      
      // Verify the result
      expect(result).toEqual({
        items: mockItems,
        nextToken: null,
        count: 1
      });
    });
    
    test('should handle empty results', async () => {
      // Mock empty response
      mockDynamoDbClient.send.mockResolvedValue({});
      
      // Call the function
      const result = await queryByIndex(
        mockDynamoDbClient,
        'TestTable',
        'GSI1',
        'GSI1PK',
        'USER#nonexistent'
      );
      
      // Verify the result has empty items array
      expect(result).toEqual({
        items: [],
        nextToken: null,
        count: 0
      });
    });
  });

  describe('countByIndex', () => {
    test('should count items by index', async () => {
      // Mock response
      mockDynamoDbClient.send.mockResolvedValue({ Count: 5 });
      
      // Call the function
      const result = await countByIndex(
        mockDynamoDbClient,
        'TestTable',
        'GSI1',
        'GSI1PK',
        'USER#123'
      );
      
      // Verify DynamoDB QueryCommand was called with correct parameters
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: {
            TableName: 'TestTable',
            IndexName: 'GSI1',
            KeyConditionExpression: 'GSI1PK = :keyValue',
            ExpressionAttributeValues: { ':keyValue': 'USER#123' },
            Select: 'COUNT'
          }
        })
      );
      
      // Verify the result
      expect(result).toBe(5);
    });
    
    test('should return 0 when no items found', async () => {
      // Mock empty response
      mockDynamoDbClient.send.mockResolvedValue({});
      
      // Call the function
      const result = await countByIndex(
        mockDynamoDbClient,
        'TestTable',
        'GSI1',
        'GSI1PK',
        'USER#nonexistent'
      );
      
      // Verify the result is 0
      expect(result).toBe(0);
    });
  });

  describe('buildUpdateExpression', () => {
    test('should build update expression from input object', () => {
      // Input object
      const input = {
        title: 'Updated Title',
        description: 'Updated description',
        status: 'COMPLETED'
      };
      
      // Call the function
      const result = buildUpdateExpression(input);
      
      // Verify the result
      expect(result).toEqual({
        expression: 'SET #title = :title, #description = :description, #status = :status',
        attributeNames: {
          '#title': 'title',
          '#description': 'description',
          '#status': 'status'
        },
        attributeValues: {
          ':title': 'Updated Title',
          ':description': 'Updated description',
          ':status': 'COMPLETED'
        }
      });
    });
    
    test('should handle empty input', () => {
      // Call the function with empty input
      const result = buildUpdateExpression({});
      
      // Verify the result
      expect(result).toEqual({
        expression: '',
        attributeNames: {},
        attributeValues: {}
      });
    });
    
    test('should handle null input', () => {
      // Call the function with null input
      const result = buildUpdateExpression(null);
      
      // Verify the result
      expect(result).toEqual({
        expression: '',
        attributeNames: {},
        attributeValues: {}
      });
    });
    
    test('should handle input with nested objects', () => {
      // Input object with nested structure
      const input = {
        title: 'Updated Title',
        metadata: {
          version: 2,
          tags: ['important', 'urgent']
        }
      };
      
      // Call the function
      const result = buildUpdateExpression(input);
      
      // Verify the result
      expect(result).toEqual({
        expression: 'SET #title = :title, #metadata = :metadata',
        attributeNames: {
          '#title': 'title',
          '#metadata': 'metadata'
        },
        attributeValues: {
          ':title': 'Updated Title',
          ':metadata': {
            version: 2,
            tags: ['important', 'urgent']
          }
        }
      });
    });
  });
});
