// __tests__/mutations.test.js

const { mockClient } = require('aws-sdk-client-mock');
const { DynamoDBDocumentClient, PutCommand, UpdateCommand, DeleteCommand } = require('@aws-sdk/lib-dynamodb');
const { isAdmin } = require('../../utils/permissions');
const helpers = require('../helpers');
const { 
  createUser,
  deleteUser,
  addUserToGroup,
  removeUserFromGroup
} = require('../mutations');

// Mock dependencies
jest.mock('../../utils/permissions');
jest.mock('../helpers');
jest.mock('crypto', () => ({
  randomUUID: jest.fn().mockReturnValue('mocked-uuid')
}));

describe('User Mutations', () => {
  const mockDynamoDbClient = {
    send: jest.fn()
  };
  
  beforeEach(() => {
    mockDynamoDbClient.send.mockClear();
    jest.clearAllMocks();
  });

  describe('createUser', () => {
    test('should create user when user is admin', async () => {
      isAdmin.mockReturnValue(true);
      
      const mockInput = {
        username: 'testuser',
        email: '<EMAIL>'
      };
      
      helpers.getUserByUsername.mockResolvedValue(null); // User doesn't exist yet
      mockDynamoDbClient.send.mockResolvedValue({});
      
      const result = await createUser(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        mockInput
      );
      
      // Verify admin permission check
      expect(isAdmin).toHaveBeenCalledWith(['admin']);
      
      // Verify user existence check
      expect(helpers.getUserByUsername).toHaveBeenCalledWith(
        mockDynamoDbClient,
        'TestTable',
        'testuser'
      );
      
      // Verify DynamoDB put operation
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: {
            TableName: 'TestTable',
            Item: expect.objectContaining({
              ...mockInput,
              PK: 'USER#testuser',
              SK: 'METADATA#testuser',
              managerGroupIds: [],
              operatorGroupIds: [],
              workerGroupIds: []
            })
          }
        })
      );
      
      // Verify returned user
      expect(result).toEqual(expect.objectContaining({
        ...mockInput,
        PK: 'USER#testuser',
        SK: 'METADATA#testuser',
        managerGroupIds: [],
        operatorGroupIds: [],
        workerGroupIds: []
      }));
    });
    
    test('should throw error when user is not admin', async () => {
      isAdmin.mockReturnValue(false);
      
      const mockInput = {
        username: 'testuser',
        email: '<EMAIL>'
      };
      
      await expect(createUser(
        mockDynamoDbClient,
        'TestTable',
        ['UserGroup'],
        mockInput
      )).rejects.toThrow('You do not have permission');
      
      // Verify DynamoDB was not called
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
    
    test('should throw error when username already exists', async () => {
      isAdmin.mockReturnValue(true);
      
      const mockInput = {
        username: 'existinguser',
        email: '<EMAIL>'
      };
      
      // Mock that user already exists
      helpers.getUserByUsername.mockResolvedValue({
        username: 'existinguser',
        email: '<EMAIL>'
      });
      
      await expect(createUser(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        mockInput
      )).rejects.toThrow('already exists');
      
      // Verify DynamoDB was not called
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
  });
  
  describe('deleteUser', () => {
    test('should delete user when user is admin', async () => {
      isAdmin.mockReturnValue(true);
      
      // Mock that user exists
      helpers.getUserByUsername.mockResolvedValue({
        username: 'testuser',
        email: '<EMAIL>'
      });
      
      mockDynamoDbClient.send.mockResolvedValue({});
      
      const result = await deleteUser(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        'testuser'
      );
      
      // Verify admin permission check
      expect(isAdmin).toHaveBeenCalledWith(['admin']);
      
      // Verify user existence check
      expect(helpers.getUserByUsername).toHaveBeenCalledWith(
        mockDynamoDbClient,
        'TestTable',
        'testuser'
      );
      
      // Verify DynamoDB delete operation
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: {
            TableName: 'TestTable',
            Key: { PK: 'USER#testuser', SK: 'METADATA#testuser' }
          }
        })
      );
      
      // Verify returned username
      expect(result).toBe('testuser');
    });
    
    test('should throw error when user is not admin', async () => {
      isAdmin.mockReturnValue(false);
      
      await expect(deleteUser(
        mockDynamoDbClient,
        'TestTable',
        ['UserGroup'],
        'testuser'
      )).rejects.toThrow('You do not have permission');
      
      // Verify DynamoDB was not called
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
    
    test('should throw error when username does not exist', async () => {
      isAdmin.mockReturnValue(true);
      
      // Mock that user doesn't exist
      helpers.getUserByUsername.mockResolvedValue(null);
      
      await expect(deleteUser(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        'nonexistentuser'
      )).rejects.toThrow('not found');
      
      // Verify DynamoDB was not called
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
  });
  
  describe('addUserToGroup', () => {
    test('should add user to manager group', async () => {
      isAdmin.mockReturnValue(true);
      
      // Mock user and group
      const mockUser = {
        username: 'testuser',
        email: '<EMAIL>',
        managerGroupIds: []
      };
      
      const mockGroup = {
        id: 'group123',
        name: 'Managers',
        type: 'MANAGER'
      };
      
      helpers.getUserByUsername.mockResolvedValue(mockUser);
      helpers.getGroupById.mockResolvedValue(mockGroup);
      
      const mockUpdatedUser = {
        ...mockUser,
        managerGroupIds: ['group123'],
        updatedAt: expect.any(String)
      };
      
      mockDynamoDbClient.send
        .mockResolvedValueOnce({}) // First call for relationship record
        .mockResolvedValueOnce({ Attributes: mockUpdatedUser }); // Second call for user update
      
      const result = await addUserToGroup(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        'testuser',
        'group123'
      );
      
      // Verify admin permission check
      expect(isAdmin).toHaveBeenCalledWith(['admin']);
      
      // Verify user and group existence checks
      expect(helpers.getUserByUsername).toHaveBeenCalledWith(
        mockDynamoDbClient,
        'TestTable',
        'testuser'
      );
      
      expect(helpers.getGroupById).toHaveBeenCalledWith(
        mockDynamoDbClient,
        'TestTable',
        'group123'
      );
      
      // Verify DynamoDB operations
      expect(mockDynamoDbClient.send).toHaveBeenCalledTimes(2);
      
      // Verify relationship record creation
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: {
            TableName: 'TestTable',
            Item: expect.objectContaining({
              PK: 'USER_GROUP#testuser:group123',
              SK: 'METADATA#testuser:group123',
              username: 'testuser',
              groupId: 'group123',
              groupType: 'MANAGER'
            })
          }
        })
      );
      
      // Verify user update
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            TableName: 'TestTable',
            Key: { PK: 'USER#testuser', SK: 'METADATA#testuser' },
            UpdateExpression: expect.stringContaining('managerGroupIds')
          })
        })
      );
      
      // Verify returned user
      expect(result).toEqual(mockUpdatedUser);
    });
    
    test('should add user to operator group', async () => {
      isAdmin.mockReturnValue(true);
      
      // Mock user and group
      const mockUser = {
        username: 'testuser',
        email: '<EMAIL>',
        operatorGroupIds: []
      };
      
      const mockGroup = {
        id: 'group456',
        name: 'Operators',
        type: 'OPERATOR'
      };
      
      helpers.getUserByUsername.mockResolvedValue(mockUser);
      helpers.getGroupById.mockResolvedValue(mockGroup);
      
      const mockUpdatedUser = {
        ...mockUser,
        operatorGroupIds: ['group456'],
        updatedAt: expect.any(String)
      };
      
      mockDynamoDbClient.send
        .mockResolvedValueOnce({}) // First call for relationship record
        .mockResolvedValueOnce({ Attributes: mockUpdatedUser }); // Second call for user update
      
      const result = await addUserToGroup(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        'testuser',
        'group456'
      );
      
      // Verify DynamoDB operations
      expect(mockDynamoDbClient.send).toHaveBeenCalledTimes(2);
      
      // Verify user update
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            UpdateExpression: expect.stringContaining('operatorGroupIds')
          })
        })
      );
      
      // Verify returned user
      expect(result).toEqual(mockUpdatedUser);
    });
    
    test('should add user to worker group', async () => {
      isAdmin.mockReturnValue(true);
      
      // Mock user and group
      const mockUser = {
        username: 'testuser',
        email: '<EMAIL>',
        workerGroupIds: []
      };
      
      const mockGroup = {
        id: 'group789',
        name: 'Workers',
        type: 'WORKER'
      };
      
      helpers.getUserByUsername.mockResolvedValue(mockUser);
      helpers.getGroupById.mockResolvedValue(mockGroup);
      
      const mockUpdatedUser = {
        ...mockUser,
        workerGroupIds: ['group789'],
        updatedAt: expect.any(String)
      };
      
      mockDynamoDbClient.send
        .mockResolvedValueOnce({}) // First call for relationship record
        .mockResolvedValueOnce({ Attributes: mockUpdatedUser }); // Second call for user update
      
      const result = await addUserToGroup(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        'testuser',
        'group789'
      );
      
      // Verify DynamoDB operations
      expect(mockDynamoDbClient.send).toHaveBeenCalledTimes(2);
      
      // Verify user update
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            UpdateExpression: expect.stringContaining('workerGroupIds')
          })
        })
      );
      
      // Verify returned user
      expect(result).toEqual(mockUpdatedUser);
    });
    
    test('should throw error when user is not admin', async () => {
      isAdmin.mockReturnValue(false);
      
      await expect(addUserToGroup(
        mockDynamoDbClient,
        'TestTable',
        ['UserGroup'],
        'testuser',
        'group123'
      )).rejects.toThrow('You do not have permission');
      
      // Verify DynamoDB was not called
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
    
    test('should throw error when user does not exist', async () => {
      isAdmin.mockReturnValue(true);
      
      // Mock that user doesn't exist
      helpers.getUserByUsername.mockResolvedValue(null);
      
      await expect(addUserToGroup(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        'nonexistentuser',
        'group123'
      )).rejects.toThrow('not found');
      
      // Verify DynamoDB was not called
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
    
    test('should throw error when group does not exist', async () => {
      isAdmin.mockReturnValue(true);
      
      // Mock that user exists but group doesn't
      helpers.getUserByUsername.mockResolvedValue({
        username: 'testuser',
        email: '<EMAIL>'
      });
      
      helpers.getGroupById.mockResolvedValue(null);
      
      await expect(addUserToGroup(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        'testuser',
        'nonexistentgroup'
      )).rejects.toThrow('not found');
      
      // Verify DynamoDB was not called
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
    
    test('should not update if user is already in group', async () => {
      isAdmin.mockReturnValue(true);
      
      // Mock user already in group
      const mockUser = {
        username: 'testuser',
        email: '<EMAIL>',
        managerGroupIds: ['group123']
      };
      
      const mockGroup = {
        id: 'group123',
        name: 'Managers',
        type: 'MANAGER'
      };
      
      helpers.getUserByUsername.mockResolvedValue(mockUser);
      helpers.getGroupById.mockResolvedValue(mockGroup);
      
      const result = await addUserToGroup(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        'testuser',
        'group123'
      );
      
      // Verify DynamoDB was not called
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
      
      // Verify original user is returned
      expect(result).toEqual(mockUser);
    });
  });
  
  describe('removeUserFromGroup', () => {
    test('should remove user from manager group', async () => {
      isAdmin.mockReturnValue(true);
      
      // Mock user and group
      const mockUser = {
        username: 'testuser',
        email: '<EMAIL>',
        managerGroupIds: ['group123', 'othergroup']
      };
      
      const mockGroup = {
        id: 'group123',
        name: 'Managers',
        type: 'MANAGER'
      };
      
      helpers.getUserByUsername.mockResolvedValue(mockUser);
      helpers.getGroupById.mockResolvedValue(mockGroup);
      
      const mockUpdatedUser = {
        ...mockUser,
        managerGroupIds: ['othergroup'],
        updatedAt: expect.any(String)
      };
      
      mockDynamoDbClient.send
        .mockResolvedValueOnce({}) // First call for relationship record deletion
        .mockResolvedValueOnce({ Attributes: mockUpdatedUser }); // Second call for user update
      
      const result = await removeUserFromGroup(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        'testuser',
        'group123'
      );
      
      // Verify admin permission check
      expect(isAdmin).toHaveBeenCalledWith(['admin']);
      
      // Verify user and group existence checks
      expect(helpers.getUserByUsername).toHaveBeenCalledWith(
        mockDynamoDbClient,
        'TestTable',
        'testuser'
      );
      
      expect(helpers.getGroupById).toHaveBeenCalledWith(
        mockDynamoDbClient,
        'TestTable',
        'group123'
      );
      
      // Verify DynamoDB operations
      expect(mockDynamoDbClient.send).toHaveBeenCalledTimes(2);
      
      // Verify relationship record deletion
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: {
            TableName: 'TestTable',
            Key: { PK: 'USER_GROUP#testuser:group123', SK: 'METADATA#testuser:group123' }
          }
        })
      );
      
      // Verify user update
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            TableName: 'TestTable',
            Key: { PK: 'USER#testuser', SK: 'METADATA#testuser' },
            UpdateExpression: expect.stringContaining('managerGroupIds')
          })
        })
      );
      
      // Verify returned user
      expect(result).toEqual(mockUpdatedUser);
    });
    
    test('should throw error when user is not admin', async () => {
      isAdmin.mockReturnValue(false);
      
      await expect(removeUserFromGroup(
        mockDynamoDbClient,
        'TestTable',
        ['UserGroup'],
        'testuser',
        'group123'
      )).rejects.toThrow('You do not have permission');
      
      // Verify DynamoDB was not called
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
    
    test('should not update if user is not in group', async () => {
      isAdmin.mockReturnValue(true);
      
      // Mock user not in group
      const mockUser = {
        username: 'testuser',
        email: '<EMAIL>',
        managerGroupIds: ['othergroup']
      };
      
      const mockGroup = {
        id: 'group123',
        name: 'Managers',
        type: 'MANAGER'
      };
      
      helpers.getUserByUsername.mockResolvedValue(mockUser);
      helpers.getGroupById.mockResolvedValue(mockGroup);
      
      const result = await removeUserFromGroup(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        'testuser',
        'group123'
      );
      
      // Verify DynamoDB was not called
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
      
      // Verify original user is returned
      expect(result).toEqual(mockUser);
    });
  });
});
