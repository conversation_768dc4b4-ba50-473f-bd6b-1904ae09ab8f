// __tests__/index.test.js

const userModule = require('../index');
const queries = require('../queries');
const mutations = require('../mutations');

describe('User Module Exports', () => {
  test('should export all query functions', () => {
    // Check that all query functions are exported
    expect(userModule.getUser).toBe(queries.getUser);
    expect(userModule.listUsers).toBe(queries.listUsers);
  });

  test('should export all mutation functions', () => {
    // Check that all mutation functions are exported
    expect(userModule.createUser).toBe(mutations.createUser);
    expect(userModule.deleteUser).toBe(mutations.deleteUser);
    expect(userModule.addUserToGroup).toBe(mutations.addUserToGroup);
    expect(userModule.removeUserFromGroup).toBe(mutations.removeUserFromGroup);
  });

  test('should have all expected exports', () => {
    // Check that the module exports exactly the expected functions
    const expectedExports = [
      'getUser',
      'listUsers',
      'createUser',
      'deleteUser',
      'addUserToGroup',
      'removeUserFromGroup'
    ];

    const actualExports = Object.keys(userModule);
    
    // Check that all expected exports are present
    expectedExports.forEach(exportName => {
      expect(actualExports).toContain(exportName);
    });
    
    // Check that there are no unexpected exports
    expect(actualExports.length).toBe(expectedExports.length);
    
    // Alternative way to check the same thing
    expect(actualExports.sort()).toEqual(expectedExports.sort());
  });
});
