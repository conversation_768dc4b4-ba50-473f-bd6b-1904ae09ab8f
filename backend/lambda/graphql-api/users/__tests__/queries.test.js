// __tests__/queries.test.js

const { mockClient } = require('aws-sdk-client-mock');
const { DynamoDBDocumentClient, QueryCommand } = require('@aws-sdk/lib-dynamodb');
const { isAdmin } = require('../../utils/permissions');
const helpers = require('../helpers');
const { 
  getUser,
  listUsers
} = require('../queries');

// Mock dependencies
jest.mock('../../utils/permissions');
jest.mock('../helpers');

describe('User Queries', () => {
  const ddbMock = mockClient(DynamoDBDocumentClient);

  // Create a wrapper object that has a send method
  const mockDynamoDbClient = {
    send: jest.fn()
  };
  
  beforeEach(() => {
    ddbMock.reset();
    mockDynamoDbClient.send.mockClear();
    jest.clearAllMocks();
  });

  describe('getUser', () => {
    test('should retrieve user when requester is admin', async () => {
      isAdmin.mockReturnValue(true);
      
      const mockUser = {
        username: 'testuser',
        email: '<EMAIL>',
        managerGroupIds: ['group1'],
        operatorGroupIds: ['group2'],
        workerGroupIds: ['group3']
      };
      
      const mockManagerGroups = [{ id: 'group1', name: 'Managers', type: 'MANAGER' }];
      const mockOperatorGroups = [{ id: 'group2', name: 'Operators', type: 'OPERATOR' }];
      const mockWorkerGroups = [{ id: 'group3', name: 'Workers', type: 'WORKER' }];
      
      helpers.getUserByUsername.mockResolvedValue(mockUser);
      helpers.getUserGroupsByType
        .mockResolvedValueOnce(mockManagerGroups)
        .mockResolvedValueOnce(mockOperatorGroups)
        .mockResolvedValueOnce(mockWorkerGroups);
      
      const result = await getUser(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        'testuser',
        'admin-user'
      );
      
      // Verify admin permission check
      expect(isAdmin).toHaveBeenCalledWith(['admin']);
      
      // Verify user retrieval
      expect(helpers.getUserByUsername).toHaveBeenCalledWith(
        mockDynamoDbClient,
        'TestTable',
        'testuser'
      );
      
      // Verify group retrievals
      expect(helpers.getUserGroupsByType).toHaveBeenCalledWith(
        mockDynamoDbClient,
        'TestTable',
        'testuser',
        'MANAGER'
      );
      
      expect(helpers.getUserGroupsByType).toHaveBeenCalledWith(
        mockDynamoDbClient,
        'TestTable',
        'testuser',
        'OPERATOR'
      );
      
      expect(helpers.getUserGroupsByType).toHaveBeenCalledWith(
        mockDynamoDbClient,
        'TestTable',
        'testuser',
        'WORKER'
      );
      
      // Verify returned user with groups
      expect(result).toEqual({
        ...mockUser,
        managerGroups: mockManagerGroups,
        operatorGroups: mockOperatorGroups,
        workerGroups: mockWorkerGroups
      });
    });
    
    test('should throw error when requester is not admin and not accessing their own info', async () => {
      isAdmin.mockReturnValue(false);
      
      await expect(getUser(
        mockDynamoDbClient,
        'TestTable',
        ['UserGroup'],
        'testuser',
        'non-admin-user'
      )).rejects.toThrow('You do not have permission');
      
      // Verify user retrieval was not attempted
      expect(helpers.getUserByUsername).not.toHaveBeenCalled();
    });
    
    test('should allow user to access their own information', async () => {
      isAdmin.mockReturnValue(false);
      
      const mockUser = {
        username: 'regular-user',
        email: '<EMAIL>',
        managerGroupIds: ['group1'],
        operatorGroupIds: ['group2'],
        workerGroupIds: ['group3']
      };
      
      const mockManagerGroups = [{ id: 'group1', name: 'Managers', type: 'MANAGER' }];
      const mockOperatorGroups = [{ id: 'group2', name: 'Operators', type: 'OPERATOR' }];
      const mockWorkerGroups = [{ id: 'group3', name: 'Workers', type: 'WORKER' }];
      
      helpers.getUserByUsername.mockResolvedValue(mockUser);
      helpers.getUserGroupsByType
        .mockResolvedValueOnce(mockManagerGroups)
        .mockResolvedValueOnce(mockOperatorGroups)
        .mockResolvedValueOnce(mockWorkerGroups);
      
      const result = await getUser(
        mockDynamoDbClient,
        'TestTable',
        ['UserGroup'],
        'regular-user',
        'regular-user'
      );
      
      // Verify admin permission check
      expect(isAdmin).toHaveBeenCalledWith(['UserGroup']);
      
      // Verify user retrieval
      expect(helpers.getUserByUsername).toHaveBeenCalledWith(
        mockDynamoDbClient,
        'TestTable',
        'regular-user'
      );
      
      // Verify group retrievals
      expect(helpers.getUserGroupsByType).toHaveBeenCalledWith(
        mockDynamoDbClient,
        'TestTable',
        'regular-user',
        'MANAGER'
      );
      
      expect(helpers.getUserGroupsByType).toHaveBeenCalledWith(
        mockDynamoDbClient,
        'TestTable',
        'regular-user',
        'OPERATOR'
      );
      
      expect(helpers.getUserGroupsByType).toHaveBeenCalledWith(
        mockDynamoDbClient,
        'TestTable',
        'regular-user',
        'WORKER'
      );
      
      // Verify returned user with groups
      expect(result).toEqual({
        ...mockUser,
        managerGroups: mockManagerGroups,
        operatorGroups: mockOperatorGroups,
        workerGroups: mockWorkerGroups
      });
    });
    
    test('should throw error when user not found', async () => {
      isAdmin.mockReturnValue(true);
      helpers.getUserByUsername.mockResolvedValue(null);
      
      await expect(getUser(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        'nonexistentuser',
        'admin-user'
      )).rejects.toThrow('not found');
      
      // Verify group retrievals were not attempted
      expect(helpers.getUserGroupsByType).not.toHaveBeenCalled();
    });
  });

  describe('listUsers', () => {
    test('should list users when requester is admin', async () => {
      isAdmin.mockReturnValue(true);
      
      const mockUsers = [
        { username: 'user1', email: '<EMAIL>' },
        { username: 'user2', email: '<EMAIL>' }
      ];
      
      // Mock DynamoDB response
      mockDynamoDbClient.send.mockResolvedValue({
        Items: mockUsers,
        LastEvaluatedKey: null,
        Count: 2
      });
      
      // Mock group retrievals for each user
      helpers.getUserGroupsByType
        // For user1
        .mockResolvedValueOnce([{ id: 'group1', name: 'Managers', type: 'MANAGER' }])
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([])
        // For user2
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([{ id: 'group2', name: 'Operators', type: 'OPERATOR' }])
        .mockResolvedValueOnce([]);
      
      const result = await listUsers(
        mockDynamoDbClient,
        'TestTable',
        ['admin']
      );
      
      // Verify admin permission check
      expect(isAdmin).toHaveBeenCalledWith(['admin']);
      
      // Verify DynamoDB query
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            TableName: 'TestTable',
            IndexName: 'type-index'
          })
        })
      );
      
      // Verify group retrievals for each user
      expect(helpers.getUserGroupsByType).toHaveBeenCalledTimes(6);
      
      // Verify returned users with groups
      expect(result).toEqual({
        items: [
          {
            ...mockUsers[0],
            managerGroups: [{ id: 'group1', name: 'Managers', type: 'MANAGER' }],
            operatorGroups: [],
            workerGroups: []
          },
          {
            ...mockUsers[1],
            managerGroups: [],
            operatorGroups: [{ id: 'group2', name: 'Operators', type: 'OPERATOR' }],
            workerGroups: []
          }
        ],
        nextToken: null,
        totalCount: 2
      });
    });
    
    test('should handle pagination with nextToken', async () => {
      isAdmin.mockReturnValue(true);
      
      const mockUsers = [
        { username: 'user3', email: '<EMAIL>' }
      ];
      
      const lastEvaluatedKey = { PK: 'USER#user3' };
      const inputNextToken = JSON.stringify({ PK: 'USER#user2' });
      
      // Mock DynamoDB response
      mockDynamoDbClient.send.mockResolvedValue({
        Items: mockUsers,
        LastEvaluatedKey: lastEvaluatedKey,
        Count: 10 // Total count
      });
      
      // Mock group retrievals
      helpers.getUserGroupsByType
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([{ id: 'group3', name: 'Workers', type: 'WORKER' }]);
      
      const result = await listUsers(
        mockDynamoDbClient,
        'TestTable',
        ['admin'],
        5, // Limit
        inputNextToken
      );
      
      // Verify DynamoDB query with pagination
      expect(mockDynamoDbClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            Limit: 5,
            ExclusiveStartKey: JSON.parse(inputNextToken)
          })
        })
      );
      
      // Verify returned users with pagination info
      expect(result).toEqual({
        items: [
          {
            ...mockUsers[0],
            managerGroups: [],
            operatorGroups: [],
            workerGroups: [{ id: 'group3', name: 'Workers', type: 'WORKER' }]
          }
        ],
        nextToken: JSON.stringify(lastEvaluatedKey),
        totalCount: 10
      });
    });
    
    test('should throw error when requester is not admin', async () => {
      isAdmin.mockReturnValue(false);
      
      await expect(listUsers(
        mockDynamoDbClient,
        'TestTable',
        ['UserGroup']
      )).rejects.toThrow('You do not have permission');
      
      // Verify DynamoDB query was not attempted
      expect(mockDynamoDbClient.send).not.toHaveBeenCalled();
    });
    
    test('should handle empty results', async () => {
      isAdmin.mockReturnValue(true);
      
      // Mock empty DynamoDB response
      mockDynamoDbClient.send.mockResolvedValue({
        Items: [],
        LastEvaluatedKey: null,
        Count: 0
      });
      
      const result = await listUsers(
        mockDynamoDbClient,
        'TestTable',
        ['admin']
      );
      
      // Verify no group retrievals were attempted
      expect(helpers.getUserGroupsByType).not.toHaveBeenCalled();
      
      // Verify empty result
      expect(result).toEqual({
        items: [],
        nextToken: null,
        totalCount: 0
      });
    });
  });
});
