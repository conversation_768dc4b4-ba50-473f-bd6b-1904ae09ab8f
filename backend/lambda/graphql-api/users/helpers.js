/**
 * Internal helper functions for user operations
 */
const { getItem } = require('../utils/dynamodb');

/**
 * Get a user by username
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} username - Username
 * @returns {Promise<Object>} - The user or undefined if not found
 */
async function getUserByUsername(dynamodb, tableName, username) {
    return await getItem(dynamodb, tableName, `USER#${username}`, `METADATA#${username}`);
}

/**
 * Get a group by ID
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} groupId - Group ID
 * @returns {Promise<Object>} - The group or undefined if not found
 */
async function getGroupById(dynamodb, tableName, groupId) {
    return await getItem(dynamodb, tableName, `GROUP#${groupId}`, `METADATA#${groupId}`);
}

/**
 * Get user's groups by type
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} username - Username
 * @param {string} groupType - Group type (MANAGER, OPERATOR, WORKER)
 * @returns {Promise<Array<Object>>} - Array of groups
 */
async function getUserGroupsByType(dynamodb, tableName, username, groupType) {
    const user = await getUserByUsername(dynamodb, tableName, username);
    if (!user) return [];
    
    // Get the appropriate group IDs based on type
    let groupIds = [];
    switch (groupType) {
        case 'MANAGER':
            groupIds = user.managerGroupIds || [];
            break;
        case 'OPERATOR':
            groupIds = user.operatorGroupIds || [];
            break;
        case 'WORKER':
            groupIds = user.workerGroupIds || [];
            break;
        default:
            return [];
    }
    
    // If no groups, return empty array
    if (groupIds.length === 0) return [];
    
    // Get all groups in parallel
    const groupPromises = groupIds.map(id => getGroupById(dynamodb, tableName, id));
    const groups = await Promise.all(groupPromises);
    
    // Filter out any undefined results (in case a group was deleted)
    return groups.filter(group => group !== undefined);
}

module.exports = {
    getUserByUsername,
    getGroupById,
    getUserGroupsByType
};
