/**
 * User mutation operations
 */
const { PutCommand, UpdateCommand, DeleteCommand } = require('@aws-sdk/lib-dynamodb');
const { randomUUID } = require('crypto');
const { isAdmin } = require('../utils/permissions');
const { getUserByUsername, getGroupById } = require('./helpers');
const { buildUpdateExpression } = require('../utils/dynamodb');

/**
 * Create a new user
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {Object} input - User input data
 * @returns {Promise<Object>} - The created user
 * @throws {Error} - If user doesn't have permission or username already exists
 */
async function createUser(dynamodb, tableName, userGroups, input) {
    // Only admins can create users
    if (!isAdmin(userGroups)) {
        throw new Error("Access denied: You do not have permission to create users");
    }
    
    // Check if username already exists
    const existingUser = await getUserByUsername(dynamodb, tableName, input.username);
    if (existingUser) {
        throw new Error(`User with username ${input.username} already exists`);
    }
    
    const timestamp = new Date().toISOString();
    
    const user = {
        ...input,
        PK: `USER#${input.username}`,
        SK: `METADATA#${input.username}`,
        managerGroupIds: [],
        operatorGroupIds: [],
        type: 'USER',
        workerGroupIds: [],
        createdAt: timestamp,
        updatedAt: timestamp
    };
    
    await dynamodb.send(new PutCommand({
        TableName: tableName,
        Item: user
    }));
    
    return user;
}
anaqqqqqqqq---p-=]['']
""""""":
/**
 * Delete a user
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} username - Username to delete
 * @returns {Promise<string>} - The deleted username
 * @throws {Error} - If user doesn't have permission or username doesn't exist
 */
async function deleteUser(dynamodb, tableName, userGroups, username) {
    // Only admins can delete users
    if (!isAdmin(userGroups)) {
        throw new Error("Access denied: You do not have permission to delete users");
    }
    
    // Check if user exists
    const existingUser = await getUserByUsername(dynamodb, tableName, username);
    if (!existingUser) {
        throw new Error(`User with username ${username} not found`);
    }
    
    await dynamodb.send(new DeleteCommand({
        TableName: tableName,
        Key: { PK: `USER#${username}`, SK: `METADATA#${username}` }
    }));
    
    return username;
}

/**
 * Add a user to a group
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} username - Username to add to group
 * @param {string} groupId - Group ID to add user to
 * @returns {Promise<Object>} - The updated user
 * @throws {Error} - If user doesn't have permission, user or group doesn't exist
 */
async function addUserToGroup(dynamodb, tableName, userGroups, username, groupId) {
    // Only admins can add users to groups
    if (!isAdmin(userGroups)) {
        throw new Error("Access denied: You do not have permission to add users to groups");
    }
    
    // Check if user exists
    const user = await getUserByUsername(dynamodb, tableName, username);
    if (!user) {
        throw new Error(`User with username ${username} not found`);
    }
    
    // Check if group exists
    const group = await getGroupById(dynamodb, tableName, groupId);
    if (!group) {
        throw new Error(`Group with ID ${groupId} not found`);
    }
    
    // Determine which group array to update based on group type
    let updateExpression = '';
    let expressionAttributeNames = {};
    let expressionAttributeValues = {};

    switch (group.groupType) {
        case 'MANAGER':
            // Check if user is already in this group
            if (user.managerGroupIds && user.managerGroupIds.includes(groupId)) {
                return user; // User already in group, no update needed
            }
            
            // Add group ID to managerGroupIds array
            updateExpression = 'SET #managerGroupIds = list_append(if_not_exists(#managerGroupIds, :emptyList), :groupId), #updatedAt = :updatedAt';
            expressionAttributeNames = {
                '#managerGroupIds': 'managerGroupIds',
                '#updatedAt': 'updatedAt'
            };
            expressionAttributeValues = {
                ':groupId': [groupId],
                ':emptyList': [],
                ':updatedAt': new Date().toISOString()
            };
            break;
            
        case 'OPERATOR':
            // Check if user is already in this group
            if (user.operatorGroupIds && user.operatorGroupIds.includes(groupId)) {
                return user; // User already in group, no update needed
            }
            
            // Add group ID to operatorGroupIds array
            updateExpression = 'SET #operatorGroupIds = list_append(if_not_exists(#operatorGroupIds, :emptyList), :groupId), #updatedAt = :updatedAt';
            expressionAttributeNames = {
                '#operatorGroupIds': 'operatorGroupIds',
                '#updatedAt': 'updatedAt'
            };
            expressionAttributeValues = {
                ':groupId': [groupId],
                ':emptyList': [],
                ':updatedAt': new Date().toISOString()
            };
            break;
            
        case 'WORKER':
            // Check if user is already in this group
            if (user.workerGroupIds && user.workerGroupIds.includes(groupId)) {
                return user; // User already in group, no update needed
            }
            
            // Add group ID to workerGroupIds array
            updateExpression = 'SET #workerGroupIds = list_append(if_not_exists(#workerGroupIds, :emptyList), :groupId), #updatedAt = :updatedAt';
            expressionAttributeNames = {
                '#workerGroupIds': 'workerGroupIds',
                '#updatedAt': 'updatedAt'
            };
            expressionAttributeValues = {
                ':groupId': [groupId],
                ':emptyList': [],
                ':updatedAt': new Date().toISOString()
            };
            break;
            
        default:
            throw new Error(`Invalid group type: ${group.groupType}`);
    }
    
    // Update the user record
    const params = {
        TableName: tableName,
        Key: { PK: `USER#${username}`, SK: `METADATA#${username}` },
        UpdateExpression: updateExpression,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributeValues,
        ReturnValues: 'ALL_NEW'
    };
    
    // Also create a user-group relationship record for querying users by group
    const relationshipId = `${username}:${groupId}`;
    await dynamodb.send(new PutCommand({
        TableName: tableName,
        Item: {
            PK: `USER_GROUP#${relationshipId}`,
            SK: `METADATA#${relationshipId}`,
            username: username,
            groupId: groupId,
            type: 'USER_GROUP_RELATIONSHIP',
            groupType: group.groupType,
            createdAt: new Date().toISOString()
        }
    }));
    
    const response = await dynamodb.send(new UpdateCommand(params));
    return response.Attributes;
}

/**
 * Remove a user from a group
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} username - Username to remove from group
 * @param {string} groupId - Group ID to remove user from
 * @returns {Promise<Object>} - The updated user
 * @throws {Error} - If user doesn't have permission, user or group doesn't exist
 */
async function removeUserFromGroup(dynamodb, tableName, userGroups, username, groupId) {
    // Only admins can remove users from groups
    if (!isAdmin(userGroups)) {
        throw new Error("Access denied: You do not have permission to remove users from groups");
    }
    
    // Check if user exists
    const user = await getUserByUsername(dynamodb, tableName, username);
    if (!user) {
        throw new Error(`User with username ${username} not found`);
    }
    
    // Check if group exists
    const group = await getGroupById(dynamodb, tableName, groupId);
    if (!group) {
        throw new Error(`Group with ID ${groupId} not found`);
    }
    
    // Determine which group array to update based on group type
    let updateExpression = '';
    let expressionAttributeNames = {};
    let expressionAttributeValues = {};
    let conditionExpression = '';
    
    switch (group.groupType) {
        case 'MANAGER':
            // Check if user is in this group
            if (!user.managerGroupIds || !user.managerGroupIds.includes(groupId)) {
                return user; // User not in group, no update needed
            }
            
            // Remove group ID from managerGroupIds array
            const managerGroupIds = user.managerGroupIds.filter(id => id !== groupId);
            updateExpression = 'SET #managerGroupIds = :managerGroupIds, #updatedAt = :updatedAt';
            expressionAttributeNames = {
                '#managerGroupIds': 'managerGroupIds',
                '#updatedAt': 'updatedAt'
            };
            expressionAttributeValues = {
                ':managerGroupIds': managerGroupIds,
                ':updatedAt': new Date().toISOString()
            };
            break;
            
        case 'OPERATOR':
            // Check if user is in this group
            if (!user.operatorGroupIds || !user.operatorGroupIds.includes(groupId)) {
                return user; // User not in group, no update needed
            }
            
            // Remove group ID from operatorGroupIds array
            const operatorGroupIds = user.operatorGroupIds.filter(id => id !== groupId);
            updateExpression = 'SET #operatorGroupIds = :operatorGroupIds, #updatedAt = :updatedAt';
            expressionAttributeNames = {
                '#operatorGroupIds': 'operatorGroupIds',
                '#updatedAt': 'updatedAt'
            };
            expressionAttributeValues = {
                ':operatorGroupIds': operatorGroupIds,
                ':updatedAt': new Date().toISOString()
            };
            break;
            
        case 'WORKER':
            // Check if user is in this group
            if (!user.workerGroupIds || !user.workerGroupIds.includes(groupId)) {
                return user; // User not in group, no update needed
            }
            
            // Remove group ID from workerGroupIds array
            const workerGroupIds = user.workerGroupIds.filter(id => id !== groupId);
            updateExpression = 'SET #workerGroupIds = :workerGroupIds, #updatedAt = :updatedAt';
            expressionAttributeNames = {
                '#workerGroupIds': 'workerGroupIds',
                '#updatedAt': 'updatedAt'
            };
            expressionAttributeValues = {
                ':workerGroupIds': workerGroupIds,
                ':updatedAt': new Date().toISOString()
            };
            break;
            
        default:
            throw new Error(`Invalid group type: ${group.groupType}`);
    }
    
    // Update the user record
    const params = {
        TableName: tableName,
        Key: { PK: `USER#${username}`, SK: `METADATA#${username}` },
        UpdateExpression: updateExpression,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributeValues,
        ReturnValues: 'ALL_NEW'
    };
    
    // Also delete the user-group relationship record
    const relationshipId = `${username}:${groupId}`;
    await dynamodb.send(new DeleteCommand({
        TableName: tableName,
        Key: { PK: `USER_GROUP#${relationshipId}`, SK: `METADATA#${relationshipId}` }
    }));
    
    const response = await dynamodb.send(new UpdateCommand(params));
    return response.Attributes;
}

/**
 * Update a user
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} username - Username to update
 * @param {Object} input - User input data
 * @returns {Promise<Object>} - The updated user
 * @throws {Error} - If user doesn't have permission or username doesn't exist
 */
async function updateUser(dynamodb, tableName, userGroups, username, input) {
    // Only admins can update users
    if (!isAdmin(userGroups)) {
        throw new Error("Access denied: You do not have permission to update users");
    }
    
    // Check if user exists
    const existingUser = await getUserByUsername(dynamodb, tableName, username);
    if (!existingUser) {
        throw new Error(`User with username ${username} not found`);
    }
    
    // Build update expression
    const { expression, attributeNames, attributeValues } = buildUpdateExpression({
        email: input.email,
        disciplines: input.disciplines,
        equipments: input.equipments,
        updatedAt: new Date().toISOString()
    });
    
    // Update the user record
    const params = {
        TableName: tableName,
        Key: { PK: `USER#${username}`, SK: `METADATA#${username}` },
        UpdateExpression: expression,
        ExpressionAttributeNames: attributeNames,
        ExpressionAttributeValues: attributeValues,
        ReturnValues: 'ALL_NEW'
    };
    
    const response = await dynamodb.send(new UpdateCommand(params));
    return response.Attributes;
}

module.exports = {
    createUser,
    deleteUser,
    addUserToGroup,
    removeUserFromGroup,
    updateUser
};
