/**
 * Tests for SyncJob resolvers
 */
const { formatSyncJobItem, parseSyncJobSK, generateSyncJobId } = require('../syncjobs/helpers');
const { listSyncJobs, getLatestSyncJob, getProjectSyncHistory } = require('../syncjobs/queries');

// Mock DynamoDB client
const mockDynamoDb = {
    send: jest.fn()
};

// Mock table name
const tableName = 'test-table';

// Mock user groups
const userGroups = ['admin'];

describe('SyncJob Helpers', () => {
    describe('parseSyncJobSK', () => {
        test('should parse SK with projectId', () => {
            const result = parseSyncJobSK('ACTIVITIES#783#2025-04-30T06:45:48.671Z');
            expect(result).toEqual({
                syncType: 'ACTIVITIES',
                projectId: '783',
                timestamp: '2025-04-30T06:45:48.671Z'
            });
        });

        test('should parse SK without projectId', () => {
            const result = parseSyncJobSK('PROJECTS#2025-04-30T06:30:39.518Z');
            expect(result).toEqual({
                syncType: 'PROJECTS',
                projectId: null,
                timestamp: '2025-04-30T06:30:39.518Z'
            });
        });

        test('should handle invalid SK', () => {
            const result = parseSyncJobSK('INVALID');
            expect(result).toEqual({
                syncType: 'INVALID',
                projectId: null,
                timestamp: null
            });
        });

        test('should handle null SK', () => {
            const result = parseSyncJobSK(null);
            expect(result).toEqual({
                syncType: null,
                projectId: null,
                timestamp: null
            });
        });
    });

    describe('generateSyncJobId', () => {
        test('should generate ID from PK and SK', () => {
            const id = generateSyncJobId('SYNCJOB', 'ACTIVITIES#783#2025-04-30T06:45:48.671Z');
            expect(id).toBeTruthy();
            expect(typeof id).toBe('string');
        });

        test('should return null for invalid inputs', () => {
            expect(generateSyncJobId(null, 'SK')).toBeNull();
            expect(generateSyncJobId('PK', null)).toBeNull();
            expect(generateSyncJobId(null, null)).toBeNull();
        });
    });

    describe('formatSyncJobItem', () => {
        test('should format a SyncJob item', () => {
            const item = {
                PK: 'SYNCJOB',
                SK: 'ACTIVITIES#783#2025-04-30T06:45:48.671Z',
                status: 'SUCCEEDED',
                executionTime: 27.554,
                activitiesProcessed: 3621,
                timestamp: '2025-04-30T06:45:48.671Z',
                type: 'syncjob'
            };

            const result = formatSyncJobItem(item);
            expect(result).toMatchObject({
                syncType: 'ACTIVITIES',
                projectId: '783',
                timestamp: '2025-04-30T06:45:48.671Z',
                status: 'SUCCEEDED',
                executionTime: 27.554,
                activitiesProcessed: 3621
            });
            expect(result.id).toBeTruthy();
        });

        test('should handle null item', () => {
            expect(formatSyncJobItem(null)).toBeNull();
        });
    });
});

describe('SyncJob Queries', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('listSyncJobs', () => {
        test('should query SyncJob items', async () => {
            // Mock response
            const mockItems = [
                {
                    PK: 'SYNCJOB',
                    SK: 'ACTIVITIES#783#2025-04-30T06:45:48.671Z',
                    status: 'SUCCEEDED',
                    executionTime: 27.554,
                    activitiesProcessed: 3621,
                    timestamp: '2025-04-30T06:45:48.671Z',
                    type: 'syncjob'
                },
                {
                    PK: 'SYNCJOB',
                    SK: 'PROJECTS#2025-04-30T06:30:39.518Z',
                    status: 'SUCCEEDED',
                    executionTime: 1.278,
                    projectsProcessed: 48,
                    timestamp: '2025-04-30T06:30:39.518Z',
                    type: 'syncjob'
                }
            ];

            mockDynamoDb.send.mockResolvedValueOnce({
                Items: mockItems,
                Count: 2
            });

            // Call the function
            const result = await listSyncJobs(mockDynamoDb, tableName, userGroups, {});

            // Verify the result
            expect(result.items.length).toBe(2);
            expect(result.items[0].syncType).toBe('ACTIVITIES');
            expect(result.items[1].syncType).toBe('PROJECTS');
            expect(result.totalCount).toBe(2);
        });

        test('should apply filters', async () => {
            // Mock response
            mockDynamoDb.send.mockResolvedValueOnce({
                Items: [],
                Count: 0
            });

            // Call the function with filters
            await listSyncJobs(mockDynamoDb, tableName, userGroups, {
                syncType: 'ACTIVITIES',
                projectId: '783',
                status: 'SUCCEEDED'
            });

            // Verify the query parameters
            expect(mockDynamoDb.send).toHaveBeenCalledTimes(1);
            const queryParams = mockDynamoDb.send.mock.calls[0][0].input;
            expect(queryParams.FilterExpression).toContain('begins_with(SK, :syncType)');
            expect(queryParams.FilterExpression).toContain('(contains(SK, :projectIdPattern) OR projectId = :projectId)');
            expect(queryParams.FilterExpression).toContain('status = :status');
            expect(queryParams.ExpressionAttributeValues[':syncType']).toBe('ACTIVITIES#');
            expect(queryParams.ExpressionAttributeValues[':projectIdPattern']).toBe('#783#');
            expect(queryParams.ExpressionAttributeValues[':projectId']).toBe('783');
            expect(queryParams.ExpressionAttributeValues[':status']).toBe('SUCCEEDED');
        });
    });

    describe('getLatestSyncJob', () => {
        test('should return the latest sync job', async () => {
            // Mock response with multiple items
            const mockItems = [
                {
                    PK: 'SYNCJOB',
                    SK: 'ACTIVITIES#783#2025-04-30T06:45:48.671Z',
                    status: 'SUCCEEDED',
                    timestamp: '2025-04-30T06:45:48.671Z'
                },
                {
                    PK: 'SYNCJOB',
                    SK: 'ACTIVITIES#783#2025-04-29T06:45:48.671Z',
                    status: 'SUCCEEDED',
                    timestamp: '2025-04-29T06:45:48.671Z'
                }
            ];

            mockDynamoDb.send.mockResolvedValueOnce({
                Items: mockItems
            });

            // Call the function
            const result = await getLatestSyncJob(mockDynamoDb, tableName, userGroups, 'ACTIVITIES', '783');

            // Verify the result is the latest job
            expect(result.timestamp).toBe('2025-04-30T06:45:48.671Z');
        });

        test('should return null if no jobs found', async () => {
            // Mock empty response
            mockDynamoDb.send.mockResolvedValueOnce({
                Items: []
            });

            // Call the function
            const result = await getLatestSyncJob(mockDynamoDb, tableName, userGroups, 'ACTIVITIES', '783');

            // Verify the result
            expect(result).toBeNull();
        });
    });

    describe('getProjectSyncHistory', () => {
        test('should return sync history for a project', async () => {
            // Mock response
            const mockItems = [
                {
                    PK: 'SYNCJOB',
                    SK: 'ACTIVITIES#783#2025-04-30T06:45:48.671Z',
                    status: 'SUCCEEDED',
                    timestamp: '2025-04-30T06:45:48.671Z'
                },
                {
                    PK: 'SYNCJOB',
                    SK: 'ACTIVITYCODES#783#2025-04-30T07:01:28.895Z',
                    status: 'SUCCEEDED',
                    timestamp: '2025-04-30T07:01:28.895Z'
                }
            ];

            mockDynamoDb.send.mockResolvedValueOnce({
                Items: mockItems
            });

            // Call the function
            const result = await getProjectSyncHistory(mockDynamoDb, tableName, userGroups, '783');

            // Verify the result
            expect(result.length).toBe(2);
            expect(result[0].syncType).toBe('ACTIVITYCODES'); // Should be sorted by timestamp desc
            expect(result[1].syncType).toBe('ACTIVITIES');
        });
    });
});
