// __tests__/index.test.js

// Mock AWS SDK
jest.mock('@aws-sdk/client-dynamodb');
jest.mock('@aws-sdk/lib-dynamodb');

// Mock all imported modules
jest.mock('../activities/index');
jest.mock('../workPackages');
jest.mock('../projects');

// Import the mocked modules to set up mock implementations
const activities = require('../activities/index');
const workPackages = require('../workPackages');
const projects = require('../projects');

// Set environment variable
process.env.TABLE_NAME = 'MockedTableName';

// Import the handler function after mocking dependencies
const { handler } = require('../index');

describe('Lambda Handler', () => {
  // Reset all mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });

//   describe('WorkPackage field resolvers', () => {
//     test('should resolve activities field on WorkPackage', async () => {
//       const mockEvent = {
//         info: {
//           parentTypeName: 'WorkPackage',
//           fieldName: 'activities'
//         },
//         source: { id: 'wp123' },
//         identity: { groups: ['User'] }
//       };

//       activities.getActivitiesForWorkPackage.mockResolvedValue([
//         { id: 'act1', title: 'Activity 1' }
//       ]);

//       const result = await handler(mockEvent);

//       expect(activities.getActivitiesForWorkPackage).toHaveBeenCalledWith(
//         expect.any(Object), // dynamodb
//         'MockedTableName',
//         ['User'],
//         'wp123'
//       );
//       expect(result).toEqual([{ id: 'act1', title: 'Activity 1' }]);
//     });
//   });

  describe('Project field resolvers', () => {
    test('should resolve workPackages field on Project', async () => {
      const mockEvent = {
        info: {
          parentTypeName: 'Project',
          fieldName: 'workPackages'
        },
        source: { id: 'proj123' },
        identity: { groups: ['User'] }
      };

      workPackages.getWorkPackages.mockResolvedValue([
        { id: 'wp1', name: 'Work Package 1' }
      ]);

      const result = await handler(mockEvent);

      expect(workPackages.getWorkPackages).toHaveBeenCalledWith(
        expect.any(Object),
        'MockedTableName',
        'proj123'
      );
      expect(result).toEqual([{ id: 'wp1', name: 'Work Package 1' }]);
    });

    test('should resolve activities field on Project', async () => {
      const mockEvent = {
        info: {
          parentTypeName: 'Project',
          fieldName: 'activities'
        },
        source: { id: 'proj123' },
        arguments: { limit: 5, nextToken: 'token123' },
        identity: { groups: ['User'] }
      };

      activities.getActivitiesForProject.mockResolvedValue({
        items: [{ id: 'act1', title: 'Activity 1' }],
        nextToken: null,
        totalCount: 1
      });

      const result = await handler(mockEvent);

      expect(activities.getActivitiesForProject).toHaveBeenCalledWith(
        expect.any(Object),
        'MockedTableName',
        ['User'],
        'proj123',
        5,
        'token123'
      );
      expect(result).toEqual({
        items: [{ id: 'act1', title: 'Activity 1' }],
        nextToken: null,
        totalCount: 1
      });
    });
  });

  describe('Root-level queries and mutations', () => {
    test('should handle getActivity query', async () => {
      const mockEvent = {
        info: {
          fieldName: 'getActivity',
          parentTypeName: 'Query'
        },
        arguments: { id: 'act123' },
        identity: { groups: ['User'] }
      };

      activities.getActivity.mockResolvedValue({ 
        id: 'act123', 
        title: 'Test Activity' 
      });

      const result = await handler(mockEvent);

      expect(activities.getActivity).toHaveBeenCalledWith(
        expect.any(Object),
        'MockedTableName',
        ['User'],
        'act123'
      );
      expect(result).toEqual({ id: 'act123', title: 'Test Activity' });
    });

    test('should handle addActivity mutation', async () => {
      const mockInput = { title: 'New Activity', projectObjectId: 'proj123' };
      const mockEvent = {
        info: {
          fieldName: 'addActivity',
          parentTypeName: 'Mutation'
        },
        arguments: { input: mockInput },
        identity: { groups: ['Admin'] }
      };

      activities.addActivity.mockResolvedValue({
        id: 'new-act',
        ...mockInput
      });

      const result = await handler(mockEvent);

      expect(activities.addActivity).toHaveBeenCalledWith(
        expect.any(Object),
        'MockedTableName',
        ['Admin'],
        mockInput
      );
      expect(result).toEqual({ id: 'new-act', ...mockInput });
    });

    test('should handle updateActivity mutation', async () => {
      const mockInput = { title: 'Updated Activity' };
      const mockEvent = {
        info: {
          fieldName: 'updateActivity',
          parentTypeName: 'Mutation'
        },
        arguments: { id: 'act123', input: mockInput, username: 'test-user' },
        identity: { groups: ['Manager'] }
      };

      activities.updateActivity.mockResolvedValue({
        id: 'act123',
        title: 'Updated Activity'
      });

      const result = await handler(mockEvent);

      expect(activities.updateActivity).toHaveBeenCalledWith(
        expect.any(Object),
        'MockedTableName',
        ['Manager'],
        mockEvent
      );
      expect(result).toEqual({ id: 'act123', title: 'Updated Activity' });
    });

    test('should handle updateActivities mutation', async () => {
      const mockInputs = [
        { id: 'act1', title: 'Updated 1' },
        { id: 'act2', title: 'Updated 2' }
      ];
      const mockEvent = {
        info: {
          fieldName: 'updateActivities',
          parentTypeName: 'Mutation'
        },
        arguments: { input: mockInputs, username: 'test-user' },
        identity: { groups: ['Manager'] }
      };

      activities.updateActivities.mockResolvedValue([
        { id: 'act1', title: 'Updated 1' },
        { id: 'act2', title: 'Updated 2' }
      ]);

      const result = await handler(mockEvent);

      expect(activities.updateActivities).toHaveBeenCalledWith(
        expect.any(Object),
        'MockedTableName',
        ['Manager'],
        mockEvent
      );
      expect(result).toEqual([
        { id: 'act1', title: 'Updated 1' },
        { id: 'act2', title: 'Updated 2' }
      ]);
    });

    test('should handle deleteActivity mutation', async () => {
      const mockEvent = {
        info: {
          fieldName: 'deleteActivity',
          parentTypeName: 'Mutation'
        },
        arguments: { id: 'act123' },
        identity: { groups: ['Admin'] }
      };

      activities.deleteActivity.mockResolvedValue('act123');

      const result = await handler(mockEvent);

      expect(activities.deleteActivity).toHaveBeenCalledWith(
        expect.any(Object),
        'MockedTableName',
        ['Admin'],
        'act123'
      );
      expect(result).toEqual('act123');
    });

    test('should handle getWorkPackage query', async () => {
      const mockEvent = {
        info: {
          fieldName: 'getWorkPackage'
        },
        arguments: { id: 'wp123' },
        identity: { groups: ['User'] }
      };

      workPackages.getWorkPackage.mockResolvedValue({ 
        id: 'wp123', 
        name: 'Test Work Package' 
      });

      const result = await handler(mockEvent);

      expect(workPackages.getWorkPackage).toHaveBeenCalledWith(
        expect.any(Object),
        'MockedTableName',
        'wp123'
      );
      expect(result).toEqual({ id: 'wp123', name: 'Test Work Package' });
    });

    test('should handle getProject query', async () => {
      const mockEvent = {
        info: {
          fieldName: 'getProject'
        },
        arguments: { id: 'proj123' },
        identity: { groups: ['User'] }
      };

      projects.getProject.mockResolvedValue({ 
        id: 'proj123', 
        name: 'Test Project' 
      });

      const result = await handler(mockEvent);

      expect(projects.getProject).toHaveBeenCalledWith(
        expect.any(Object),
        'MockedTableName',
        ['User'],
        'proj123'
      );
      expect(result).toEqual({ id: 'proj123', name: 'Test Project' });
    });
    
    test('should handle listProjects query', async () => {
      const mockEvent = {
        info: {
          fieldName: 'listProjects'
        },
        arguments: { limit: 10, nextToken: 'token123' },
        identity: { groups: ['User'] }
      };

      projects.listProjects.mockResolvedValue({
        items: [{ id: 'proj1', name: 'Project 1' }],
        nextToken: null,
        totalCount: 1
      });

      const result = await handler(mockEvent);

      expect(projects.listProjects).toHaveBeenCalledWith(
        expect.any(Object),
        'MockedTableName',
        ['User'],
        10,
        'token123'
      );
      expect(result).toEqual({
        items: [{ id: 'proj1', name: 'Project 1' }],
        nextToken: null,
        totalCount: 1
      });
    });
  });

  describe('Error handling', () => {
    test('should pass through access denied errors', async () => {
      const mockEvent = {
        info: {
          fieldName: 'getActivity'
        },
        arguments: { id: 'act123' },
        identity: { groups: ['User'] }
      };

      const accessError = new Error('Access denied to this resource');
      activities.getActivity.mockRejectedValue(accessError);

      await expect(handler(mockEvent)).rejects.toEqual(accessError);
    });

    test('should wrap other errors', async () => {
      const mockEvent = {
        info: {
          fieldName: 'getActivity'
        },
        arguments: { id: 'act123' },
        identity: { groups: ['User'] }
      };

      const originalError = new Error('Something went wrong');
      activities.getActivity.mockRejectedValue(originalError);

      await expect(handler(mockEvent)).rejects.toThrow('Operation failed: Something went wrong');
    });

    test('should throw error for unknown field name', async () => {
      const mockEvent = {
        info: {
          fieldName: 'unknownField'
        },
        identity: { groups: ['User'] }
      };

      await expect(handler(mockEvent)).rejects.toThrow('Unknown field name: unknownField');
    });
  });

  test('should handle missing identity or arguments', async () => {
    const mockEvent = {
      info: {
        fieldName: 'getActivity'
      },
      arguments: { id: 'act123' }
      // No identity provided
    };

    activities.getActivity.mockResolvedValue({ id: 'act123', title: 'Activity' });

    const result = await handler(mockEvent);

    // Should call with empty array for userGroups
    expect(activities.getActivity).toHaveBeenCalledWith(
      expect.any(Object),
      'MockedTableName',
      [],
      'act123'
    );
    expect(result).toEqual({ id: 'act123', title: 'Activity' });
  });
});
