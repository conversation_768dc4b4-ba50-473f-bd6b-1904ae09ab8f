
# AWS Lambda Function for AppSync Resolver Integration

This Lambda function is designed to integrate with AWS AppSync as a resolver for a GraphQL API. It supports multiple queries for managing entities like plants, projects, scopes, work orders, and activities, using a single-table design in DynamoDB.

## Features
- Handles multiple GraphQL queries:
  - `getPlants`
  - `getPlant`
  - `getProject`
  - `getScope`
  - `getWorkOrder`
  - `getActivity`
- Dynamically resolves the field requested in the GraphQL API.
- Optimized for a single-table design in DynamoDB.
- Implements clean error handling and modular query functions.

## How It Works

### Entry Point
The `index.handler` function serves as the entry point:
1. Receives the GraphQL event with a `fieldName` and arguments.
2. Routes the request to the corresponding query function based on the `fieldName`.
3. Returns the result or throws an error for unsupported fields or failures.

### Query Functions
Each query function is responsible for interacting with DynamoDB using the AWS SDK:
- **`getPlants`**: Retrieves all plants by querying the table where `PK = "PLANT"`.
- **`getPlant`**: Fetches a specific plant by constructing the `PK` and `SK` from the `plantId`.
- **`getProject`**: Retrieves a specific project based on its ID.
- **`getScope`**: Fetches a scope by its ID.
- **`getWorkOrder`**: Fetches a work order by its ID.
- **`getActivity`**: Retrieves an activity by its ID.

### Error Handling
- Logs the error details for debugging.
- Returns a meaningful error message if the requested `fieldName` is not supported.

## Environment Variables
The function requires the following environment variable:
- **`TABLE_NAME`**: Name of the DynamoDB table where the data is stored.

## DynamoDB Table Design
This Lambda function assumes a single-table design with the following composite keys:
- **Partition Key (`PK`)**: Identifies the entity type and unique identifier (e.g., `PLANT#123` or `PROJECT#456`).
- **Sort Key (`SK`)**: Used for metadata and relationships (e.g., `METADATA#123`).

### Example Table Entries
| PK            | SK                | Additional Attributes       |
|---------------|-------------------|-----------------------------|
| `PLANT#123`   | `METADATA#123`    | `name`, `location`, etc.    |
| `PROJECT#456` | `METADATA#456`    | `name`, `description`, etc. |
| `SCOPE#789`   | `METADATA#789`    | `name`, `projectId`, etc.   |
| `WORKORDER#1` | `METADATA#1`      | `description`, `scopeId`, etc. |
| `ACTIVITY#10` | `METADATA#10`     | `status`, `progress`, etc.  |

## Setup Instructions
1. **Deploy to AWS Lambda**:
   - Package the function and dependencies into a `.zip` file.
   - Deploy the `.zip` file to a Lambda function in AWS.
2. **Set Environment Variables**:
   - Add the `TABLE_NAME` environment variable to the Lambda function configuration.
3. **Connect to AppSync**:
   - Attach the Lambda function as a data source in your AppSync API.
   - Map resolvers to the appropriate GraphQL fields.
4. **Test**:
   - Use AWS AppSync's query console to test the integrated queries.

## Example Usage
### GraphQL Query
```graphql
query GetPlants {
  getPlants {
    id
    name
  }
}
```

### Expected DynamoDB Query
```plaintext
KeyConditionExpression: "PK = :pk",
ExpressionAttributeValues: { ":pk": "PLANT" }
```

### Response
```json
[
  {
    "id": "123",
    "name": "Plant A"
  },
  {
    "id": "124",
    "name": "Plant B"
  }
]
```

## Error Scenarios
- **Invalid `fieldName`**:
  - Logs an error and returns: `Unsupported field: {fieldName}`.
- **DynamoDB Query Failure**:
  - Logs the error and throws an exception with the error message.

## Dependencies
- **AWS SDK**: For interacting with DynamoDB.
  - Installed by default in AWS Lambda runtime environments.

## License
This project is open-source and available under the MIT license.
