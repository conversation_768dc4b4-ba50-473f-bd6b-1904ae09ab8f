/**
 * Unit tests for SyncJob query operations
 */
const { listSyncJobs } = require('../queries');
const { formatSyncJobItem } = require('../helpers');
const { isAdminWorkerOperatorOrManager } = require('../../utils/permissions');
const { QueryCommand } = require('@aws-sdk/lib-dynamodb');

// Mock dependencies
jest.mock('../helpers', () => ({
    formatSyncJobItem: jest.fn(item => item ? { 
        id: 'mocked-id',
        syncType: item.SK?.split('#')[0] || 'unknown',
        projectId: item.projectId || null,
        timestamp: item.timestamp || null,
        status: item.status,
        executionTime: item.executionTime,
        ...item // Pass through other properties
    } : null)
}));

jest.mock('../../utils/permissions', () => ({
    isAdminWorkerOperatorOrManager: {
        admin: jest.fn()
    }
}));

// Mock AWS SDK
jest.mock('@aws-sdk/lib-dynamodb', () => ({
    QueryCommand: jest.fn()
}));

describe('SyncJob Queries', () => {
    // Mock DynamoDB client
    const mockDynamoDb = {
        send: jest.fn()
    };
    
    // Mock table name
    const tableName = 'test-table';
    
    // Mock user groups
    const adminGroups = ['admin'];
    const nonAdminGroups = ['User'];
    
    beforeEach(() => {
        jest.clearAllMocks();
        
        // Default mock for isAdminWorkerOperatorOrManager.admin
        isAdminWorkerOperatorOrManager.admin.mockImplementation(groups => groups.includes('admin'));
    });
    
    describe('listSyncJobs', () => {
        test('should query SyncJob items with default parameters', async () => {
            // Arrange
            const mockItems = [
                {
                    PK: 'SYNCJOB',
                    SK: 'ACTIVITIES#783#2025-04-30T06:45:48.671Z',
                    status: 'SUCCEEDED',
                    executionTime: 27.554,
                    activitiesProcessed: 3621,
                    timestamp: '2025-04-30T06:45:48.671Z',
                    type: 'syncjob'
                },
                {
                    PK: 'SYNCJOB',
                    SK: 'PROJECTS#2025-04-30T06:30:39.518Z',
                    status: 'SUCCEEDED',
                    executionTime: 1.278,
                    projectsProcessed: 48,
                    timestamp: '2025-04-30T06:30:39.518Z',
                    type: 'syncjob'
                }
            ];

            mockDynamoDb.send.mockResolvedValueOnce({
                Items: mockItems,
                Count: 2
            });

            // Act
            const result = await listSyncJobs(mockDynamoDb, tableName, adminGroups);

            // Assert
            expect(isAdminWorkerOperatorOrManager.admin).toHaveBeenCalledWith(adminGroups);
            expect(QueryCommand).toHaveBeenCalledWith(expect.objectContaining({
                TableName: tableName,
                KeyConditionExpression: 'PK = :pk',
                ExpressionAttributeValues: { ':pk': 'SYNCJOB' }
            }));
            expect(mockDynamoDb.send).toHaveBeenCalledTimes(1);
            expect(formatSyncJobItem).toHaveBeenCalledTimes(2);
            
            expect(result.items.length).toBe(2);
            expect(result.totalCount).toBe(2);
            expect(result.nextToken).toBeNull();
        });

        test('should throw error when user is not admin', async () => {
            // Arrange
            isAdminWorkerOperatorOrManager.admin.mockReturnValue(false);
            
            // Act & Assert
            await expect(listSyncJobs(
                mockDynamoDb, 
                tableName, 
                nonAdminGroups
            )).rejects.toThrow('Insufficient permissions: Admin access required');
            
            expect(isAdminWorkerOperatorOrManager.admin).toHaveBeenCalledWith(nonAdminGroups);
            expect(mockDynamoDb.send).not.toHaveBeenCalled();
        });

        test('should filter by syncType only', async () => {
            // Arrange
            mockDynamoDb.send.mockResolvedValueOnce({
                Items: [],
                Count: 0
            });

            // Act
            await listSyncJobs(mockDynamoDb, tableName, adminGroups, {
                syncType: 'ACTIVITIES'
            });

            // Assert
            expect(QueryCommand).toHaveBeenCalledWith(expect.objectContaining({
                KeyConditionExpression: 'PK = :pk AND begins_with(SK, :syncType)',
                ExpressionAttributeValues: { 
                    ':pk': 'SYNCJOB',
                    ':syncType': 'ACTIVITIES#'
                }
            }));
        });

        test('should filter by projectId only', async () => {
            // Arrange
            mockDynamoDb.send.mockResolvedValueOnce({
                Items: [],
                Count: 0
            });

            // Act
            await listSyncJobs(mockDynamoDb, tableName, adminGroups, {
                projectId: '783'
            });

            // Assert
            expect(QueryCommand).toHaveBeenCalledWith(expect.objectContaining({
                KeyConditionExpression: 'PK = :pk',
                FilterExpression: 'contains(SK, :projectId) OR projectId = :projectId',
                ExpressionAttributeValues: { 
                    ':pk': 'SYNCJOB',
                    ':projectId': '783'
                }
            }));
        });

        test('should filter by syncType and projectId', async () => {
            // Arrange
            mockDynamoDb.send.mockResolvedValueOnce({
                Items: [],
                Count: 0
            });

            // Act
            await listSyncJobs(mockDynamoDb, tableName, adminGroups, {
                syncType: 'ACTIVITIES',
                projectId: '783'
            });

            // Assert
            expect(QueryCommand).toHaveBeenCalledWith(expect.objectContaining({
                KeyConditionExpression: 'PK = :pk AND begins_with(SK, :syncTypeProj)',
                ExpressionAttributeValues: { 
                    ':pk': 'SYNCJOB',
                    ':syncTypeProj': 'ACTIVITIES#783#'
                }
            }));
        });

        test('should filter by status', async () => {
            // Arrange
            mockDynamoDb.send.mockResolvedValueOnce({
                Items: [],
                Count: 0
            });

            // Act
            await listSyncJobs(mockDynamoDb, tableName, adminGroups, {
                status: 'SUCCEEDED'
            });

            // Assert
            expect(QueryCommand).toHaveBeenCalledWith(expect.objectContaining({
                KeyConditionExpression: 'PK = :pk',
                FilterExpression: 'status = :status',
                ExpressionAttributeValues: { 
                    ':pk': 'SYNCJOB',
                    ':status': 'SUCCEEDED'
                }
            }));
        });

        test('should combine filters correctly', async () => {
            // Arrange
            mockDynamoDb.send.mockResolvedValueOnce({
                Items: [],
                Count: 0
            });

            // Act
            await listSyncJobs(mockDynamoDb, tableName, adminGroups, {
                projectId: '783',
                status: 'SUCCEEDED'
            });

            // Assert
            expect(QueryCommand).toHaveBeenCalledWith(expect.objectContaining({
                KeyConditionExpression: 'PK = :pk',
                FilterExpression: 'contains(SK, :projectId) OR projectId = :projectId AND status = :status',
                ExpressionAttributeValues: { 
                    ':pk': 'SYNCJOB',
                    ':projectId': '783',
                    ':status': 'SUCCEEDED'
                }
            }));
        });

        test('should handle pagination', async () => {
            // Arrange
            const nextToken = JSON.stringify({ lastKey: 'some-key' });
            
            mockDynamoDb.send.mockResolvedValueOnce({
                Items: [],
                Count: 0,
                LastEvaluatedKey: { lastKey: 'next-key' }
            });

            // Act
            const result = await listSyncJobs(mockDynamoDb, tableName, adminGroups, {
                limit: 5,
                nextToken
            });

            // Assert
            expect(QueryCommand).toHaveBeenCalledWith(expect.objectContaining({
                ExclusiveStartKey: { lastKey: 'some-key' }
            }));
            expect(result.nextToken).toBe(JSON.stringify({ lastKey: 'next-key' }));
        });

        test('should handle DynamoDB errors', async () => {
            // Arrange
            mockDynamoDb.send.mockRejectedValueOnce(new Error('DynamoDB error'));

            // Act & Assert
            await expect(listSyncJobs(
                mockDynamoDb, 
                tableName, 
                adminGroups
            )).rejects.toThrow('Failed to list sync jobs: DynamoDB error');
        });
    });
});
