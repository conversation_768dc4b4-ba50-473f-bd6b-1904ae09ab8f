/**
 * Unit tests for SyncJob helper functions
 */
const { 
    parseSyncJobSK, 
    generateSyncJobId, 
    formatSyncJobItem 
} = require('../helpers');

describe('SyncJob Helpers', () => {
    describe('parseSyncJobSK', () => {
        test('should parse SK with projectId', () => {
            const result = parseSyncJobSK('ACTIVITIES#783#2025-04-30T06:45:48.671Z');
            expect(result).toEqual({
                syncType: 'ACTIVITIES',
                projectId: '783',
                timestamp: '2025-04-30T06:45:48.671Z'
            });
        });

        test('should parse SK without projectId', () => {
            const result = parseSyncJobSK('PROJECTS#2025-04-30T06:30:39.518Z');
            expect(result).toEqual({
                syncType: 'PROJECTS',
                projectId: null,
                timestamp: '2025-04-30T06:30:39.518Z'
            });
        });

        test('should handle invalid SK format', () => {
            const result = parseSyncJobSK('INVALID');
            expect(result).toEqual({
                syncType: null,
                projectId: null,
                timestamp: null
            });
        });

        test('should handle null SK', () => {
            const result = parseSyncJobSK(null);
            expect(result).toEqual({
                syncType: null,
                projectId: null,
                timestamp: null
            });
        });

        test('should handle undefined SK', () => {
            const result = parseSyncJobSK(undefined);
            expect(result).toEqual({
                syncType: null,
                projectId: null,
                timestamp: null
            });
        });
    });

    describe('generateSyncJobId', () => {
        test('should generate ID from PK and SK', () => {
            const id = generateSyncJobId('SYNCJOB', 'ACTIVITIES#783#2025-04-30T06:45:48.671Z');
            expect(id).toBeTruthy();
            expect(typeof id).toBe('string');
            // Verify it's a base64 string
            expect(() => Buffer.from(id, 'base64')).not.toThrow();
        });

        test('should return null for null PK', () => {
            const id = generateSyncJobId(null, 'ACTIVITIES#783#2025-04-30T06:45:48.671Z');
            expect(id).toBeNull();
        });

        test('should return null for null SK', () => {
            const id = generateSyncJobId('SYNCJOB', null);
            expect(id).toBeNull();
        });

        test('should return null for both null inputs', () => {
            const id = generateSyncJobId(null, null);
            expect(id).toBeNull();
        });

        test('should return null for undefined inputs', () => {
            const id = generateSyncJobId(undefined, undefined);
            expect(id).toBeNull();
        });

        test('should generate consistent IDs for the same inputs', () => {
            const id1 = generateSyncJobId('SYNCJOB', 'ACTIVITIES#783#2025-04-30T06:45:48.671Z');
            const id2 = generateSyncJobId('SYNCJOB', 'ACTIVITIES#783#2025-04-30T06:45:48.671Z');
            expect(id1).toBe(id2);
        });

        test('should generate different IDs for different inputs', () => {
            const id1 = generateSyncJobId('SYNCJOB', 'ACTIVITIES#783#2025-04-30T06:45:48.671Z');
            const id2 = generateSyncJobId('SYNCJOB', 'PROJECTS#2025-04-30T06:30:39.518Z');
            expect(id1).not.toBe(id2);
        });
    });

    describe('formatSyncJobItem', () => {
        test('should format a SyncJob item with projectId in SK', () => {
            const item = {
                PK: 'SYNCJOB',
                SK: 'ACTIVITIES#783#2025-04-30T06:45:48.671Z',
                status: 'SUCCEEDED',
                executionTime: 27.554,
                fetchTime: 10.123,
                writeTime: 17.431,
                activitiesProcessed: 3621,
                type: 'syncjob'
            };

            const result = formatSyncJobItem(item);
            
            expect(result).toMatchObject({
                syncType: 'ACTIVITIES',
                projectId: '783',
                timestamp: '2025-04-30T06:45:48.671Z',
                status: 'SUCCEEDED',
                executionTime: 27.554,
                fetchTime: 10.123,
                writeTime: 17.431,
                activitiesProcessed: 3621
            });
            expect(result.id).toBeTruthy();
        });

        test('should format a SyncJob item without projectId in SK', () => {
            const item = {
                PK: 'SYNCJOB',
                SK: 'PROJECTS#2025-04-30T06:30:39.518Z',
                status: 'SUCCEEDED',
                executionTime: 1.278,
                fetchTime: 0.5,
                writeTime: 0.778,
                projectsProcessed: 48,
                projectId: 'fallback-id', // Should use this when not in SK
                type: 'syncjob'
            };

            const result = formatSyncJobItem(item);
            
            expect(result).toMatchObject({
                syncType: 'PROJECTS',
                projectId: 'fallback-id', // Should use the fallback from item
                timestamp: '2025-04-30T06:30:39.518Z',
                status: 'SUCCEEDED',
                executionTime: 1.278,
                fetchTime: 0.5,
                writeTime: 0.778,
                projectsProcessed: 48
            });
            expect(result.id).toBeTruthy();
        });

        test('should handle null item', () => {
            const result = formatSyncJobItem(null);
            expect(result).toBeNull();
        });

        test('should handle undefined item', () => {
            const result = formatSyncJobItem(undefined);
            expect(result).toBeNull();
        });

        test('should include all processing counts when present', () => {
            const item = {
                PK: 'SYNCJOB',
                SK: 'ACTIVITIES#783#2025-04-30T06:45:48.671Z',
                status: 'SUCCEEDED',
                executionTime: 27.554,
                projectsProcessed: 1,
                activitiesProcessed: 3621,
                activityCodesProcessed: 42,
                relationshipsProcessed: 1500,
                type: 'syncjob'
            };

            const result = formatSyncJobItem(item);
            
            expect(result).toMatchObject({
                projectsProcessed: 1,
                activitiesProcessed: 3621,
                activityCodesProcessed: 42,
                relationshipsProcessed: 1500
            });
        });
    });
});
