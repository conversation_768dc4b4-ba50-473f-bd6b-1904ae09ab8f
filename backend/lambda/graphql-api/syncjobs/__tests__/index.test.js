/**
 * Unit tests for SyncJob module main exports
 */
const syncJobsModule = require('../index');

// Mock the imported modules
jest.mock('../queries', () => ({
    listSyncJobs: jest.fn()
}));

describe('SyncJob Module', () => {
    test('should export all required functions', () => {
        // Assert
        expect(syncJobsModule).toHaveProperty('listSyncJobs');
    });

    test('should export the correct functions from queries', () => {
        // Import the original modules
        const { listSyncJobs } = require('../queries');
        
        // Assert that the exports match the original functions
        expect(syncJobsModule.listSyncJobs).toBe(listSyncJobs);
    });
});
