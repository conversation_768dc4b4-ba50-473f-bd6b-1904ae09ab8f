/**
 * Helper functions for SyncJob operations
 */

/**
 * Parse the SyncJob sort key to extract syncType, projectId, and timestamp
 * @param {string} sk - Sort key in format "TYPE#projectId#timestamp" or "TYPE#timestamp"
 * @returns {Object} - Object with syncType, projectId, and timestamp
 */
function parseSyncJobSK(sk) {
    if (!sk) {
        return { syncType: null, projectId: null, timestamp: null };
    }

    const parts = sk.split('#');
    
    // Handle different SK formats
    if (parts.length === 3) {
        // Format: TYPE#projectId#timestamp (e.g., ACTIVITIES#783#2025-04-30T06:45:48.671Z)
        return {
            syncType: parts[0],
            projectId: parts[1],
            timestamp: parts[2]
        };
    } else if (parts.length === 2) {
        // Format: TYPE#timestamp (e.g., PROJECTS#2025-04-30T06:30:39.518Z)
        return {
            syncType: parts[0],
            projectId: null,
            timestamp: parts[1]
        };
    }
    
    // Default case if format doesn't match
    return { syncType: null, projectId: null, timestamp: null };
}

/**
 * Generate a unique ID for a SyncJob from PK and SK
 * @param {string} pk - Partition key
 * @param {string} sk - Sort key
 * @returns {string} - Unique ID
 */
function generateSyncJobId(pk, sk) {
    if (!pk || !sk) {
        return null;
    }
    
    // Create a hash of PK and SK
    return Buffer.from(`${pk}#${sk}`).toString('base64');
}

/**
 * Format a DynamoDB SyncJob item to match the GraphQL schema
 * @param {Object} item - DynamoDB item
 * @returns {Object} - Formatted SyncJob object
 */
function formatSyncJobItem(item) {
    if (!item) {
        return null;
    }
    
    const { syncType, projectId, timestamp } = parseSyncJobSK(item.SK);
    
    return {
        id: generateSyncJobId(item.PK, item.SK),
        syncType,
        projectId: projectId || item.projectId, // Use projectId from SK if available, otherwise from item
        timestamp,
        status: item.status,
        executionTime: item.executionTime,
        fetchTime: item.fetchTime,
        writeTime: item.writeTime,
        projectsProcessed: item.projectsProcessed,
        activitiesProcessed: item.activitiesProcessed,
        activityCodesProcessed: item.activityCodesProcessed,
        relationshipsProcessed: item.relationshipsProcessed
    };
}

module.exports = {
    parseSyncJobSK,
    generateSyncJobId,
    formatSyncJobItem
};
