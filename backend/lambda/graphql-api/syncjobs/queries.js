/**
 * SyncJob query operations
 */
const { QueryCommand } = require('@aws-sdk/lib-dynamodb');
const { formatSyncJobItem } = require('./helpers');
const { isAdminWorkerOperatorOrManager } = require('../utils/permissions');


/**
 * List sync jobs with optional filtering
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {Object} args - Query arguments
 * @returns {Promise<Object>} - SyncJob connection with items and pagination info
 */
async function listSyncJobs(dynamodb, tableName, userGroups, args = {}) {

    const { limit = 50, nextToken, syncType, projectId, status } = args;

    // Check user permissions
    if (!isAdminWorkerOperatorOrManager.admin(userGroups)) {
        throw new Error("Insufficient permissions: Admin access required");
    }
    
    // TODO: implement limit and pagination

    // Build query parameters
    const queryParams = {
        TableName: tableName,
        KeyConditionExpression: 'PK = :pk',
        ExpressionAttributeValues: {
            ':pk': 'SYNCJOB'
        }
    };
    
    // Add filter expressions if filters are provided
    const filterExpressions = [];
    
    // Handle SK condition based on syncType and projectId
    if (syncType && projectId) {
        // For activities, activitycodes, relationships that have projectId in SK
        queryParams.KeyConditionExpression += ' AND begins_with(SK, :syncTypeProj)';
        queryParams.ExpressionAttributeValues[':syncTypeProj'] = `${syncType}#${projectId}#`;
    } else if (syncType) {
        // For filtering by syncType only
        queryParams.KeyConditionExpression += ' AND begins_with(SK, :syncType)';
        queryParams.ExpressionAttributeValues[':syncType'] = `${syncType}#`;
    } else if (projectId) {
        // For projects or when only projectId is provided
        // Use FilterExpression instead of KeyConditionExpression for projectId
        filterExpressions.push('contains(SK, :projectId) OR projectId = :projectId');
        queryParams.ExpressionAttributeValues[':projectId'] = projectId;
    }

    if (status) {
        filterExpressions.push('status = :status');
        queryParams.ExpressionAttributeValues[':status'] = status;
    }
    
    // Combine filter expressions if any
    if (filterExpressions.length > 0) {
        queryParams.FilterExpression = filterExpressions.join(' AND ');
    }
    
    // Handle pagination
    if (nextToken) {
        try {
            queryParams.ExclusiveStartKey = JSON.parse(nextToken);
        } catch (error) {
            console.error('Error parsing nextToken:', error);
            throw new Error('Invalid pagination token');
        }
    }
    
    try {
        // Execute the query
        const result = await dynamodb.send(new QueryCommand(queryParams));
        
        // Format the results
        const items = (result.Items || []).map(item => formatSyncJobItem(item));
        
        // Return the connection object
        return {
            items,
            nextToken: result.LastEvaluatedKey ? JSON.stringify(result.LastEvaluatedKey) : null,
            totalCount: items.length // Note: This is not the total count across all pages
        };
    } catch (error) {
        console.error('Error in listSyncJobs:', error);
        throw new Error(`Failed to list sync jobs: ${error.message}`);
    }
}

module.exports = {
    listSyncJobs
};
