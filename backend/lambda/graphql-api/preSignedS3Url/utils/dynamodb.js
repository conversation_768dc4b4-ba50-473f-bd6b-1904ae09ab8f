/**
 * DynamoDB utility functions
 */
const { GetCommand } = require('@aws-sdk/lib-dynamodb');

/**
 * Get an item by its primary key
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} pk - Partition key value
 * @param {string} sk - Sort key value
 * @returns {Promise<Object>} - The item or undefined if not found
 */
async function getItem(dynamodb, tableName, pk, sk) {
    const params = {
        TableName: tableName,
        Key: { PK: pk, SK: sk }
    };
    const response = await dynamodb.send(new GetCommand(params));
    return response.Item;
}

module.exports = {
    getItem
};
