/**
 * Permission utility functions
 */

const UserRoleGroup = {
    ADMIN: 'admin',
    EXECUTOR: 'executor',
    MANAGER: 'manager',
    OPERATOR: 'operator'
};

/**
 * Check if user has a specific role
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} role - Role to check
 * @returns {boolean} - True if user has the role
 */
function hasRole(userGroups, role) {
    return userGroups.includes(role);
}

/**
 * Check if user has any of the specified roles
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {Array<string>} roles - Roles to check
 * @returns {boolean} - True if user has any of the roles
 */
function hasRoles(userGroups, roles) {
    if (!userGroups || !roles) return false;
    return roles.some(role => hasRole(userGroups, role));
}

/**
 * Check if user is an admin
 * @param {Array<string>} userGroups - User groups for authorization
 * @returns {boolean} - True if user is an admin
 */
function isAdmin(userGroups) {
    return hasRole(userGroups, UserRoleGroup.ADMIN);
}

module.exports = {
    UserRoleGroup,
    hasRole,
    hasRoles,
    isAdmin
};
