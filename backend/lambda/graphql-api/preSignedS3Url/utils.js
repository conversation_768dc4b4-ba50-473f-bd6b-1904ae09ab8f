/**
 * Utility functions for S3 operations
 */

/**
 * Extract file extension from a filename
 * @param {string} fileName - The file name
 * @returns {string} - The file extension
 */
function getFileExtension(fileName) {
    return fileName.split('.').pop();
}

/**
 * Generate a content type based on file extension
 * @param {string} fileName - The file name
 * @returns {string} - The content type
 */
function getContentType(fileName) {
    const extension = getFileExtension(fileName).toLowerCase();
    const contentTypes = {
        'pdf': 'application/pdf',
        'doc': 'application/msword',
        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xls': 'application/vnd.ms-excel',
        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'ppt': 'application/vnd.ms-powerpoint',
        'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'gif': 'image/gif',
        'txt': 'text/plain',
        'csv': 'text/csv',
        'html': 'text/html',
        'json': 'application/json',
        'xml': 'application/xml',
        'zip': 'application/zip',
        'rar': 'application/x-rar-compressed',
        'tar': 'application/x-tar',
        'gz': 'application/gzip'
    };
    
    return contentTypes[extension] || 'application/octet-stream';
}

/**
 * Format S3 key with optional prefix
 * @param {string} key - The S3 key
 * @param {string} prefix - Optional prefix
 * @returns {string} - The formatted key
 */
function formatS3Key(key, prefix = '') {
    if (!prefix) {
        return key;
    }
    
    // Ensure prefix ends with a slash
    const formattedPrefix = prefix.endsWith('/') ? prefix : `${prefix}/`;
    return `${formattedPrefix}${key}`;
}

module.exports = {
    getFileExtension,
    getContentType,
    formatS3Key
};
