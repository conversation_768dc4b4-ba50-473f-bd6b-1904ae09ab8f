/**
 * Internal helper functions for preSignedS3Url operations
 */
const { getItem } = require('../utils/dynamodb');

/**
 * Get an activity by its ID
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} id - Activity ID
 * @returns {Promise<Object>} - The activity or undefined if not found
 */
async function getActivityById(dynamodb, tableName, id) {
    return await getItem(dynamodb, tableName, `ACTIVITY#${id}`, `METADATA#${id}`);
}

/**
 * Get a project by its ID
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {string} projectId - Project ID
 * @returns {Promise<Object>} - The project or undefined if not found
 */
async function getProjectById(dynamodb, tableName, projectId) {
    return await getItem(dynamodb, tableName, `PROJECT#${projectId}`, `METADATA#${projectId}`);
}

module.exports = {
    getActivityById,
    getProjectById
};
