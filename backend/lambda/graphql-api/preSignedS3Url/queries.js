/**
 * PreSignedS3Url query operations
 */
const { S3Client, GetObjectCommand } = require('@aws-sdk/client-s3');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
const { isAdmin, hasRoles } = require('../utils/permissions');
const { getActivityById, getProjectById } = require('./helpers');

// Initialize S3 client
const s3Client = new S3Client({});

/**
 * Generate a pre-signed URL for downloading an S3 object
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} key - S3 object key
 * @param {string} activityId - Activity ID for permission check
 * @returns {Promise<Object>} - Object containing the signed URL and key
 * @throws {Error} - If user doesn't have permission or parameters are invalid
 */
async function getDownloadUrl(dynamodb, tableName, userGroups, key, activityId) {
    if (!key) {
        throw new Error('Key is required for download operations');
    }

    const bucketName = process.env.S3_BUCKET_NAME;
    if (!bucketName) {
        throw new Error('S3_BUCKET_NAME environment variable is not set');
    }

    // Check if user is an admin
    const isUserAdmin = isAdmin(userGroups);
    
    if (!isUserAdmin) {
        // If not admin, check if user has access to the activity
        if (!activityId) {
            throw new Error('Activity ID is required for permission check');
        }
        
        // Get the activity
        const activity = await getActivityById(dynamodb, tableName, activityId);
        if (!activity) {
            throw new Error('Activity not found');
        }
        
        // Get the project
        const project = await getProjectById(dynamodb, tableName, activity.projectObjectId);
        if (!project) {
            throw new Error('Project not found');
        }
        
        // Check if user is in one of the project's workerGroups
        const isWorker = hasRoles(userGroups, project.workerGroups);
        
        if (!isWorker) {
            throw new Error('Access denied: You do not have permission to access this resource');
        }
    }

    // Create the command for getting the object
    const command = new GetObjectCommand({ 
        Bucket: bucketName, 
        Key: key 
    });

    try {
        // Generate the signed URL
        const signedUrl = await getSignedUrl(s3Client, command, { expiresIn: 3600 });
        return { url: signedUrl, key };
    } catch (error) {
        console.error(`Error generating signed URL for download:`, error);
        throw error;
    }
}

module.exports = {
    getDownloadUrl
};
