/**
 * PreSignedS3Url mutation operations
 */
const { S3Client, PutObjectCommand, DeleteObjectCommand } = require('@aws-sdk/client-s3');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
const { randomUUID } = require('crypto');
const { isAdmin, hasRoles } = require('../utils/permissions');
const { getActivityById, getProjectById } = require('./helpers');

// Initialize S3 client
const s3Client = new S3Client({});

/**
 * Generate a pre-signed URL for uploading to S3
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} fileName - Original file name
 * @param {string} activityId - Activity ID for permission check
 * @returns {Promise<Object>} - Object containing the signed URL and generated key
 * @throws {Error} - If user doesn't have permission or parameters are invalid
 */
async function getUploadUrl(dynamodb, tableName, userGroups, fileName, activityId) {
    if (!fileName) {
        throw new Error('File name is required for upload operations');
    }

    const bucketName = process.env.S3_BUCKET_NAME;
    if (!bucketName) {
        throw new Error('S3_BUCKET_NAME environment variable is not set');
    }

    // Check if user is an admin
    const isUserAdmin = isAdmin(userGroups);
    
    if (!isUserAdmin) {
        // If not admin, check if user has access to the activity
        if (!activityId) {
            throw new Error('Activity ID is required for permission check');
        }
        
        // Get the activity
        const activity = await getActivityById(dynamodb, tableName, activityId);
        if (!activity) {
            throw new Error('Activity not found');
        }
        
        // Get the project
        const project = await getProjectById(dynamodb, tableName, activity.projectObjectId);
        if (!project) {
            throw new Error('Project not found');
        }
        
        // Check if user is in one of the project's workerGroups
        const isWorker = hasRoles(userGroups, project.workerGroups);
        
        if (!isWorker) {
            throw new Error('Access denied: You do not have permission to access this resource');
        }
    }

    // Generate a unique key for the file
    const fileKey = `${randomUUID()}`;
    
    // Create the command for putting the object
    const command = new PutObjectCommand({ 
        Bucket: bucketName, 
        Key: fileKey 
    });

    try {
        // Generate the signed URL
        const signedUrl = await getSignedUrl(s3Client, command, { expiresIn: 3600 });
        return { url: signedUrl, key: fileKey };
    } catch (error) {
        console.error(`Error generating signed URL for upload:`, error);
        throw error;
    }
}

/**
 * Generate a pre-signed URL for deleting an S3 object
 * @param {Object} dynamodb - DynamoDB document client
 * @param {string} tableName - DynamoDB table name
 * @param {Array<string>} userGroups - User groups for authorization
 * @param {string} key - S3 object key
 * @param {string} activityId - Activity ID for permission check
 * @returns {Promise<Object>} - Object containing the signed URL and key
 * @throws {Error} - If user doesn't have permission or parameters are invalid
 */
async function getDeleteUrl(dynamodb, tableName, userGroups, key, activityId) {
    if (!key) {
        throw new Error('Key is required for delete operations');
    }

    const bucketName = process.env.S3_BUCKET_NAME;
    if (!bucketName) {
        throw new Error('S3_BUCKET_NAME environment variable is not set');
    }

    // Check if user is an admin
    const isUserAdmin = isAdmin(userGroups);
    
    if (!isUserAdmin) {
        // If not admin, check if user has access to the activity
        if (!activityId) {
            throw new Error('Activity ID is required for permission check');
        }
        
        // Get the activity
        const activity = await getActivityById(dynamodb, tableName, activityId);
        if (!activity) {
            throw new Error('Activity not found');
        }
        
        // Get the project
        const project = await getProjectById(dynamodb, tableName, activity.projectObjectId);
        if (!project) {
            throw new Error('Project not found');
        }
        
        // Check if user is in one of the project's workerGroups
        const isWorker = hasRoles(userGroups, project.workerGroups);
        
        if (!isWorker) {
            throw new Error('Access denied: You do not have permission to access this resource');
        }
    }

    // Create the command for deleting the object
    const command = new DeleteObjectCommand({ 
        Bucket: bucketName, 
        Key: key 
    });

    try {
        // Generate the signed URL
        const signedUrl = await getSignedUrl(s3Client, command, { expiresIn: 3600 });
        return { url: signedUrl, key };
    } catch (error) {
        console.error(`Error generating signed URL for delete:`, error);
        throw error;
    }
}

module.exports = {
    getUploadUrl,
    getDeleteUrl
};
