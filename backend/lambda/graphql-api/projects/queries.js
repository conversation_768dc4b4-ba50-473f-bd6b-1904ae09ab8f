const { GetCommand, QueryCommand } = require('@aws-sdk/lib-dynamodb');
const { ENTITY_TYPE, isAdminWorkerOperatorOrManager, formatKeys } = require('./helpers');

/**
 * Get a specific project by ID with permission check
 */
async function getProject(dynamodb, tableName, userGroups, id) {
  try {
    if (!id) throw new Error("Project ID is required");
    
    const params = {
      TableName: tableName,
      Key: formatKeys.project(id)
    };
    
    const { Item: project } = await dynamodb.send(new GetCommand(params));
    
    if (!project) return null;
    
    return isAdminWorkerOperatorOrManager.forProject(project, userGroups) ? project : null;
  } catch (error) {
    console.error(`Error fetching project ${id}:`, error);
    throw new Error(`Failed to retrieve project: ${error.message}`);
  }
}

/**
 * List projects with permission filtering
 */
async function listProjects(dynamodb, tableName, userGroups, limit = 100, nextToken = null) {
  try {
    const params = {
      TableName: tableName,
      IndexName: 'type-index',
      KeyConditionExpression: "#type = :projectType",
      ExpressionAttributeNames: {
        "#type": "type"
      },
      ExpressionAttributeValues: {
        ":projectType": ENTITY_TYPE.PROJECT
      },
      Limit: limit
    };
    
    // Add pagination token if provided
    if (nextToken) {
      params.ExclusiveStartKey = JSON.parse(Buffer.from(nextToken, 'base64').toString());
    }

    const data = await dynamodb.send(new QueryCommand(params));
    const projects = data.Items || [];
    
    // Filter projects based on permissions - only return projects where user is in the project group
    const filteredProjects = projects.filter(project => 
      isAdminWorkerOperatorOrManager.forProject(project, userGroups)
    );
      
    // Format the response with pagination
    const response = {
      items: filteredProjects,
      nextToken: null // Always include nextToken property
    };
    
    // Add the next token if there are more results
    if (data.LastEvaluatedKey) {
      response.nextToken = Buffer.from(
        JSON.stringify(data.LastEvaluatedKey)
      ).toString('base64');
    }
    
    return response;
  } catch (error) {
    console.error("Error listing projects:", error);
    throw new Error(`Failed to list projects: ${error.message}`);
  }
}

module.exports = {
  getProject,
  listProjects
};
