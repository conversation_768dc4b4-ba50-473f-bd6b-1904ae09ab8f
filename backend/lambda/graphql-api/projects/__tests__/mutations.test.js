const { addProject, updateProject, deleteProject } = require('../mutations');
const { getProject } = require('../queries');
const { PutCommand, UpdateCommand, DeleteCommand } = require('@aws-sdk/lib-dynamodb');
const { randomUUID } = require('crypto');

// Mock the queries module for the deleteProject tests
jest.mock('../queries', () => ({
  getProject: jest.fn()
}));

// Mock the UUID generation to have predictable IDs in tests
jest.mock('crypto', () => ({
  randomUUID: jest.fn(() => 'mock-uuid-123')
}));

// Mock the date to have predictable timestamps
const mockDate = new Date('2023-01-01T00:00:00.000Z');
global.Date = class extends Date {
  constructor() {
    return mockDate;
  }
  static now() {
    return mockDate.getTime();
  }
};
global.Date.toISOString = jest.fn(() => '2023-01-01T00:00:00.000Z');

describe('Project Mutation Functions', () => {
  // Common test variables
  const tableName = 'TestTable';
  const projectId = '123';
  const adminGroups = ['admin'];
  const regularGroups = ['users'];

  // Sample project data
  const testProject = {
    id: projectId,
    name: 'Test Project',
    type: 'PROJECT',
    PK: `PROJECT#${projectId}`,
    SK: `METADATA#${projectId}`,
    managerGroups: [{ id: 'managers', name: 'Managers' }],
    operatorGroups: [{ id: 'operators', name: 'Operators' }],
    workerGroups: [{ id: 'workers', name: 'Workers' }],
  };

  // Mock DynamoDB client
  const mockDynamodb = {
    send: jest.fn(),
  };

  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('addProject', () => {
    const validInput = {
      name: 'New Project',
      type: 'PROJECT',
      description: 'Project description'
    };

    it('should throw error when user is not admin', async () => {
      await expect(addProject(mockDynamodb, tableName, regularGroups, validInput))
        .rejects.toThrow('Insufficient permissions: Admin access required');
      
      expect(mockDynamodb.send).not.toHaveBeenCalled();
    });

    it('should throw error when required fields are missing', async () => {
      const incompleteInput = { description: 'Missing required fields' };
      
      await expect(addProject(mockDynamodb, tableName, adminGroups, incompleteInput))
        .rejects.toThrow('Missing required field: name');
    });

    it('should create project with generated ID when not provided', async () => {
      mockDynamodb.send.mockResolvedValueOnce({});
      
      const result = await addProject(mockDynamodb, tableName, adminGroups, validInput);
      
      expect(mockDynamodb.send).toHaveBeenCalledTimes(1);
      expect(mockDynamodb.send.mock.calls[0][0].input).toMatchObject({
        TableName: tableName,
        Item: {
          ...validInput,
          id: 'mock-uuid-123',
          PK: 'PROJECT#mock-uuid-123',
          SK: 'METADATA#mock-uuid-123',
          type: 'PROJECT',
          createdAt: '2023-01-01T00:00:00.000Z',
          updatedAt: '2023-01-01T00:00:00.000Z'
        },
        ConditionExpression: "attribute_not_exists(PK)"
      });
      expect(result).toMatchObject({
        ...validInput,
        id: 'mock-uuid-123',
        PK: 'PROJECT#mock-uuid-123',
        SK: 'METADATA#mock-uuid-123'
      });
    });

    it('should create project with provided ID', async () => {
      const inputWithId = { ...validInput, id: 'custom-id' };
      mockDynamodb.send.mockResolvedValueOnce({});
      
      const result = await addProject(mockDynamodb, tableName, adminGroups, inputWithId);
      
      expect(mockDynamodb.send).toHaveBeenCalledTimes(1);
      expect(mockDynamodb.send.mock.calls[0][0].input.Item.id).toBe('custom-id');
      expect(mockDynamodb.send.mock.calls[0][0].input.Item.PK).toBe('PROJECT#custom-id');
    });

    it('should handle case when project with ID already exists', async () => {
      const conditionalError = new Error('Conditional check failed');
      conditionalError.name = 'ConditionalCheckFailedException';
      mockDynamodb.send.mockRejectedValueOnce(conditionalError);
      
      await expect(addProject(mockDynamodb, tableName, adminGroups, validInput))
        .rejects.toThrow('A project with this ID already exists');
    });
  });

  describe('updateProject', () => {
    const updateInput = {
      name: 'Updated Project',
      description: 'Updated description'
    };

    it('should throw error when user is not admin', async () => {
      await expect(updateProject(mockDynamodb, tableName, regularGroups, projectId, updateInput))
        .rejects.toThrow('Insufficient permissions: Admin access required');
      
      expect(mockDynamodb.send).not.toHaveBeenCalled();
    });

    it('should throw error when id is not provided', async () => {
      await expect(updateProject(mockDynamodb, tableName, adminGroups, null, updateInput))
        .rejects.toThrow('Project ID is required');
    });

    // it('should throw error when there are no valid fields to update', async () => {
    //   await expect(updateProject(mockDynamodb, tableName, adminGroups, projectId, { id: projectId, PK: 'test', SK: 'test' }))
    //     .rejects.toThrow('No valid fields to update');
    // });

    it('should update project fields correctly', async () => {
      const updatedProject = { ...testProject, ...updateInput, updatedAt: '2023-01-01T00:00:00.000Z' };
      mockDynamodb.send.mockResolvedValueOnce({ Attributes: updatedProject });
      
      const result = await updateProject(mockDynamodb, tableName, adminGroups, projectId, updateInput);
      
      expect(mockDynamodb.send).toHaveBeenCalledTimes(1);
      expect(mockDynamodb.send.mock.calls[0][0].input).toMatchObject({
        TableName: tableName,
        Key: {
          PK: `PROJECT#${projectId}`,
          SK: `METADATA#${projectId}`,
        },
        UpdateExpression: expect.stringContaining('#name = :name'),
        ExpressionAttributeNames: {
          '#name': 'name',
          '#description': 'description',
          '#updatedAt': 'updatedAt',
        },
        ExpressionAttributeValues: {
          ':name': 'Updated Project',
          ':description': 'Updated description',
          ':updatedAt': '2023-01-01T00:00:00.000Z',
        },
        ReturnValues: 'ALL_NEW',
      });
      expect(result).toEqual(updatedProject);
    });

    it('should handle case when project does not exist', async () => {
      const conditionalError = new Error('Conditional check failed');
      conditionalError.name = 'ConditionalCheckFailedException';
      mockDynamodb.send.mockRejectedValueOnce(conditionalError);
      
      await expect(updateProject(mockDynamodb, tableName, adminGroups, projectId, updateInput))
        .rejects.toThrow(`Project not found: ${projectId}`);
    });

    it('should handle DynamoDB errors', async () => {
      const error = new Error('DynamoDB error');
      mockDynamodb.send.mockRejectedValueOnce(error);
      
      await expect(updateProject(mockDynamodb, tableName, adminGroups, projectId, updateInput))
        .rejects.toThrow(`Failed to update project: DynamoDB error`);
    });
  });

  describe('deleteProject', () => {
    beforeEach(() => {
      getProject.mockReset();
    });

    it('should throw error when user is not admin', async () => {
      await expect(deleteProject(mockDynamodb, tableName, regularGroups, projectId))
        .rejects.toThrow('Insufficient permissions: Admin access required');
    });

    it('should throw error when id is not provided', async () => {
      await expect(deleteProject(mockDynamodb, tableName, adminGroups, null))
        .rejects.toThrow('Project ID is required');
    });

    it('should throw error when project does not exist', async () => {
      getProject.mockResolvedValueOnce(null);
      
      await expect(deleteProject(mockDynamodb, tableName, adminGroups, projectId))
        .rejects.toThrow(`Project not found: ${projectId}`);
    });

    it('should delete project successfully', async () => {
      getProject.mockResolvedValueOnce(testProject);
      mockDynamodb.send.mockResolvedValueOnce({});
      
      const result = await deleteProject(mockDynamodb, tableName, adminGroups, projectId);
      
      expect(mockDynamodb.send).toHaveBeenCalledTimes(1);
      expect(mockDynamodb.send.mock.calls[0][0].input).toMatchObject({
        TableName: tableName,
        Key: {
          PK: `PROJECT#${projectId}`,
          SK: `METADATA#${projectId}`,
        },
      });
      expect(result).toBe(projectId);
    });

    it('should handle DynamoDB errors during delete', async () => {
      getProject.mockResolvedValueOnce(testProject);
      
      const error = new Error('DynamoDB delete error');
      mockDynamodb.send.mockRejectedValueOnce(error);
      
      await expect(deleteProject(mockDynamodb, tableName, adminGroups, projectId))
        .rejects.toThrow(`Failed to delete project: DynamoDB delete error`);
    });
  });
});
