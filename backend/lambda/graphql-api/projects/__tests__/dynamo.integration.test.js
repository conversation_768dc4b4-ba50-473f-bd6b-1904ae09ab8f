// dynamo.integration.test.js - Integration tests for project operations

const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient } = require('@aws-sdk/lib-dynamodb');
const { listProjects, getProject } = require('../queries');
const { addProject, updateProject, deleteProject } = require('../mutations');

/**
 * This integration test uses local AWS SSO credentials to test project operations.
 * 
 * Prerequisites:
 * 1. Valid AWS SSO credentials configured in your environment
 * 2. Access to the DynamoDB table used by the application
 * 
 * To run this test:
 * 1. Make sure you're logged in with AWS SSO
 * 2. Run: jest projects/__tests__/dynamo.integration.test.js
 */

// Set a longer timeout for integration tests
jest.setTimeout(30000);

describe('Project List Query - Integration Test', () => {
  // Initialize DynamoDB client with local AWS SSO credentials
  // This will use the credentials from the AWS SSO login
  const client = new DynamoDBClient({});
  const dynamodb = DynamoDBDocumentClient.from(client);
  
  // Get table name from environment or use a test table
  // In a real scenario, you might want to use a test-specific table
  const tableName = process.env.TABLE_NAME || 'TAExGraphQlApiStack-TAExTable948F8D9B-PNHWWR1O612L';
  
  // Admin user groups for testing
  const adminGroups = ['admin'];
  
  test('should list projects when authenticated as admin', async () => {
    try {
      // Call the listProjects function with actual DynamoDB client
      const result = await listProjects(
        dynamodb,
        tableName,
        adminGroups
      );
      
      // Verify the structure of the response
      expect(result).toHaveProperty('items');
      expect(result).toHaveProperty('nextToken');
      
      // Verify that items is an array
      expect(Array.isArray(result.items)).toBe(true);
      
      // If there are items, verify their structure
      if (result.items.length > 0) {
        const firstProject = result.items[0];
        expect(firstProject).toHaveProperty('id');
        expect(firstProject).toHaveProperty('name');
        expect(firstProject).toHaveProperty('type');
      }
      
      console.log(`Found ${result.items.length} projects in the database`);
    } catch (error) {
      console.log(error);
      // If the error is about permissions, it might be that the SSO credentials
      // don't have admin access, which is expected in some environments
      if (error.message.includes('Access denied') || error.message.includes('permission')) {
        console.warn('Test skipped due to permission issues. Make sure your SSO credentials have admin access.');
        return;
      }
      
      // For other errors, fail the test
      throw error;
    }
  });
  
  test('should handle pagination correctly', async () => {
    try {
      // First request with small limit
      const firstPage = await listProjects(
        dynamodb,
        tableName,
        adminGroups,
        2 // Small limit to force pagination
      );
      
      // If there are more than 2 projects, we should have a nextToken
      if (firstPage.items.length > 2) {
        expect(firstPage.nextToken).toBeTruthy();
        
        // Get the second page using the nextToken
        const secondPage = await listProjects(
          dynamodb,
          tableName,
          adminGroups,
          2, // Same limit
          firstPage.nextToken
        );
        
        // Verify we got different projects
        expect(secondPage.items).not.toEqual(firstPage.items);
      } else {
        console.log('Not enough projects to test pagination');
      }
    } catch (error) {
      // Handle permission errors as in the previous test
      if (error.message.includes('Access denied') || error.message.includes('permission')) {
        console.warn('Test skipped due to permission issues. Make sure your SSO credentials have admin access.');
        return;
      }
      
      throw error;
    }
  });
  
  test('should filter projects based on permissions', async () => {
    try {
      // Test with non-admin user groups
      const nonAdminGroups = ['managers'];
      
      // Get projects with admin permissions
      const adminProjects = await listProjects(
        dynamodb,
        tableName,
        adminGroups
      );
      
      // Get projects with non-admin permissions
      const managerProjects = await listProjects(
        dynamodb,
        tableName,
        nonAdminGroups
      );
      
      // Admin should see all projects, managers should see only projects they have access to
      expect(managerProjects.items.length).toBeLessThanOrEqual(adminProjects.items.length);
      
      console.log(`Admin sees ${adminProjects.items.length} projects, manager sees ${managerProjects.items.length} projects`);
    } catch (error) {
      // Handle permission errors as in the previous tests
      if (error.message.includes('Access denied') || error.message.includes('permission')) {
        console.warn('Test skipped due to permission issues. Make sure your SSO credentials have admin access.');
        return;
      }
      
      throw error;
    }
  });
});

describe('Project Creation - Integration Test', () => {
  // Initialize DynamoDB client with local AWS SSO credentials
  const client = new DynamoDBClient({});
  const dynamodb = DynamoDBDocumentClient.from(client);
  
  // Get table name from environment or use a test table
  const tableName = process.env.TABLE_NAME || 'TAExGraphQlApiStack-TAExTable948F8D9B-PNHWWR1O612L';
  
  // Admin user groups for testing
  const adminGroups = ['admin'];
  const nonAdminGroups = ['managers'];
  
  // Generate a unique project name for testing
  const testProjectName = `Test Project ${Date.now()}`;
  let createdProjectId = null;
  
  // Clean up after tests
  afterAll(async () => {
    try {
      if (createdProjectId) {
        // Delete the test project
        await deleteProject(
          dynamodb,
          tableName,
          adminGroups,
          createdProjectId
        );
        
        console.log(`Deleted test project: ${createdProjectId}`);
      }
    } catch (error) {
      console.warn(`Failed to delete test project: ${error.message}`);
    }
  });
  
  test('should create a new project when authenticated as admin', async () => {
    try {
      // Create project input
      const projectInput = {
        name: testProjectName,
        status: 'ACTIVE'
      };
      
      // Call the addProject function with actual DynamoDB client
      const result = await addProject(
        dynamodb,
        tableName,
        adminGroups,
        projectInput
      );
      
      // Save the ID for cleanup
      createdProjectId = result.id;
      
      // Verify the structure of the response
      expect(result).toHaveProperty('id');
      expect(result).toHaveProperty('name', testProjectName);
      expect(result).toHaveProperty('status', 'ACTIVE');
      expect(result).toHaveProperty('PK', `PROJECT#${result.id}`);
      expect(result).toHaveProperty('SK', `METADATA#${result.id}`);
      expect(result).toHaveProperty('type');
      expect(result).toHaveProperty('createdAt');
      expect(result).toHaveProperty('updatedAt');
      
      console.log(`Successfully created project: ${result.id}`);
    } catch (error) {
      // If the error is about permissions, it might be that the SSO credentials
      // don't have admin access, which is expected in some environments
      if (error.message.includes('Access denied') || error.message.includes('permission')) {
        console.warn('Test skipped due to permission issues. Make sure your SSO credentials have admin access.');
        return;
      }
      
      // For other errors, fail the test
      throw error;
    }
  });
  
  test('should reject project creation for non-admin users', async () => {
    // Create project input with a different name
    const projectInput = {
      name: `${testProjectName}-2`,
      type: 'Construction',
      description: 'This should fail'
    };
    
    // Expect the function to throw an error about permissions
    await expect(addProject(
      dynamodb,
      tableName,
      nonAdminGroups,
      projectInput
    )).rejects.toThrow('Insufficient permissions: Admin access required');
  });
  
  test('should reject creation with missing required fields', async () => {
    // Try to create a project without required fields
    const incompleteInput = {
      description: 'Missing required fields'
    };
    
    // Expect the function to throw an error about missing fields
    await expect(addProject(
      dynamodb,
      tableName,
      adminGroups,
      incompleteInput
    )).rejects.toThrow('Missing required field');
  });
});

describe('Project Update - Integration Test', () => {
  // Initialize DynamoDB client with local AWS SSO credentials
  const client = new DynamoDBClient({});
  const dynamodb = DynamoDBDocumentClient.from(client);
  
  // Get table name from environment or use a test table
  const tableName = process.env.TABLE_NAME || 'TAExGraphQlApiStack-TAExTable948F8D9B-PNHWWR1O612L';
  
  // Admin user groups for testing
  const adminGroups = ['admin'];
  const nonAdminGroups = ['managers'];
  
  // Generate a unique project name for testing
  const testProjectName = `Test Update Project ${Date.now()}`;
  let testProjectId = null;
  
  // Create a test project before running the update tests
  beforeAll(async () => {
    try {
      // Create project input
      const projectInput = {
        name: testProjectName,
        type: 'Construction',
        description: 'Project for update tests',
        status: 'Planning'
      };
      
      // Create a test project to update
      const project = await addProject(
        dynamodb,
        tableName,
        adminGroups,
        projectInput
      );
      
      testProjectId = project.id;
      console.log(`Created test project for update: ${testProjectId}`);
    } catch (error) {
      console.warn(`Failed to create test project: ${error.message}`);
      // If we can't create the test project, the tests might fail
    }
  });
  
  // Clean up after tests
  afterAll(async () => {
    try {
      if (testProjectId) {
        // Delete the test project
        await deleteProject(
          dynamodb,
          tableName,
          adminGroups,
          testProjectId
        );
        
        console.log(`Deleted test project: ${testProjectId}`);
      }
    } catch (error) {
      console.warn(`Failed to delete test project: ${error.message}`);
    }
  });
  
  test('should update a project when authenticated as admin', async () => {
    try {
      // Skip if test project wasn't created
      if (!testProjectId) {
        console.warn('Test skipped because test project was not created');
        return;
      }
      
      // Update project input
      const updateInput = {
        name: `${testProjectName} (Updated)`,
        description: 'Updated description',
        status: 'In Progress'
      };
      
      // Call the updateProject function with actual DynamoDB client
      const result = await updateProject(
        dynamodb,
        tableName,
        adminGroups,
        testProjectId,
        updateInput
      );
      
      // Verify the structure of the response
      expect(result).toHaveProperty('id', testProjectId);
      expect(result).toHaveProperty('name', `${testProjectName} (Updated)`);
      expect(result).toHaveProperty('description', 'Updated description');
      expect(result).toHaveProperty('status', 'In Progress');
      expect(result).toHaveProperty('updatedAt');
      
      console.log(`Successfully updated project: ${testProjectId}`);
    } catch (error) {
      // If the error is about permissions, it might be that the SSO credentials
      // don't have admin access, which is expected in some environments
      if (error.message.includes('Access denied') || error.message.includes('permission')) {
        console.warn('Test skipped due to permission issues. Make sure your SSO credentials have admin access.');
        return;
      }
      
      // For other errors, fail the test
      throw error;
    }
  });
  
  test('should reject project update for non-admin users', async () => {
    // Skip if test project wasn't created
    if (!testProjectId) {
      console.warn('Test skipped because test project was not created');
      return;
    }
    
    // Update project input
    const updateInput = {
      name: `${testProjectName} (Should Fail)`,
    };
    
    // Expect the function to throw an error about permissions
    await expect(updateProject(
      dynamodb,
      tableName,
      nonAdminGroups,
      testProjectId,
      updateInput
    )).rejects.toThrow('Insufficient permissions: Admin access required');
  });
  
  test('should reject update of non-existent project', async () => {
    // Try to update a project that doesn't exist
    const nonExistentId = `non-existent-project-${Date.now()}`;
    
    const updateInput = {
      name: 'Non-existent Project',
    };
    
    // Expect the function to throw an error about the project not existing
    await expect(updateProject(
      dynamodb,
      tableName,
      adminGroups,
      nonExistentId,
      updateInput
    )).rejects.toThrow(`Project not found: ${nonExistentId}`);
  });
});

describe('Project Deletion - Integration Test', () => {
  // Initialize DynamoDB client with local AWS SSO credentials
  const client = new DynamoDBClient({});
  const dynamodb = DynamoDBDocumentClient.from(client);
  
  // Get table name from environment or use a test table
  const tableName = process.env.TABLE_NAME || 'TAExGraphQlApiStack-TAExTable948F8D9B-PNHWWR1O612L';
  
  // Admin user groups for testing
  const adminGroups = ['admin'];
  const nonAdminGroups = ['managers'];
  
  // Generate a unique project name for testing
  const testProjectName = `Test Delete Project ${Date.now()}`;
  let testProjectId = null;
  
  // Create a test project before running the deletion tests
  beforeAll(async () => {
    try {
      // Create project input
      const projectInput = {
        name: testProjectName,
        type: 'Construction',
        description: 'Project for deletion tests'
      };
      
      // Create a test project to delete
      const project = await addProject(
        dynamodb,
        tableName,
        adminGroups,
        projectInput
      );
      
      testProjectId = project.id;
      console.log(`Created test project for deletion: ${testProjectId}`);
    } catch (error) {
      console.warn(`Failed to create test project: ${error.message}`);
      // If we can't create the test project, the tests might fail
    }
  });
  
  test('should delete a project when authenticated as admin', async () => {
    try {
      // Skip if test project wasn't created
      if (!testProjectId) {
        console.warn('Test skipped because test project was not created');
        return;
      }
      
      // Call the deleteProject function with actual DynamoDB client
      const result = await deleteProject(
        dynamodb,
        tableName,
        adminGroups,
        testProjectId
      );
      
      // Verify the result is the id of the deleted project
      expect(result).toBe(testProjectId);
      
      console.log(`Successfully deleted project: ${testProjectId}`);
      
      // Verify the project no longer exists by trying to get it
      const deletedProject = await getProject(
        dynamodb,
        tableName,
        adminGroups,
        testProjectId
      );
      expect(deletedProject).toBeNull();
      
      // Clear the ID since we've deleted it
      testProjectId = null;
    } catch (error) {
      // If the error is about permissions, it might be that the SSO credentials
      // don't have admin access, which is expected in some environments
      if (error.message.includes('Access denied') || error.message.includes('permission')) {
        console.warn('Test skipped due to permission issues. Make sure your SSO credentials have admin access.');
        return;
      }
      
      // For other errors, fail the test
      throw error;
    }
  });
  
  test('should reject project deletion for non-admin users', async () => {
    // Generate another test project
    let anotherProjectId = null;
    
    try {
      // Create another test project
      const project = await addProject(
        dynamodb,
        tableName,
        adminGroups,
        {
          name: `${testProjectName}-2`,
          type: 'Construction',
          description: 'Another project for deletion tests'
        }
      );
      
      anotherProjectId = project.id;
      
      // Expect the function to throw an error about permissions when non-admin tries to delete
      await expect(deleteProject(
        dynamodb,
        tableName,
        nonAdminGroups,
        anotherProjectId
      )).rejects.toThrow('Insufficient permissions: Admin access required');
      
      // Clean up - delete the project with admin permissions
      await deleteProject(
        dynamodb,
        tableName,
        adminGroups,
        anotherProjectId
      );
    } catch (error) {
      // If we can't create the test project due to permissions, skip the test
      if (error.message.includes('Access denied') || error.message.includes('permission')) {
        console.warn('Test skipped due to permission issues. Make sure your SSO credentials have admin access.');
        return;
      }
      
      throw error;
    }
  });
  
  test('should reject deletion of non-existent project', async () => {
    // Try to delete a project that doesn't exist
    const nonExistentId = `non-existent-project-${Date.now()}`;
    
    // Expect the function to throw an error about the project not existing
    await expect(deleteProject(
      dynamodb,
      tableName,
      adminGroups,
      nonExistentId
    )).rejects.toThrow(`Project not found: ${nonExistentId}`);
  });
});

describe('Project Get - Integration Test', () => {
  // Initialize DynamoDB client with local AWS SSO credentials
  const client = new DynamoDBClient({});
  const dynamodb = DynamoDBDocumentClient.from(client);
  
  // Get table name from environment or use a test table
  const tableName = process.env.TABLE_NAME || 'TAExGraphQlApiStack-TAExTable948F8D9B-PNHWWR1O612L';
  
  // User groups for testing
  const adminGroups = ['admin'];
  const managerGroups = ['managers'];
  const regularGroups = ['users'];
  
  // Generate a unique project name for testing
  const testProjectName = `Test Get Project ${Date.now()}`;
  let testProjectId = null;
  
  // Create a test project before running the get tests
  beforeAll(async () => {
    try {
      // Create project input with specific manager groups
      const projectInput = {
        name: testProjectName,
        type: 'Construction',
        description: 'Project for get tests',
        managerGroups: [{ id: 'managers', name: 'Managers' }]
      };
      
      // Create a test project to get
      const project = await addProject(
        dynamodb,
        tableName,
        adminGroups,
        projectInput
      );
      
      testProjectId = project.id;
      console.log(`Created test project for get tests: ${testProjectId}`);
    } catch (error) {
      console.warn(`Failed to create test project: ${error.message}`);
      // If we can't create the test project, the tests might fail
    }
  });
  
  // Clean up after tests
  afterAll(async () => {
    try {
      if (testProjectId) {
        // Delete the test project
        await deleteProject(
          dynamodb,
          tableName,
          adminGroups,
          testProjectId
        );
        
        console.log(`Deleted test project: ${testProjectId}`);
      }
    } catch (error) {
      console.warn(`Failed to delete test project: ${error.message}`);
    }
  });
  
  test('should get a project when authenticated as admin', async () => {
    try {
      // Skip if test project wasn't created
      if (!testProjectId) {
        console.warn('Test skipped because test project was not created');
        return;
      }
      
      // Call the getProject function with actual DynamoDB client
      const result = await getProject(
        dynamodb,
        tableName,
        adminGroups,
        testProjectId
      );
      
      // Verify the structure of the response
      expect(result).toHaveProperty('id', testProjectId);
      expect(result).toHaveProperty('name', testProjectName);
      expect(result).toHaveProperty('type', 'PROJECT');
      
      console.log(`Successfully retrieved project as admin: ${testProjectId}`);
    } catch (error) {
      // If the error is about permissions, it might be that the SSO credentials
      // don't have admin access, which is expected in some environments
      if (error.message.includes('Access denied') || error.message.includes('permission')) {
        console.warn('Test skipped due to permission issues. Make sure your SSO credentials have admin access.');
        return;
      }
      
      // For other errors, fail the test
      throw error;
    }
  });
  
  test('should get a project when user is in manager groups', async () => {
    try {
      // Skip if test project wasn't created
      if (!testProjectId) {
        console.warn('Test skipped because test project was not created');
        return;
      }
      
      // Call the getProject function with manager groups
      const result = await getProject(
        dynamodb,
        tableName,
        managerGroups,
        testProjectId
      );
      
      // Verify we got the project
      expect(result).not.toBeNull();
      expect(result).toHaveProperty('id', testProjectId);
      
      console.log(`Successfully retrieved project as manager: ${testProjectId}`);
    } catch (error) {
      // For errors, fail the test
      throw error;
    }
  });
  
  test('should return null when user does not have permission', async () => {
    // Skip if test project wasn't created
    if (!testProjectId) {
      console.warn('Test skipped because test project was not created');
      return;
    }
    
    // Call the getProject function with regular user groups
    const result = await getProject(
      dynamodb,
      tableName,
      regularGroups,
      testProjectId
    );
    
    // Verify we didn't get the project
    expect(result).toBeNull();
  });
  
  test('should return null for non-existent project', async () => {
    // Try to get a project that doesn't exist
    const nonExistentId = `non-existent-project-${Date.now()}`;
    
    // Call the getProject function
    const result = await getProject(
      dynamodb,
      tableName,
      adminGroups,
      nonExistentId
    );
    
    // Verify we didn't get a project
    expect(result).toBeNull();
  });
});
