const { getProject, listProjects } = require('../queries');
const { GetCommand, QueryCommand } = require('@aws-sdk/lib-dynamodb');

// Mock the UUID generation to have predictable IDs in tests
jest.mock('crypto', () => ({
  randomUUID: jest.fn(() => 'mock-uuid-123')
}));

// Mock the date to have predictable timestamps
const mockDate = new Date('2023-01-01T00:00:00.000Z');
global.Date = class extends Date {
  constructor() {
    return mockDate;
  }
  static now() {
    return mockDate.getTime();
  }
};
global.Date.toISOString = jest.fn(() => '2023-01-01T00:00:00.000Z');

describe('Project Query Functions', () => {
  // Common test variables
  const tableName = 'TestTable';
  const projectId = '123';
  const adminGroups = ['admin'];
  const managerGroups = ['managers'];
  const operatorGroups = ['operators'];
  const workerGroups = ['workers'];
  const regularGroups = ['users'];

  // Sample project data
  const testProject = {
    id: projectId,
    name: 'Test Project',
    type: 'PROJECT',
    PK: `PROJECT#${projectId}`,
    SK: `METADATA#${projectId}`,
    managerGroups: [{ id: 'managers', name: 'Managers' }],
    operatorGroups: [{ id: 'operators', name: 'Operators' }],
    workerGroups: [{ id: 'workers', name: 'Workers' }],
  };

  // Mock DynamoDB client
  const mockDynamodb = {
    send: jest.fn(),
  };

  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getProject', () => {
    it('should return null when id is not provided', async () => {
      await expect(getProject(mockDynamodb, tableName, adminGroups, null))
        .rejects.toThrow('Project ID is required');
    });

    it('should return null when project does not exist', async () => {
      mockDynamodb.send.mockResolvedValueOnce({ Item: null });
      
      const result = await getProject(mockDynamodb, tableName, adminGroups, projectId);
      
      expect(mockDynamodb.send).toHaveBeenCalledTimes(1);
      expect(mockDynamodb.send.mock.calls[0][0].input).toEqual({
        TableName: tableName,
        Key: {
          PK: `PROJECT#${projectId}`,
          SK: `METADATA#${projectId}`,
        }
      });
      expect(result).toBeNull();
    });

    it('should return project when user is admin', async () => {
      mockDynamodb.send.mockResolvedValueOnce({ Item: testProject });
      
      const result = await getProject(mockDynamodb, tableName, adminGroups, projectId);
      
      expect(result).toEqual(testProject);
    });

    it('should return project when user is in manager groups', async () => {
      mockDynamodb.send.mockResolvedValueOnce({ Item: testProject });
      
      const result = await getProject(mockDynamodb, tableName, managerGroups, projectId);
      
      expect(result).toEqual(testProject);
    });

    it('should return project when user is in operator groups', async () => {
      mockDynamodb.send.mockResolvedValueOnce({ Item: testProject });
      
      const result = await getProject(mockDynamodb, tableName, operatorGroups, projectId);
      
      expect(result).toEqual(testProject);
    });

    it('should return project when user is in worker groups', async () => {
      mockDynamodb.send.mockResolvedValueOnce({ Item: testProject });
      
      const result = await getProject(mockDynamodb, tableName, workerGroups, projectId);
      
      expect(result).toEqual(testProject);
    });

    it('should return null when user does not have permission', async () => {
      mockDynamodb.send.mockResolvedValueOnce({ Item: testProject });
      
      const result = await getProject(mockDynamodb, tableName, regularGroups, projectId);
      
      expect(result).toBeNull();
    });

    it('should handle DynamoDB errors', async () => {
      const error = new Error('DynamoDB error');
      mockDynamodb.send.mockRejectedValueOnce(error);
      
      await expect(getProject(mockDynamodb, tableName, adminGroups, projectId))
        .rejects.toThrow('Failed to retrieve project: DynamoDB error');
    });
  });

  describe('listProjects', () => {
    const mockProjects = [
      {
        ...testProject,
        id: '1',
        PK: 'PROJECT#1',
        SK: 'METADATA#1',
      },
      {
        ...testProject,
        id: '2',
        PK: 'PROJECT#2',
        SK: 'METADATA#2',
        managerGroups: [{ id: 'other-managers', name: 'Other Managers' }],
      },
    ];

    it('should return all projects for admin users', async () => {
      mockDynamodb.send.mockResolvedValueOnce({ 
        Items: mockProjects,
        Count: 2,
      });
      
      const result = await listProjects(mockDynamodb, tableName, adminGroups);
      
      expect(mockDynamodb.send).toHaveBeenCalledTimes(1);
      expect(mockDynamodb.send.mock.calls[0][0].input).toMatchObject({
        TableName: tableName,
        IndexName: 'type-index',
        KeyConditionExpression: "#type = :projectType",
        Limit: 100,
      });
      expect(result.items).toEqual(mockProjects);
      expect(result.nextToken).toBeUndefined();
    });

    it('should filter projects based on user permissions', async () => {
      mockDynamodb.send.mockResolvedValueOnce({ 
        Items: mockProjects,
        Count: 2,
      });
      
      const result = await listProjects(mockDynamodb, tableName, managerGroups);
      
      expect(result.items).toHaveLength(1);
      expect(result.items[0].id).toBe('1');
    });

    it('should handle pagination', async () => {
      const lastEvaluatedKey = { PK: 'PROJECT#2', SK: 'METADATA#2' };
      mockDynamodb.send.mockResolvedValueOnce({ 
        Items: mockProjects,
        LastEvaluatedKey: lastEvaluatedKey,
      });
      
      const result = await listProjects(mockDynamodb, tableName, adminGroups, 10);
      
      expect(result.items).toEqual(mockProjects);
      expect(result.nextToken).toBe(
        Buffer.from(JSON.stringify(lastEvaluatedKey)).toString('base64')
      );
    });

    it('should use provided nextToken for pagination', async () => {
      const mockToken = Buffer.from(JSON.stringify({ PK: 'PROJECT#5' })).toString('base64');
      mockDynamodb.send.mockResolvedValueOnce({ Items: [mockProjects[0]] });
      
      await listProjects(mockDynamodb, tableName, adminGroups, 10, mockToken);
      
      expect(mockDynamodb.send).toHaveBeenCalledTimes(1);
      expect(mockDynamodb.send.mock.calls[0][0].input.ExclusiveStartKey).toEqual({ PK: 'PROJECT#5' });
    });

    it('should handle empty results', async () => {
      mockDynamodb.send.mockResolvedValueOnce({ Items: [] });
      
      const result = await listProjects(mockDynamodb, tableName, adminGroups);
      
      expect(result.items).toEqual([]);
    });

    it('should handle DynamoDB errors', async () => {
      const error = new Error('DynamoDB error');
      mockDynamodb.send.mockRejectedValueOnce(error);
      
      await expect(listProjects(mockDynamodb, tableName, adminGroups))
        .rejects.toThrow('Failed to list projects: DynamoDB error');
    });
  });
});
