// Constants to avoid hardcoding throughout the code
const ENTITY_TYPE = {
  PROJECT: 'PROJECT'
};

// Permission helpers with the new name
const isAdminWorkerOperatorOrManager = {
  admin: (userGroups) => userGroups.includes("admin"),
  
  forProject: (project, userGroups) => {
    if (!project) return false;
    
    // <PERSON><PERSON> can see all projects regardless of isVisible flag
    if (isAdminWorkerOperatorOrManager.admin(userGroups)) {
      return true;
    }
    
    // Non-admins can only see projects where:
    // 1. They belong to the project's groups, AND
    // 2. The project has isVisible set to true
    const userBelongsToProjectGroups = (
      project.managerGroups?.some(group => userGroups.includes(group)) ||
      project.operatorGroups?.some(group => userGroups.includes(group)) ||
      project.workerGroups?.some(group => userGroups.includes(group))
    );
    
    return userBelongsToProjectGroups && project.isVisible === true;
  }
};

// Key formatting helper
const formatKeys = {
  project: (id) => ({
    PK: `PROJECT#${id}`,
    SK: `METADATA#${id}`
  })
};

module.exports = {
  ENTITY_TYPE,
  isAdminWorkerOperatorOrManager,
  formatKeys
};
