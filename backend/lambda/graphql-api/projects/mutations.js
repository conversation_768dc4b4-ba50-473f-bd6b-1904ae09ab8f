const { PutCommand, UpdateCommand, DeleteCommand } = require('@aws-sdk/lib-dynamodb');
const { randomUUID } = require('crypto');
const { ENTITY_TYPE, isAdminWorkerOperatorOrManager, formatKeys } = require('./helpers');
const { getProject } = require('./queries');

/**
 * Add a new project (admin only)
 */
async function addProject(dynamodb, tableName, userGroups, input) {
  try {
    if (!isAdminWorkerOperatorOrManager.admin(userGroups)) {
      throw new Error("Insufficient permissions: Admin access required");
    }
    
    // Validate required fields
    const requiredFields = ['name'];
    for (const field of requiredFields) {
      if (!input[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
    
    const id = input.id || randomUUID();
    const timestamp = new Date().toISOString();
    
    const project = {
      ...input,
      id,
      ...formatKeys.project(id),
      type: 'PROJECT',
      isVisible: input.isVisible !== undefined ? input.isVisible : false, // Default to false
      createdAt: timestamp,
      updatedAt: timestamp
    };

    await dynamodb.send(new PutCommand({
      TableName: tableName,
      Item: project,
      // Ensure we don't overwrite an existing project with the same ID
      ConditionExpression: "attribute_not_exists(PK)"
    }));
    
    return project;
  } catch (error) {
    if (error.name === 'ConditionalCheckFailedException') {
      throw new Error(`A project with this ID already exists`);
    }
    console.error("Error adding project:", error);
    throw new Error(`Failed to add project: ${error.message}`);
  }
}

/**
 * Update an existing project (admin only)
 */
async function updateProject(dynamodb, tableName, userGroups, id, input) {
  try {
    if (!isAdminWorkerOperatorOrManager.admin(userGroups)) {
      throw new Error("Insufficient permissions: Admin access required");
    }
    
    if (!id) throw new Error("Project ID is required");
    
    // Prevent updating key fields
    const protectedFields = ['id', 'PK', 'SK', 'type', 'createdAt'];
    const updateInput = { ...input, updatedAt: new Date().toISOString() };
    
    protectedFields.forEach(field => {
      delete updateInput[field];
    });
    
    if (Object.keys(updateInput).length === 0) {
      throw new Error("No valid fields to update");
    }

    // Build update expression
    let updateExpression = "SET ";
    const expressionAttributeNames = {};
    const expressionAttributeValues = {};
    
    Object.keys(updateInput).forEach((key) => {
      expressionAttributeNames[`#${key}`] = key;
      expressionAttributeValues[`:${key}`] = updateInput[key];
      updateExpression += `#${key} = :${key}, `;
    });

    updateExpression = updateExpression.slice(0, -2); // Remove trailing comma and space

    const params = {
      TableName: tableName,
      Key: formatKeys.project(id),
      UpdateExpression: updateExpression,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
      ReturnValues: "ALL_NEW",
      ConditionExpression: "attribute_exists(PK)" // Ensure the project exists
    };

    const { Attributes: updatedProject } = await dynamodb.send(new UpdateCommand(params));
    return updatedProject;
  } catch (error) {
    if (error.name === 'ConditionalCheckFailedException') {
      throw new Error(`Project not found: ${id}`);
    }
    console.error(`Error updating project ${id}:`, error);
    throw new Error(`Failed to update project: ${error.message}`);
  }
}

/**
 * Delete a project (admin only)
 */
async function deleteProject(dynamodb, tableName, userGroups, id) {
  try {
    if (!isAdminWorkerOperatorOrManager.admin(userGroups)) {
      throw new Error("Insufficient permissions: Admin access required");
    }
    
    if (!id) throw new Error("Project ID is required");
    
    // First check if the project exists
    const projectExists = await getProject(dynamodb, tableName, userGroups, id);
    
    if (!projectExists) {
      throw new Error(`Project not found: ${id}`);
    }
    
    await dynamodb.send(new DeleteCommand({
      TableName: tableName,
      Key: formatKeys.project(id)
    }));
    
    return id;
  } catch (error) {
    console.error(`Error deleting project ${id}:`, error);
    throw new Error(`Failed to delete project: ${error.message}`);
  }
}

module.exports = {
  addProject,
  updateProject,
  deleteProject
};
