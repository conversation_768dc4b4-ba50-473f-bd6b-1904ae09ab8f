import axios from 'axios';

export class GraphQLClient {
  private endpoint: string;
  private apiKey: string;

  constructor(endpoint: string, apiKey: string) {
    this.endpoint = endpoint;
    this.apiKey = apiKey;
  }

  async query(query: string, variables: any = {}) {
    const response = await axios({
      url: this.endpoint,
      method: 'post',
      headers: {
        'Authorization': this.apiKey,
        'Content-Type': 'application/json',
      },
      data: JSON.stringify({
        query,
        variables,
      }),
    });
    return response.data;
  }
}
