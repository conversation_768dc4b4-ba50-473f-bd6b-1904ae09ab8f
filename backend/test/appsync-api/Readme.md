aws cognito-idp initiate-auth --auth-flow USER_PASSWORD_AUTH --client-id 7hjb6b0dln6nuce8u4cu2dl4vb --auth-parameters USERNAME=<USERNAME>,PASSWORD=<PASSWORD> --region eu-central-1

$USERNAME = $env:USERNAME
$PASSWORD = $env:PASSWORD
$authResponse = aws cognito-idp initiate-auth --auth-flow USER_PASSWORD_AUTH --client-id 69j26hfquq6lncd6esj2nagn2o --auth-parameters USERNAME=$USERNAME,PASSWORD=$PASSWORD --region eu-central-1
$authToken = ($authResponse | ConvertFrom-Json).AuthenticationResult.IdToken
$env:TEST_AUTH_TOKEN = $authToken
$env:TEST_API_ENDPOINT = "https://eipkzxeqmja4jdrs56thxa4jtq.appsync-api.eu-central-1.amazonaws.com/graphql"

npm test createProject
