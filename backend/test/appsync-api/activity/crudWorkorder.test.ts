import { GraphQLClient } from '../graphql-client'; // Reuse your GraphQL client

const API_ENDPOINT = process.env.TEST_API_ENDPOINT!;
const AUTH_TOKEN = process.env.TEST_AUTH_TOKEN!;

describe('CRUD Operations for WorkOrders', () => {
  let workOrderId: string;

  // Instantiate the GraphQLClient
  const client = new GraphQLClient(API_ENDPOINT, AUTH_TOKEN);

  // GraphQL Queries and Mutations
  const ADD_WORK_ORDER = `
    mutation AddWorkOrder($input: AddWorkOrderInput!) {
      addWorkOrder(input: $input) {
        id
        name
        description
        scope {
          id
        }
        dependencies {
          id
        }
        stopPointNo
        stopPointDescription
      }
    }
  `;

  const GET_WORK_ORDER = `
    query GetWorkOrder($id: ID!) {
      getWorkOrder(id: $id) {
        id
        name
        description
        scope {
          id
        }
        dependencies {
          id
        }
        stopPointNo
        stopPointDescription
      }
    }
  `;

  const UPDATE_WORK_ORDER = `
    mutation UpdateWorkOrder($id: ID!, $input: UpdateWorkOrderInput!) {
      updateWorkOrder(id: $id, input: $input) {
        id
        name
        description
        stopPointNo
        stopPointDescription
      }
    }
  `;

  const DELETE_WORK_ORDER = `
    mutation DeleteWorkOrder($id: ID!) {
      deleteWorkOrder(id: $id)
    }
  `;

  // Test Cases
  test('should add a new work order', async () => {
    const input = {
      name: 'Test Work Order',
      description: 'This is a test work order.',
      scopeId: 'scope-123', // Replace with a valid scope ID
      dependencies: [],
      stopPointNo: 'SP-01',
      stopPointDescription: 'Test Stop Point',
    };

    const response = await client.query(ADD_WORK_ORDER, { input });

    expect(response).toHaveProperty('data.addWorkOrder');
    const addedWorkOrder = response.data.addWorkOrder;
    expect(addedWorkOrder).toMatchObject({
      name: 'Test Work Order',
      description: 'This is a test work order.',
      stopPointNo: 'SP-01',
      stopPointDescription: 'Test Stop Point',
    });

    workOrderId = addedWorkOrder.id;
  });

  test('should fetch the created work order', async () => {
    const response = await client.query(GET_WORK_ORDER, { id: workOrderId });

    expect(response).toHaveProperty('data.getWorkOrder');
    expect(response.data.getWorkOrder).toMatchObject({
      id: workOrderId,
      name: 'Test Work Order',
      description: 'This is a test work order.',
      stopPointNo: 'SP-01',
      stopPointDescription: 'Test Stop Point',
    });
  });

  test('should update the work order', async () => {
    const input = {
      name: 'Updated Work Order',
      description: 'This work order has been updated.',
      stopPointNo: 'SP-02',
      stopPointDescription: 'Updated Stop Point',
    };

    const response = await client.query(UPDATE_WORK_ORDER, { id: workOrderId, input });

    expect(response).toHaveProperty('data.updateWorkOrder');
    expect(response.data.updateWorkOrder).toMatchObject({
      id: workOrderId,
      ...input,
    });
  });

  test('should delete the work order', async () => {
    const response = await client.query(DELETE_WORK_ORDER, { id: workOrderId });

    expect(response).toHaveProperty('data.deleteWorkOrder');
    expect(response.data.deleteWorkOrder).toBe(`WorkOrder with ID ${workOrderId} deleted successfully.`);
  });

  // Uncomment this test if your backend throws an error for querying deleted records
  // test('should confirm work order is deleted', async () => {
  //   await expect(client.query(GET_WORK_ORDER, { id: workOrderId })).rejects.toThrow(/not found/i);
  // });
});
