import { readFileSync } from 'fs';
import { GraphQLClient } from '../graphql-client'; // Reuse your GraphQL client

const API_ENDPOINT = process.env.TEST_API_ENDPOINT!;
const AUTH_TOKEN = process.env.TEST_AUTH_TOKEN!;

describe('CRUD Operations for Activities', () => {
  let activityId: string;

  // Instantiate the GraphQLClient
  const client = new GraphQLClient(API_ENDPOINT, AUTH_TOKEN);

  // Read inputs from a file
  const testInputs = JSON.parse(readFileSync('test/appsync-api/activity/testInputs.json', 'utf-8'));

  const ADD_ACTIVITY = `
    mutation AddActivity($input: AddActivityInput!) {
      addActivity(input: $input) {
        id
        name
        description
        progress
        status
      }
    }
  `;

  const GET_ACTIVITY = `
    query GetActivity($id: ID!) {
      getActivity(id: $id) {
        id
        name
        description
        progress
        status
      }
    }
  `;

  const UPDATE_ACTIVITY = `
    mutation UpdateActivity($id: ID!, $input: UpdateActivityInput!) {
      updateActivity(id: $id, input: $input) {
        id
        name
        description
        progress
        status
      }
    }
  `;

  const DELETE_ACTIVITY = `
    mutation DeleteActivity($id: ID!) {
      deleteActivity(id: $id)
    }
  `;

  test('should add a new activity', async () => {
    const response = await client.query(ADD_ACTIVITY, { input: testInputs.addActivityInput });
    console.log(response)
    expect(response).toHaveProperty('data.addActivity');
    const addedActivity = response.data.addActivity;
    expect(addedActivity).toMatchObject(testInputs.addActivityInput);

    activityId = addedActivity.id;
  });

  test('should fetch the created activity', async () => {
    const response = await client.query(GET_ACTIVITY, { id: activityId });

    expect(response).toHaveProperty('data.getActivity');
    expect(response.data.getActivity).toMatchObject({
      id: activityId,
      ...testInputs.addActivityInput,
    });
  });

  test('should update the activity', async () => {
    const response = await client.query(UPDATE_ACTIVITY, { id: activityId, input: testInputs.updateActivityInput });

    expect(response).toHaveProperty('data.updateActivity');
    expect(response.data.updateActivity).toMatchObject({
      id: activityId,
      ...testInputs.updateActivityInput,
    });
  });

  test('should delete the activity', async () => {
    const response = await client.query(DELETE_ACTIVITY, { id: activityId });

    expect(response).toHaveProperty('data.deleteActivity');
    expect(response.data.deleteActivity).toBe(`ACTIVITY with ID ${activityId} deleted successfully.`);
  });
});
