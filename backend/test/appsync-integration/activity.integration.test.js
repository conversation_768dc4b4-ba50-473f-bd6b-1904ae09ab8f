require('dotenv').config();
const { GraphQLClient } = require('graphql-request');

// Create a GraphQL client
const client = new GraphQLClient(process.env.APPSYNC_API_URL, {
  headers: {
    'x-api-key': process.env.APPSYNC_API_KEY,
  },
});

// Define GraphQL queries and mutations
const ADD_ACTIVITY = `
  mutation AddActivity($input: AddActivityInput!) {
    addActivity(input: $input) {
      id
      name
      description
      progress
      status
    }
  }
`;

const GET_ACTIVITY = `
  query GetActivity($id: ID!) {
    getActivity(id: $id) {
      id
      name
      description
      progress
      status
    }
  }
`;

const UPDATE_ACTIVITY = `
  mutation UpdateActivity($id: ID!, $input: UpdateActivityInput!) {
    updateActivity(id: $id, input: $input) {
      id
      name
      description
      progress
      status
    }
  }
`;

const DELETE_ACTIVITY = `
  mutation DeleteActivity($id: ID!) {
    deleteActivity(id: $id)
  }
`;

// Sample data
const activityId = `activity-${Date.now()}`;
const sampleActivity = {
  id: activityId,
  name: "Test Activity",
  description: "This is a test activity.",
  progress: 0,
  status: "NOT_STARTED",
};

describe('AppSync API Integration Test', () => {
  test('should create, read, update, and delete an activity', async () => {
    // 1. Add Activity
    const addResult = await client.request(ADD_ACTIVITY, { input: sampleActivity });
    expect(addResult.addActivity).toMatchObject(sampleActivity);

    // 2. Get Activity
    const getResult = await client.request(GET_ACTIVITY, { id: activityId });
    expect(getResult.getActivity).toMatchObject(sampleActivity);

    // 3. Update Activity
    const updatedData = { name: "Updated Activity", progress: 50, status: "IN_PROGRESS" };
    const updateResult = await client.request(UPDATE_ACTIVITY, { id: activityId, input: updatedData });
    expect(updateResult.updateActivity).toMatchObject({
      id: activityId,
      ...updatedData,
    });

    // 4. Delete Activity
    const deleteResult = await client.request(DELETE_ACTIVITY, { id: activityId });
    expect(deleteResult.deleteActivity).toBe(`Activity with ID ${activityId} deleted successfully.`);

    // 5. Verify Deletion
    const verifyDeletion = await client.request(GET_ACTIVITY, { id: activityId }).catch((err) => err);
    expect(verifyDeletion.response.errors[0].message).toMatch(/not found/i);
  });
});
