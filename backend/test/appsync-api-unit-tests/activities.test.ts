// @ts-ignore
import { getActivitiesForWorkPackage, getActivity, listActivities, addActivity, updateActivity, deleteActivity } from '../../lambda/graphql-api/activities.js';

let dynamodb: {
    query: jest.Mock;
    get: jest.Mock;
    scan: jest.Mock;
    put: jest.Mock;
    update: jest.Mock;
    delete: jest.Mock;
};

beforeEach(() => {
    dynamodb = {
        query: jest.fn(),
        get: jest.fn(),
        scan: jest.fn(),
        put: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
    };
});

describe('AppSync Lambda Functions', () => {
    const tableName = 'TestTable';

    describe('getActivitiesForWorkPackage', () => {
        it('should return all activities for admin users', async () => {
            const mockItems = [{ id: '1' }, { id: '2' }];
            dynamodb.query.mockReturnValueOnce({ promise: () => Promise.resolve({ Items: mockItems }) });

            const result = await getActivitiesForWorkPackage(dynamodb, tableName, ['admin'], 'wp-1');

            expect(result).toEqual(mockItems);
            expect(dynamodb.query).toHaveBeenCalledWith({
                TableName: tableName,
                IndexName: 'workPackageId-index',
                KeyConditionExpression: 'workPackageId = :workPackageId',
                ExpressionAttributeValues: { ':workPackageId': 'wp-1' },
            });
        });

        it('should filter activities based on allowedGroups for non-admin users', async () => {
            const mockItems = [
                { id: '1', allowedGroups: ['user'] },
                { id: '2', allowedGroups: ['admin'] },
            ];
            dynamodb.query.mockReturnValueOnce({ promise: () => Promise.resolve({ Items: mockItems }) });

            const result = await getActivitiesForWorkPackage(dynamodb, tableName, ['user'], 'wp-1');

            expect(result).toEqual([{ id: '1', allowedGroups: ['user'] }]);
        });
    });

    describe('getActivity', () => {
        it('should return the activity for admin users', async () => {
            const mockItem = { id: '1', allowedGroups: ['user'] };
            dynamodb.get.mockReturnValueOnce({ promise: () => Promise.resolve({ Item: mockItem }) });

            const result = await getActivity(dynamodb, tableName, ['admin'], '1');

            expect(result).toEqual(mockItem);
        });

        it('should return null if the activity is not found', async () => {
            dynamodb.get.mockReturnValueOnce({ promise: () => Promise.resolve({}) });

            const result = await getActivity(dynamodb, tableName, ['user'], '1');

            expect(result).toBeNull();
        });
    });

    describe('listActivities', () => {
        it('should return all activities', async () => {
            const mockItems = [{ id: '1' }, { id: '2' }];
            dynamodb.scan.mockReturnValueOnce({ promise: () => Promise.resolve({ Items: mockItems }) });

            const result = await listActivities(dynamodb, tableName);

            expect(result).toEqual(mockItems);
        });
    });

    describe('addActivity', () => {
        it('should add a new activity', async () => {
            const input = { name: 'Test Activity' };
            dynamodb.put.mockReturnValueOnce({ promise: () => Promise.resolve() });

            const result = await addActivity(dynamodb, tableName, input);

            expect(result).toHaveProperty('PK');
            expect(result).toHaveProperty('SK');
            expect(result).toHaveProperty('id');
            expect(dynamodb.put).toHaveBeenCalled();
        });
    });

    describe('updateActivity', () => {
        it('should update an activity and add a log entry', async () => {
            const id = '1';
            const input = { name: 'Updated Activity' };
            dynamodb.update.mockReturnValueOnce({ promise: () => Promise.resolve({ Attributes: { ...input } }) });

            const result = await updateActivity(dynamodb, tableName, id, input);

            expect(result).toEqual(input);
            expect(dynamodb.update).toHaveBeenCalled();
        });
    });

    describe('deleteActivity', () => {
        it('should delete an activity', async () => {
            const id = '1';
            dynamodb.delete.mockReturnValueOnce({ promise: () => Promise.resolve() });

            const result = await deleteActivity(dynamodb, tableName, id);

            expect(result).toEqual(`Activity with ID ${id} deleted successfully.`);
            expect(dynamodb.delete).toHaveBeenCalledWith({
                TableName: tableName,
                Key: { PK: `ACTIVITY#${id}`, SK: `METADATA#${id}` },
            });
        });
    });
});
