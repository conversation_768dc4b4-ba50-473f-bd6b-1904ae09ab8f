// @ts-ignore
import { getWorkPackage, getWorkPackages, addWorkPackage, updateWorkPackage, deleteWorkPackage } from '../../lambda/graphql-api/workPackages.js';
import { v4 as uuidv4 } from 'uuid';

let dynamodb: {
    get: jest.Mock;
    scan: jest.Mock;
    put: jest.Mock;
    update: jest.Mock;
    delete: jest.Mock;
};

beforeEach(() => {
    dynamodb = {
        get: jest.fn(),
        scan: jest.fn(),
        put: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
    };
});

describe('WorkPackage Lambda Functions', () => {
    const tableName = 'TestTable';

    describe('getWorkPackage', () => {
        it('should return a work package if it exists', async () => {
            const mockItem = { id: '1', name: 'Test WorkPackage' };
            dynamodb.get.mockReturnValueOnce({ promise: () => Promise.resolve({ Item: mockItem }) });

            const result = await getWorkPackage(dynamodb, tableName, '1');

            expect(result).toEqual(mockItem);
            expect(dynamodb.get).toHaveBeenCalledWith({
                TableName: tableName,
                Key: {
                    PK: 'WORKPACKAGE#1',
                    SK: 'METADATA#1',
                },
            });
        });

        it('should return null if the work package does not exist', async () => {
            dynamodb.get.mockReturnValueOnce({ promise: () => Promise.resolve({}) });

            const result = await getWorkPackage(dynamodb, tableName, '1');

            expect(result).toBeNull();
        });
    });

    describe('getWorkPackages', () => {
        it('should return all work packages for a given project', async () => {
            const mockItems = [
                { id: '1', projectId: 'p1' },
                { id: '2', projectId: 'p1' },
            ];
            dynamodb.scan.mockReturnValueOnce({ promise: () => Promise.resolve({ Items: mockItems }) });

            const result = await getWorkPackages(dynamodb, tableName, 'p1');

            expect(result).toEqual(mockItems);
            expect(dynamodb.scan).toHaveBeenCalledWith({
                TableName: tableName,
                FilterExpression: 'begins_with(PK, :pk) AND projectId = :projectId',
                ExpressionAttributeValues: {
                    ':pk': 'WORKPACKAGE#',
                    ':projectId': 'p1',
                },
            });
        });
    });

    // describe('addWorkPackage', () => {
    //     it('should add a new work package and return it', async () => {
    //         const input = { name: 'New WorkPackage', projectId: 'p1' };
    //         const uuid = '1234';

    //         jest.spyOn(uuidv4, 'mockImplementation').mockReturnValueOnce(uuid);
    //         dynamodb.put.mockReturnValueOnce({ promise: () => Promise.resolve() });

    //         const result = await addWorkPackage(dynamodb, tableName, input);

    //         expect(result).toEqual({
    //             ...input,
    //             PK: `WORKPACKAGE#${uuid}`,
    //             SK: `METADATA#${uuid}`,
    //             id: uuid,
    //         });
    //         expect(dynamodb.put).toHaveBeenCalled();
    //     });
    // });

    describe('updateWorkPackage', () => {
        it('should update a work package and return updated attributes', async () => {
            const id = '1';
            const input = { name: 'Updated WorkPackage' };
            const updatedAttributes = { id, name: 'Updated WorkPackage' };

            dynamodb.update.mockReturnValueOnce({ promise: () => Promise.resolve({ Attributes: updatedAttributes }) });

            const result = await updateWorkPackage(dynamodb, tableName, id, input);

            expect(result).toEqual(updatedAttributes);
            expect(dynamodb.update).toHaveBeenCalled();
        });
    });

    describe('deleteWorkPackage', () => {
        it('should delete a work package and return a success message', async () => {
            const id = '1';
            dynamodb.delete.mockReturnValueOnce({ promise: () => Promise.resolve() });

            const result = await deleteWorkPackage(dynamodb, tableName, id);

            expect(result).toEqual(`WorkPackage with ID ${id} deleted successfully.`);
            expect(dynamodb.delete).toHaveBeenCalledWith({
                TableName: tableName,
                Key: {
                    PK: `WORKPACKAGE#${id}`,
                    SK: `METADATA#${id}`,
                },
            });
        });
    });
});
